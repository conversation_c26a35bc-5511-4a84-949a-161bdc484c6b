"""
自动化 TimeMixer++ 训练系统
============================

基于 timemixer_plus_plus_example.py 创建自动化训练系统，
每次训练在新文件夹中创建对应的py文件，并将结果记录到CSV中。
"""

import os
import pandas as pd
import numpy as np
import shutil
from datetime import datetime
from typing import Dict, Any, List
import warnings

warnings.filterwarnings('ignore')

class TimeMixerTrainingSystem:
    """TimeMixer++ 自动化训练系统"""
    
    def __init__(self, base_dir="C:/Users/<USER>/Desktop/timemix"):
        self.base_dir = base_dir
        self.template_file = os.path.join(base_dir, "timemixer_plus_plus_example.py")
        self.results_csv = os.path.join(base_dir, "timemixer_evaluation_results.csv")
        self.training_counter = self._get_next_training_number()
        
        # 确保结果CSV文件存在
        self._initialize_results_csv()
    
    def _get_next_training_number(self) -> int:
        """获取下一个训练编号"""
        if os.path.exists(self.results_csv):
            try:
                df = pd.read_csv(self.results_csv)
                if not df.empty and 'Training_ID' in df.columns:
                    # 提取数字部分并找到最大值
                    max_id = 0
                    for training_id in df['Training_ID']:
                        if isinstance(training_id, str) and training_id.startswith('training_'):
                            try:
                                num = int(training_id.split('_')[1].split('_')[0])
                                max_id = max(max_id, num)
                            except:
                                continue
                    return max_id + 1
            except:
                pass
        return 1
    
    def _initialize_results_csv(self):
        """初始化结果CSV文件"""
        if not os.path.exists(self.results_csv):
            # 创建CSV文件头
            df = pd.DataFrame(columns=[
                'Training_ID', 'Timestamp', 'Parameters', 
                'MAE', 'MAE_Description',
                'RMSE', 'RMSE_Description', 
                'NSE', 'NSE_Description',
                'R2', 'R2_Description'
            ])
            df.to_csv(self.results_csv, index=False, encoding='utf-8-sig')
    
    def create_training_script(self, parameters: Dict[str, Any]) -> str:
        """创建训练脚本"""
        # 创建训练文件夹
        param_str = "_".join([f"{k}{v}" for k, v in parameters.items()])
        training_id = f"training_{self.training_counter}_{param_str}"
        training_dir = os.path.join(self.base_dir, training_id)
        
        if not os.path.exists(training_dir):
            os.makedirs(training_dir)
        
        # 读取模板文件
        with open(self.template_file, 'r', encoding='utf-8') as f:
            template_content = f.read()
        
        # 修改参数
        modified_content = self._modify_template(template_content, parameters, training_id)
        
        # 保存新的训练脚本
        script_path = os.path.join(training_dir, f"{training_id}.py")
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(modified_content)
        
        # 复制数据文件到训练文件夹
        data_file = os.path.join(self.base_dir, "1964-2017dailyRunoff.csv")
        if os.path.exists(data_file):
            shutil.copy2(data_file, training_dir)
        
        return script_path, training_id
    
    def _modify_template(self, template: str, parameters: Dict[str, Any], training_id: str) -> str:
        """修改模板文件中的参数"""
        lines = template.split('\n')
        modified_lines = []
        
        for line in lines:
            # 修改模型参数
            if 'n_layers=' in line and 'n_layers' in parameters:
                line = f"    n_layers={parameters['n_layers']},"
            elif 'top_k=' in line and 'top_k' in parameters:
                line = f"    top_k={parameters['top_k']},"
            elif 'd_model=' in line and 'd_model' in parameters:
                line = f"    d_model={parameters['d_model']},"
            elif 'd_ffn=' in line and 'd_ffn' in parameters:
                line = f"    d_ffn={parameters['d_ffn']},"
            elif 'moving_avg=' in line and 'moving_avg' in parameters:
                line = f"    moving_avg={parameters['moving_avg']},"
            elif 'dropout=' in line and 'dropout' in parameters:
                line = f"    dropout={parameters['dropout']},"
            elif 'epochs=' in line and 'epochs' in parameters:
                line = f"    epochs={parameters['epochs']},"
            elif 'lr=' in line and 'learning_rate' in parameters:
                line = f"    optimizer=Adam(lr={parameters['learning_rate']}),"
            elif 'saving_path=' in line:
                line = f'    saving_path="{training_id}_results",'
            
            modified_lines.append(line)
        
        # 添加评估指标计算
        evaluation_code = self._get_evaluation_code(training_id)
        
        # 在文件末尾添加评估代码
        modified_content = '\n'.join(modified_lines)
        modified_content += '\n\n' + evaluation_code
        
        return modified_content
    
    def _get_evaluation_code(self, training_id: str) -> str:
        """获取评估指标计算代码"""
        return f'''
# 添加详细的评估指标计算
import sklearn.metrics as metrics

def calculate_evaluation_metrics(y_true, y_pred):
    """计算评估指标"""
    # 展平数组以便计算
    y_true_flat = y_true.flatten()
    y_pred_flat = y_pred.flatten()
    
    # MAE - 平均绝对误差
    mae = np.mean(np.abs(y_true_flat - y_pred_flat))
    
    # RMSE - 均方根误差
    rmse = np.sqrt(np.mean((y_true_flat - y_pred_flat) ** 2))
    
    # NSE - Nash-Sutcliffe效率系数
    mean_observed = np.mean(y_true_flat)
    nse = 1 - (np.sum((y_true_flat - y_pred_flat) ** 2) / 
               np.sum((y_true_flat - mean_observed) ** 2))
    
    # R² - 决定系数
    r2 = metrics.r2_score(y_true_flat, y_pred_flat)
    
    return mae, rmse, nse, r2

# 计算评估指标
mae, rmse, nse, r2 = calculate_evaluation_metrics(
    true_values_for_testing, timemixer_prediction
)

print(f"\\n=== 详细评估结果 ===")
print(f"MAE (平均绝对误差): {{mae:.6f}}")
print(f"RMSE (均方根误差): {{rmse:.6f}}")
print(f"NSE (Nash-Sutcliffe效率系数): {{nse:.6f}}")
print(f"R² (决定系数): {{r2:.6f}}")

# 保存结果到CSV
import csv
import os
from datetime import datetime

results_csv = "C:/Users/<USER>/Desktop/timemix/timemixer_evaluation_results.csv"
training_id = "{training_id}"
timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

# 参数字符串
parameters_str = "layers={{}}|d_model={{}}|epochs={{}}".format(
    {{}}, {{}}, {{}}  # 这些会在运行时被实际参数替换
)

# 写入结果
new_row = [
    training_id,
    timestamp,
    parameters_str,
    mae, "平均绝对误差",
    rmse, "均方根误差", 
    nse, "Nash-Sutcliffe效率系数",
    r2, "决定系数"
]

# 检查文件是否存在，如果不存在则创建
if not os.path.exists(results_csv):
    with open(results_csv, 'w', newline='', encoding='utf-8-sig') as f:
        writer = csv.writer(f)
        writer.writerow([
            'Training_ID', 'Timestamp', 'Parameters',
            'MAE', 'MAE_Description',
            'RMSE', 'RMSE_Description',
            'NSE', 'NSE_Description', 
            'R2', 'R2_Description'
        ])

# 追加新结果
with open(results_csv, 'a', newline='', encoding='utf-8-sig') as f:
    writer = csv.writer(f)
    writer.writerow(new_row)

print(f"\\n结果已保存到: {{results_csv}}")
print(f"训练ID: {{training_id}}")
'''
    
    def run_training(self, parameters: Dict[str, Any]) -> Dict[str, float]:
        """运行训练并返回结果"""
        print(f"\\n{'='*60}")
        print(f"开始训练 #{self.training_counter}")
        print(f"参数: {parameters}")
        print(f"{'='*60}")
        
        # 创建训练脚本
        script_path, training_id = self.create_training_script(parameters)
        print(f"训练脚本已创建: {script_path}")
        
        # 运行训练脚本
        try:
            import subprocess
            import sys
            
            # 切换到训练目录
            training_dir = os.path.dirname(script_path)
            
            # 运行训练脚本
            result = subprocess.run([
                sys.executable, os.path.basename(script_path)
            ], cwd=training_dir, capture_output=True, text=True, timeout=1800)  # 30分钟超时
            
            if result.returncode == 0:
                print(f"✓ 训练 {training_id} 完成成功")
                self.training_counter += 1
                return {"status": "success", "training_id": training_id}
            else:
                print(f"✗ 训练 {training_id} 失败")
                print(f"错误输出: {result.stderr}")
                return {"status": "failed", "error": result.stderr}
                
        except subprocess.TimeoutExpired:
            print(f"✗ 训练 {training_id} 超时")
            return {"status": "timeout"}
        except Exception as e:
            print(f"✗ 训练 {training_id} 出现异常: {e}")
            return {"status": "error", "error": str(e)}
    
    def batch_training(self, parameter_sets: List[Dict[str, Any]]):
        """批量训练"""
        print(f"开始批量训练，共 {len(parameter_sets)} 个配置")
        
        results = []
        for i, params in enumerate(parameter_sets, 1):
            print(f"\\n进度: {i}/{len(parameter_sets)}")
            result = self.run_training(params)
            results.append(result)
            
            # 短暂休息
            import time
            time.sleep(2)
        
        print(f"\\n批量训练完成！")
        print(f"成功: {sum(1 for r in results if r['status'] == 'success')}")
        print(f"失败: {sum(1 for r in results if r['status'] != 'success')}")
        
        return results

def main():
    """主函数 - 演示如何使用训练系统"""
    system = TimeMixerTrainingSystem()
    
    # 定义不同的参数配置进行实验
    parameter_sets = [
        # 实验1: 基础配置
        {
            "n_layers": 1,
            "d_model": 32,
            "d_ffn": 64,
            "epochs": 5,
            "learning_rate": 1e-3,
            "dropout": 0.1
        },
        # 实验2: 增加层数
        {
            "n_layers": 2,
            "d_model": 32,
            "d_ffn": 64,
            "epochs": 5,
            "learning_rate": 1e-3,
            "dropout": 0.1
        },
        # 实验3: 增加模型维度
        {
            "n_layers": 2,
            "d_model": 64,
            "d_ffn": 128,
            "epochs": 5,
            "learning_rate": 1e-3,
            "dropout": 0.1
        },
        # 实验4: 调整学习率
        {
            "n_layers": 2,
            "d_model": 64,
            "d_ffn": 128,
            "epochs": 5,
            "learning_rate": 5e-4,
            "dropout": 0.1
        }
    ]
    
    # 运行批量训练
    results = system.batch_training(parameter_sets)
    
    print(f"\\n所有训练结果已保存到: {system.results_csv}")

if __name__ == "__main__":
    main()
