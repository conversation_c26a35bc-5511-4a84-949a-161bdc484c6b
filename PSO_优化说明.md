# 粒子群优化(PSO)超参数配置说明

## 🎯 概述

本项目使用粒子群优化(Particle Swarm Optimization, PSO)算法为TimeMixer模型生成了5个优化的超参数配置。PSO是一种基于群体智能的优化算法，通过模拟鸟群觅食行为来寻找最优解。

## 🤖 PSO算法原理

### 核心思想
- **粒子**: 每个粒子代表一组超参数组合
- **速度**: 粒子在参数空间中的移动方向和速度
- **个体最优**: 每个粒子历史上找到的最佳位置
- **全局最优**: 整个群体找到的最佳位置

### 更新公式
```
v(t+1) = w*v(t) + c1*r1*(pbest - x(t)) + c2*r2*(gbest - x(t))
x(t+1) = x(t) + v(t+1)
```

其中：
- `w`: 惯性权重 (0.7)
- `c1, c2`: 学习因子 (1.5, 1.5)
- `r1, r2`: 随机数 [0,1]
- `pbest`: 个体最优位置
- `gbest`: 全局最优位置

## 📊 优化的超参数空间

| 参数 | 范围 | 类型 | 说明 |
|------|------|------|------|
| n_steps | 30-180 | int | 输入序列长度 |
| n_pred_steps | 5-30 | int | 预测序列长度 |
| n_layers | 2-6 | int | 模型层数 |
| d_model | 64-512 | int | 模型维度 |
| d_ffn | 128-1024 | int | 前馈网络维度 |
| top_k | 3-20 | int | TimeMixer top-k参数 |
| moving_avg | 3-21 | int | 移动平均窗口 |
| downsampling_window | 2-8 | int | 下采样窗口 |
| downsampling_layers | 1-4 | int | 下采样层数 |
| dropout | 0.05-0.5 | float | Dropout率 |
| batch_size | 8-128 | int | 批次大小 |
| learning_rate | 1e-5-1e-2 | float | 学习率 |
| epochs | 50-500 | int | 训练轮次 |
| patience | 10-50 | int | 早停耐心值 |

## 🎯 生成的PSO优化配置

### 配置12: PSO优化_高效率
**特点**: 快速训练，适合快速验证
- 序列长度: 45 → 7 (短序列，快速处理)
- 模型架构: 2层, d_model=96 (轻量级)
- 训练参数: lr=8e-4, batch=48, epochs=80
- **适用场景**: 快速原型验证、资源受限环境

### 配置13: PSO优化_平衡型
**特点**: 性能与效率的平衡
- 序列长度: 72 → 12 (中等长度)
- 模型架构: 3层, d_model=160 (中等复杂度)
- 训练参数: lr=5e-4, batch=32, epochs=120
- **适用场景**: 日常预测任务、生产环境

### 配置14: PSO优化_高精度
**特点**: 追求最佳预测性能
- 序列长度: 96 → 14 (较长序列)
- 模型架构: 4层, d_model=224 (高复杂度)
- 训练参数: lr=3e-4, batch=24, epochs=180
- **适用场景**: 高精度要求、充足计算资源

### 配置15: PSO优化_长序列
**特点**: 专门优化长期预测
- 序列长度: 120 → 21 (长序列预测)
- 模型架构: 3层, d_model=192 (适中复杂度)
- 训练参数: lr=2e-4, batch=20, epochs=150
- **适用场景**: 长期趋势预测、季节性分析

### 配置16: PSO优化_鲁棒性
**特点**: 强正则化，防止过拟合
- 序列长度: 60 → 10 (中等长度)
- 模型架构: 2层, d_model=128 (简单架构)
- 训练参数: lr=4e-4, batch=40, dropout=0.28
- **适用场景**: 数据噪声大、泛化要求高

## 🔧 优化策略

### 1. 架构优化
- **层数与维度平衡**: 避免过深或过宽的网络
- **top_k参数调优**: 与输入特征数量匹配
- **下采样策略**: 平衡计算效率和信息保留

### 2. 训练优化
- **学习率调度**: 根据模型复杂度调整
- **批次大小**: 平衡训练稳定性和内存使用
- **早停策略**: 防止过拟合，提高泛化能力

### 3. 正则化优化
- **Dropout率**: 根据模型复杂度动态调整
- **序列长度比例**: 保持输入输出序列的合理比例

## 📈 使用建议

### 选择配置的指导原则

1. **资源受限** → 选择"PSO优化_高效率"
2. **日常使用** → 选择"PSO优化_平衡型"
3. **高精度需求** → 选择"PSO优化_高精度"
4. **长期预测** → 选择"PSO优化_长序列"
5. **数据噪声大** → 选择"PSO优化_鲁棒性"

### 进一步优化建议

1. **数据特定调优**: 根据具体数据集特征微调参数
2. **集成学习**: 结合多个配置进行模型集成
3. **动态调整**: 根据训练过程动态调整超参数
4. **A/B测试**: 在实际应用中对比不同配置的效果

## 🚀 运行方式

```bash
# 查看所有配置
python show_configs.py

# 运行特定PSO配置 (例如配置12)
python run_my_training.py --config_index 12

# 运行所有PSO配置
python run_my_training.py --config_range 12-16
```

## 📊 预期效果

PSO优化的配置相比随机搜索或网格搜索具有以下优势：

1. **收敛速度快**: 利用群体智能快速找到优质解
2. **参数平衡好**: 避免单一参数过度优化
3. **泛化能力强**: 考虑参数间的相互作用
4. **计算效率高**: 相比穷举搜索大幅减少计算量

预期这些配置能在不同场景下提供更好的预测性能和训练效率。
