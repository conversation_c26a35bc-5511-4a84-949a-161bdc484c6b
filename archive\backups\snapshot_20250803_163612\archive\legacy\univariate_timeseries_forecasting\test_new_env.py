"""
Test New PyPOTS Environment
===========================

Quick test to verify the new pypots environment is working.
"""

print("🧪 Testing New PyPOTS Environment")
print("=" * 40)

# Test imports
try:
    print("1. Testing PyPOTS import...")
    import pypots
    print(f"   ✅ PyPOTS version: {pypots.__version__}")
    
    print("2. Testing TimeMixer import...")
    from pypots.forecasting import TimeMixer
    print("   ✅ TimeMixer imported")
    
    print("3. Testing basic packages...")
    import pandas as pd
    import numpy as np
    import torch
    print(f"   ✅ PyTorch version: {torch.__version__}")
    
    print("4. Testing data loading...")
    file_path = r'C:\Users\<USER>\Desktop\timemix\1964-2017dailyRunoff.csv'
    data = pd.read_excel(file_path)
    print(f"   ✅ Data loaded: {data.shape}")
    
    print("\n🎉 All tests passed!")
    print("The pypots environment is ready for TimeMixer++!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
