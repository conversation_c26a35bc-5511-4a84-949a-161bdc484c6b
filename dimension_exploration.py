"""
维度探索训练
============

基于配置17的成功，探索更高和更低维度的配置
目标：找到最优的模型维度配置
"""

import os
from datetime import datetime

def create_dimension_configs():
    """创建不同维度的配置"""
    print("🔧 创建维度探索配置")
    print("="*40)
    
    # 基于配置17的成功参数
    base_config = {
        'n_steps': 90,
        'n_pred_steps': 5,
        'n_layers': 3,
        'd_model': 256,                   # 基线维度
        'd_ffn': 512,                     # 基线FFN
        'top_k': 10,
        'moving_avg': 7,
        'downsampling_window': 3,
        'downsampling_layers': 2,
        'use_norm': True,
        'batch_size': 32,
        'device': 'cuda',
        'num_workers': 0,
        'dropout': 0.2,
        'learning_rate': 3e-4,
        'epochs': 200,
        'patience': 30
    }
    
    # 维度探索配置
    dimension_configs = []
    
    # 1. 超低维度配置 (d_model=128)
    config_low = base_config.copy()
    config_low.update({
        'name': '维度探索_超低维度_128',
        'd_model': 128,                   # 50%减少
        'd_ffn': 256,                     # 50%减少
        'top_k': 8,                       # 相应减少
        'learning_rate': 4e-4,            # 稍微提高学习率
        'epochs': 250                     # 增加训练轮次补偿
    })
    dimension_configs.append(config_low)
    
    # 2. 低维度配置 (d_model=192)
    config_low_mid = base_config.copy()
    config_low_mid.update({
        'name': '维度探索_低维度_192',
        'd_model': 192,                   # 25%减少
        'd_ffn': 384,                     # 25%减少
        'top_k': 9,                       # 相应减少
        'learning_rate': 3.5e-4,          # 稍微提高学习率
        'epochs': 220                     # 稍微增加训练轮次
    })
    dimension_configs.append(config_low_mid)
    
    # 3. 基线配置 (d_model=256) - 作为对照
    config_baseline = base_config.copy()
    config_baseline.update({
        'name': '维度探索_基线_256',
        # 保持所有基线参数不变
    })
    dimension_configs.append(config_baseline)
    
    # 4. 高维度配置 (d_model=384)
    config_high = base_config.copy()
    config_high.update({
        'name': '维度探索_高维度_384',
        'd_model': 384,                   # 50%增加
        'd_ffn': 768,                     # 50%增加
        'top_k': 12,                      # 相应增加
        'dropout': 0.25,                  # 增加正则化
        'learning_rate': 2.5e-4,          # 降低学习率
        'batch_size': 24,                 # 减小batch size
        'epochs': 180                     # 可能更快收敛
    })
    dimension_configs.append(config_high)
    
    # 5. 超高维度配置 (d_model=512)
    config_ultra_high = base_config.copy()
    config_ultra_high.update({
        'name': '维度探索_超高维度_512',
        'd_model': 512,                   # 100%增加
        'd_ffn': 1024,                    # 100%增加
        'top_k': 15,                      # 相应增加
        'dropout': 0.3,                   # 大幅增加正则化
        'learning_rate': 2e-4,            # 大幅降低学习率
        'batch_size': 16,                 # 大幅减小batch size
        'epochs': 150,                    # 可能很快收敛
        'patience': 25                    # 减少耐心值
    })
    dimension_configs.append(config_ultra_high)
    
    # 6. 极低维度配置 (d_model=96)
    config_ultra_low = base_config.copy()
    config_ultra_low.update({
        'name': '维度探索_极低维度_96',
        'd_model': 96,                    # 62.5%减少
        'd_ffn': 192,                     # 62.5%减少
        'top_k': 6,                       # 大幅减少
        'dropout': 0.15,                  # 减少正则化
        'learning_rate': 5e-4,            # 提高学习率
        'batch_size': 48,                 # 增加batch size
        'epochs': 300                     # 大幅增加训练轮次
    })
    dimension_configs.append(config_ultra_low)
    
    print(f"✅ 创建了 {len(dimension_configs)} 个维度探索配置:")
    for i, config in enumerate(dimension_configs, 1):
        d_model = config['d_model']
        d_ffn = config['d_ffn']
        params = d_model * d_ffn / 1000  # 估算参数量(K)
        print(f"  {i}. {config['name']}")
        print(f"     d_model={d_model}, d_ffn={d_ffn}, 参数量≈{params:.0f}K")
    
    return dimension_configs

def run_dimension_exploration():
    """运行维度探索训练"""
    print("\n🚀 开始维度探索训练")
    print("="*50)
    
    configs = create_dimension_configs()
    
    print("💡 探索策略:")
    print("  • 系统性测试不同的模型维度")
    print("  • 从极低维度(96)到超高维度(512)")
    print("  • 相应调整学习率、正则化等参数")
    print("  • 寻找最优的维度配置")
    
    results = []
    
    for i, config in enumerate(configs, 1):
        print(f"\n{'='*20} 维度测试 {i}/{len(configs)} {'='*20}")
        print(f"配置: {config['name']}")
        print(f"维度: d_model={config['d_model']}, d_ffn={config['d_ffn']}")
        
        success, r2_value = run_single_training(config)
        
        results.append({
            'config': config['name'],
            'd_model': config['d_model'],
            'd_ffn': config['d_ffn'],
            'success': success,
            'r2': r2_value
        })
        
        if success and r2_value and r2_value > 0.8:
            print(f"\n🎉 维度 {config['d_model']} 成功突破R²=0.8！")
            break
        elif r2_value and r2_value > 0.75:
            print(f"\n👍 维度 {config['d_model']} 表现良好: R²={r2_value:.4f}")
        
        print(f"{'='*60}")
    
    # 分析结果
    analyze_dimension_results(results)
    
    return results

def run_single_training(config):
    """运行单个训练配置"""
    try:
        import enhanced_compatible_training_runner
        
        print(f"\n🔥 开始训练: {config['name']}")
        print(f"🔧 关键参数:")
        print(f"  d_model: {config['d_model']}")
        print(f"  d_ffn: {config['d_ffn']}")
        print(f"  learning_rate: {config['learning_rate']:.2e}")
        print(f"  dropout: {config['dropout']}")
        print(f"  batch_size: {config['batch_size']}")
        print(f"  epochs: {config['epochs']}")
        
        start_time = datetime.now()
        
        result = enhanced_compatible_training_runner.run_compatible_training(config)
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        if result:
            print(f"\n✅ 训练完成! 用时: {duration:.1f}秒")
            
            # 读取结果
            try:
                import pandas as pd
                df = pd.read_csv('timemixer_evaluation_results.csv')
                latest_result = df.iloc[-1]
                r2_value = latest_result['R2']
                
                print(f"\n🎯 训练结果:")
                print(f"  R² Score: {r2_value:.4f}")
                print(f"  MAE: {latest_result['MAE']:.2f}")
                print(f"  RMSE: {latest_result['RMSE']:.2f}")
                
                # 与基线对比
                baseline_r2 = 0.7301
                improvement = r2_value - baseline_r2
                
                print(f"\n📈 与配置17对比:")
                print(f"  基线R²: {baseline_r2:.4f}")
                print(f"  当前R²: {r2_value:.4f}")
                print(f"  提升: {improvement:+.4f} ({improvement/baseline_r2*100:+.1f}%)")
                
                return True, r2_value
                
            except Exception as e:
                print(f"📊 结果读取失败: {e}")
                return False, None
        else:
            print(f"❌ 训练失败")
            return False, None
            
    except Exception as e:
        print(f"❌ 训练过程出错: {e}")
        return False, None

def analyze_dimension_results(results):
    """分析维度探索结果"""
    print(f"\n📊 维度探索结果分析")
    print("="*50)
    
    # 过滤有效结果
    valid_results = [r for r in results if r['r2'] is not None]
    
    if not valid_results:
        print("❌ 没有有效的训练结果")
        return
    
    # 按R²排序
    valid_results.sort(key=lambda x: x['r2'], reverse=True)
    
    print(f"🏆 维度性能排名:")
    for i, result in enumerate(valid_results, 1):
        status = "🎯" if i == 1 else f"#{i}"
        print(f"  {status} d_model={result['d_model']:3d}: R²={result['r2']:.4f}")
    
    # 分析最佳维度
    best_result = valid_results[0]
    baseline_r2 = 0.7301
    
    print(f"\n🎯 最佳维度分析:")
    print(f"  最佳配置: {best_result['config']}")
    print(f"  最佳维度: d_model={best_result['d_model']}")
    print(f"  最佳R²: {best_result['r2']:.4f}")
    
    if best_result['r2'] > 0.8:
        print(f"  🎉 成功突破R²=0.8!")
    elif best_result['r2'] > baseline_r2:
        improvement = best_result['r2'] - baseline_r2
        print(f"  📈 超越基线: +{improvement:.4f}")
    else:
        print(f"  📊 未超越基线配置17")
    
    # 维度趋势分析
    print(f"\n📈 维度趋势分析:")
    
    # 按维度排序分析
    by_dimension = sorted(valid_results, key=lambda x: x['d_model'])
    
    low_dim = [r for r in by_dimension if r['d_model'] <= 192]
    mid_dim = [r for r in by_dimension if 192 < r['d_model'] <= 384]
    high_dim = [r for r in by_dimension if r['d_model'] > 384]
    
    if low_dim:
        avg_low = sum(r['r2'] for r in low_dim) / len(low_dim)
        print(f"  低维度 (≤192): 平均R²={avg_low:.4f}")
    
    if mid_dim:
        avg_mid = sum(r['r2'] for r in mid_dim) / len(mid_dim)
        print(f"  中维度 (193-384): 平均R²={avg_mid:.4f}")
    
    if high_dim:
        avg_high = sum(r['r2'] for r in high_dim) / len(high_dim)
        print(f"  高维度 (>384): 平均R²={avg_high:.4f}")
    
    # 建议
    print(f"\n💡 维度选择建议:")
    if best_result['d_model'] <= 192:
        print(f"  • 低维度模型表现最佳，建议使用轻量级配置")
        print(f"  • 可能数据复杂度不需要大模型")
    elif best_result['d_model'] <= 384:
        print(f"  • 中等维度模型表现最佳，平衡了性能和效率")
        print(f"  • 当前配置17的维度选择是合理的")
    else:
        print(f"  • 高维度模型表现最佳，但要注意过拟合风险")
        print(f"  • 建议增加正则化和更多训练数据")

def main():
    """主函数"""
    print("🎯 TimeMixer维度探索程序")
    print("="*50)
    
    print("📊 探索目标:")
    print("  • 基于配置17 (R²=0.7301) 探索最优维度")
    print("  • 测试从极低维度(96)到超高维度(512)")
    print("  • 寻找性能与效率的最佳平衡点")
    print("  • 争取突破R²=0.8")
    
    # 运行维度探索
    results = run_dimension_exploration()
    
    print(f"\n🎉 维度探索完成!")
    print(f"📊 请查看 timemixer_evaluation_results.csv 了解详细结果")
    
    # 保存探索结果
    import json
    exploration_summary = {
        'exploration_type': 'dimension_exploration',
        'timestamp': datetime.now().isoformat(),
        'baseline_r2': 0.7301,
        'results': results,
        'total_configs': len(results),
        'successful_configs': len([r for r in results if r['success']])
    }
    
    with open('dimension_exploration_results.json', 'w', encoding='utf-8') as f:
        json.dump(exploration_summary, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"📋 探索结果已保存到: dimension_exploration_results.json")

if __name__ == "__main__":
    main()
