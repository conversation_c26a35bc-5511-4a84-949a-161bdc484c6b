"""
我的参数配置文件
================

在这里定义您想要测试的参数组合。
修改下面的参数值，然后运行训练。
"""

def get_my_parameters():
    """
    🎯 全数据训练参数配置 - 包含完整TimeMixer参数

    基于68年日径流数据(24,836天)的完整训练配置
    数据范围: 1950-2017年，平均径流量: 2184 m³/s
    包含所有TimeMixer可配置参数
    """

    # 📊 数据特征分析：
    # - 总数据量: 24,836天 (68年)
    # - 数据范围: 179-21,200 m³/s
    # - 季节性: 明显的年周期性
    # - 趋势性: 长期变化趋势

    # 🔧 完整参数说明：
    # 数据参数: n_steps, n_pred_steps
    # 模型架构: n_layers, d_model, d_ffn, top_k, moving_avg, downsampling_window, downsampling_layers
    # 正则化: dropout, use_norm
    # 训练参数: epochs, batch_size, learning_rate, patience
    # 系统参数: device, num_workers

    my_configs = [
        # 🚀 配置1: 快速验证 (包含所有参数)
        {
            'name': '快速验证_完整参数',
            # 数据相关参数
            'n_steps': 96,                    # 输入序列长度
            'n_pred_steps': 5,               # 预测序列长度
            'max_samples': 1000,              # 限制样本数量(快速验证)

            # 模型架构参数
            'n_layers': 3,                    # 模型层数
            'd_model': 64,                    # 模型维度
            'd_ffn': 128,                     # 前馈网络维度
            'top_k': 5,                       # TimeMixer的top-k参数
            'moving_avg': 5,                  # 移动平均窗口大小 (避免维度问题)
            'downsampling_window': 2,         # 下采样窗口大小
            'downsampling_layers': 1,         # 下采样层数

            # 正则化参数
            'dropout': 0.1,                   # Dropout率
            'use_norm': True,                 # 是否使用层归一化

            # 训练参数
            'epochs': 100,                     # 训练轮次
            'batch_size': 32,                 # 批次大小
            'learning_rate': 1e-3,            # 学习率
            'patience': 5,                    # 早停耐心值

            # 系统参数
            'device': 'cuda',                 # 计算设备
            'num_workers': 0                  # 数据加载进程数
        },

        # 🌊 配置2: 季节性预测 (中等复杂度)
        {
            'name': '季节性预测_中等',
            # 数据相关参数
            'n_steps': 90,                    # 一季度历史数据
            'n_pred_steps': 30,               # 预测一个月

            # 模型架构参数
            'n_layers': 3,                    # 更深网络
            'd_model': 128,                   # 更大维度
            'd_ffn': 256,                     # 更大前馈网络
            'top_k': 7,                       # 更大top-k
            'moving_avg': 5,                  # 移动平均窗口 (避免维度问题)
            'downsampling_window': 3,         # 更大下采样窗口
            'downsampling_layers': 2,         # 更多下采样层

            # 正则化参数
            'dropout': 0.15,                  # 中度正则化
            'use_norm': True,                 # 使用层归一化

            # 训练参数
            'epochs': 400,                     # 更多训练轮次
            'batch_size': 24,                 # 中等批次
            'learning_rate': 5e-4,            # 中等学习率
            'patience': 40,                   # 中等耐心

            # 系统参数
            'device': 'cuda',                 # GPU加速
            'num_workers': 0                  # 单进程
        },

        # 📅 配置3: 年度长期预测 (高复杂度)
        {
            'name': '年度长期预测_高复杂',
            # 数据相关参数
            'n_steps': 365,                   # 一年历史数据
            'n_pred_steps': 90,               # 预测三个月

            # 模型架构参数
            'n_layers': 4,                    # 深层网络
            'd_model': 256,                   # 大维度
            'd_ffn': 512,                     # 大前馈网络
            'top_k': 10,                      # 大top-k
            'moving_avg': 10,                 # 移动平均窗口 (避免维度问题)
            'downsampling_window': 4,         # 大下采样窗口
            'downsampling_layers': 3,         # 多下采样层

            # 正则化参数
            'dropout': 0.2,                   # 强正则化
            'use_norm': True,                 # 使用层归一化

            # 训练参数
            'epochs': 80,                     # 长期训练
            'batch_size': 16,                 # 小批次(内存考虑)
            'learning_rate': 2e-4,            # 低学习率
            'patience': 15,                   # 大耐心

            # 系统参数
            'device': 'cuda',                 # GPU加速
            'num_workers': 0                  # 单进程
        },

        # 🎯 配置4: 极端事件预测 (最高复杂度)
        {
            'name': '极端事件预测_最高',
            # 数据相关参数
            'n_steps': 180,                   # 半年历史数据
            'n_pred_steps': 14,               # 预测两周

            # 模型架构参数
            'n_layers': 4,                    # 最深网络
            'd_model': 384,                   # 最大维度
            'd_ffn': 768,                     # 最大前馈网络
            'top_k': 15,                      # 最大top-k
            'moving_avg': 10,                 # 移动平均窗口 (避免维度问题)
            'downsampling_window': 5,         # 最大下采样窗口
            'downsampling_layers': 3,         # 最多下采样层

            # 正则化参数
            'dropout': 0.25,                  # 最强正则化
            'use_norm': True,                 # 使用层归一化

            # 训练参数
            'epochs': 100,                    # 最长训练
            'batch_size': 8,                  # 最小批次
            'learning_rate': 1e-4,            # 最低学习率
            'patience': 20,                   # 最大耐心

            # 系统参数
            'device': 'cuda',                 # GPU加速
            'num_workers': 0                  # 单进程
        },

        # ⚡ 配置5: 学习率实验
        {
            'name': '学习率实验_高LR',
            # 数据相关参数
            'n_steps': 72,                    # 中等序列长度
            'n_pred_steps': 18,               # 中等预测长度

            # 模型架构参数
            'n_layers': 2,                    # 标准层数
            'd_model': 96,                    # 中等维度
            'd_ffn': 192,                     # 中等前馈网络
            'top_k': 5,                       # 标准top-k
            'moving_avg': 5,                  # 移动平均窗口 (避免维度问题)
            'downsampling_window': 2,         # 标准下采样窗口
            'downsampling_layers': 1,         # 标准下采样层

            # 正则化参数
            'dropout': 0.15,                  # 中度正则化
            'use_norm': True,                 # 使用层归一化

            # 训练参数
            'epochs': 30,                     # 中等训练轮次
            'batch_size': 32,                 # 标准批次
            'learning_rate': 2e-3,            # 高学习率实验
            'patience': 8,                    # 中等耐心

            # 系统参数
            'device': 'cuda',                 # GPU加速
            'num_workers': 0                  # 单进程
        },

        # 🔬 配置6: 架构实验 (宽网络)
        {
            'name': '架构实验_宽网络',
            # 数据相关参数
            'n_steps': 96,                    # 标准序列长度
            'n_pred_steps': 24,               # 标准预测长度
            'max_samples': 5000,              # 中等样本数量

            # 模型架构参数
            'n_layers': 2,                    # 浅层但宽网络
            'd_model': 512,                   # 超大维度
            'd_ffn': 1024,                    # 超大前馈网络
            'top_k': 10,                      # 大top-k
            'moving_avg': 10,                 # 移动平均窗口 (避免维度问题)
            'downsampling_window': 3,         # 中等下采样窗口
            'downsampling_layers': 2,         # 中等下采样层

            # 正则化参数
            'dropout': 0.2,                   # 强正则化(防止过拟合)
            'use_norm': True,                 # 使用层归一化

            # 训练参数
            'epochs': 60,                     # 充分训练
            'batch_size': 16,                 # 小批次(内存限制)
            'learning_rate': 3e-4,            # 低学习率
            'patience': 12,                   # 大耐心

            # 系统参数
            'device': 'cuda',                 # GPU加速
            'num_workers': 0                  # 单进程
        },

        # 🌟 配置7: 全数据训练 (使用所有24,836天数据)
        {
            'name': '全数据训练_最大规模',
            # 数据相关参数
            'n_steps': 96,                    # 96天历史
            'n_pred_steps': 5,               # 预测15天
            # 不设置max_samples，使用全部数据

            # 模型架构参数
            'n_layers': 3,                    # 深层网络
            'd_model': 256,                   # 大维度
            'd_ffn': 512,                     # 大前馈网络
            'top_k': 8,                       # 中等top-k
            'moving_avg': 5,                  # 移动平均窗口 (避免维度问题)
            'downsampling_window': 3,         # 标准下采样窗口
            'downsampling_layers': 2,         # 标准下采样层

            # 正则化参数
            'dropout': 0.15,                  # 中度正则化
            'use_norm': True,                 # 使用层归一化

            # 训练参数
            'epochs': 500,                    # 500轮训练
            'batch_size': 64,                 # 大批次(充分利用GPU)
            'learning_rate': 8e-4,            # 中等学习率
            'patience': 30,                   # 大耐心值

            # 系统参数
            'device': 'cuda',                 # GPU加速
            'num_workers': 0                  # 单进程
        }
    ]
    
    return my_configs

# 💡 使用示例：
# 1. 修改上面的参数值
# 2. 运行: python run_my_training.py
# 3. 查看结果: timemixer_evaluation_results.csv

if __name__ == "__main__":
    configs = get_my_parameters()
    print("您定义的参数配置:")
    for i, config in enumerate(configs, 1):
        print(f"\n{i}. {config['name']}")
        for key, value in config.items():
            if key != 'name':
                print(f"   {key}: {value}")
