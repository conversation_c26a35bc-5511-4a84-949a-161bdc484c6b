"""
TimeMixer++ Implementation Based on test.py
===========================================

This script implements TimeMixer++ for runoff forecasting based on the 
structure and parameters from test.py.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

def load_runoff_data():
    """Load and prepare runoff data."""
    print("🌊 Loading Runoff Data for TimeMixer++")
    print("=" * 50)
    
    file_path = r'C:\Users\<USER>\Desktop\timemix\1964-2017dailyRunoff.csv'
    
    try:
        data = pd.read_excel(file_path)
        data['DATA'] = pd.to_datetime(data['DATA'])
        data = data.sort_values('DATA').reset_index(drop=True)
        
        print(f"✅ Data loaded successfully")
        print(f"   📊 Shape: {data.shape}")
        print(f"   📅 Date range: {data['DATA'].min().date()} to {data['DATA'].max().date()}")
        print(f"   💧 Runoff range: {data['runoff'].min():.1f} - {data['runoff'].max():.1f}")
        print(f"   📈 Average: {data['runoff'].mean():.1f}")
        
        return data
        
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        return None

def prepare_pypots_dataset(data, n_steps=60, n_pred_steps=14):
    """
    Prepare dataset in PyPOTS format similar to physionet2012_dataset.
    
    Args:
        data: Raw runoff data
        n_steps: Input sequence length
        n_pred_steps: Prediction sequence length
        
    Returns:
        Dictionary with dataset information and prepared data
    """
    print(f"\n🔧 Preparing PyPOTS Dataset")
    print(f"   📥 Input steps: {n_steps}")
    print(f"   📤 Prediction steps: {n_pred_steps}")
    
    values = data['runoff'].values
    
    # Normalize data (important for neural networks)
    mean_val = np.mean(values)
    std_val = np.std(values)
    normalized_values = (values - mean_val) / std_val
    
    print(f"   🔄 Normalized: mean=0, std=1 (original: mean={mean_val:.1f}, std={std_val:.1f})")
    
    # Create sequences
    total_length = n_steps + n_pred_steps
    X = []
    
    for i in range(len(normalized_values) - total_length + 1):
        sequence = normalized_values[i:(i + total_length)]
        X.append(sequence)
    
    X = np.array(X)
    
    # Reshape for PyPOTS: (n_samples, n_steps + n_pred_steps, n_features)
    X = X.reshape(X.shape[0], X.shape[1], 1)
    
    print(f"   ✅ Created {X.shape[0]} sequences")
    print(f"   📊 Full sequence shape: {X.shape}")
    
    # Split data chronologically
    n_samples = X.shape[0]
    train_end = int(0.7 * n_samples)
    val_end = int(0.85 * n_samples)
    
    train_X = X[:train_end]
    val_X = X[train_end:val_end]
    test_X = X[val_end:]
    
    print(f"   📊 Train: {train_X.shape[0]} samples")
    print(f"   📊 Validation: {val_X.shape[0]} samples")
    print(f"   📊 Test: {test_X.shape[0]} samples")
    
    # Create dataset dictionary similar to physionet2012_dataset
    dataset_info = {
        "n_steps": total_length - n_pred_steps,  # Input sequence length
        "n_pred_steps": n_pred_steps,             # Prediction length
        "n_features": 1,                          # Univariate
        "train_X": train_X,
        "val_X": val_X,
        "test_X": test_X,
        "mean_val": mean_val,
        "std_val": std_val
    }
    
    # Prepare datasets for PyPOTS forecasting format
    # For forecasting, we need to split the sequence into input and target parts
    input_steps = total_length - n_pred_steps

    # Split sequences into input and target parts
    train_X_input = train_X[:, :input_steps, :]
    train_X_target = train_X[:, input_steps:, :]

    val_X_input = val_X[:, :input_steps, :]
    val_X_target = val_X[:, input_steps:, :]

    test_X_input = test_X[:, :input_steps, :]
    test_X_target = test_X[:, input_steps:, :]

    # Create proper PyPOTS forecasting datasets
    dataset_for_training = {
        "X": train_X_input,
        "X_pred": train_X_target  # Target for forecasting
    }
    dataset_for_validating = {
        "X": val_X_input,
        "X_pred": val_X_target
    }
    dataset_for_testing = {
        "X": test_X_input,
        "X_pred": test_X_target
    }
    
    return dataset_info, dataset_for_training, dataset_for_validating, dataset_for_testing

def create_timemixer_model(dataset_info, n_pred_steps):
    """
    Create TimeMixer model based on test.py configuration.
    
    Args:
        dataset_info: Dataset information dictionary
        n_pred_steps: Number of prediction steps
        
    Returns:
        Configured TimeMixer model
    """
    print(f"\n🤖 Creating TimeMixer Model (Based on test.py)")
    
    try:
        # Import PyPOTS components
        from pypots.optim import Adam
        from pypots.forecasting import TimeMixer
        
        print("✅ PyPOTS imported successfully")
        
        # Create TimeMixer model with parameters from test.py
        timemixer = TimeMixer(
            # Data configuration
            n_steps=dataset_info["n_steps"],                  # Input steps
            n_features=dataset_info["n_features"],            # Number of features
            n_pred_steps=n_pred_steps,                        # Prediction steps
            n_pred_features=dataset_info["n_features"],       # Prediction features
            
            # Model architecture (from test.py)
            term="short",                    # Term type
            n_layers=2,                      # Number of layers
            top_k=5,                         # Top-k parameter
            d_model=64,                      # Model dimension (increased from test.py's 32)
            d_ffn=128,                       # FFN dimension (increased from test.py's 32)
            moving_avg=25,                   # Moving average window
            downsampling_window=2,           # Downsampling window
            downsampling_layers=1,           # Downsampling layers
            use_norm=True,                   # Use normalization
            dropout=0.1,                     # Dropout rate
            
            # Training configuration
            epochs=50,                       # More epochs for better performance
            patience=10,                     # Early stopping patience
            optimizer=Adam(lr=1e-3),         # Adam optimizer with learning rate
            num_workers=0,                   # Data loading workers
            device='cpu',                    # Use CPU to avoid CUDA issues
            
            # Saving configuration
            saving_path="timemixer_runoff_results",
            model_saving_strategy="best",    # Save best model only
        )
        
        print("✅ TimeMixer model created successfully")
        print(f"   ⚙️  Configuration:")
        print(f"      📥 Input steps: {dataset_info['n_steps'] - n_pred_steps}")
        print(f"      📤 Prediction steps: {n_pred_steps}")
        print(f"      🧠 Model dimension: 64")
        print(f"      🔢 Layers: 2")
        print(f"      🎯 Epochs: 50")
        
        return timemixer
        
    except ImportError as e:
        print(f"❌ PyPOTS import failed: {e}")
        print("💡 Please fix PyPOTS installation first")
        return None
    except Exception as e:
        print(f"❌ Model creation failed: {e}")
        return None

def train_and_evaluate_timemixer(timemixer, train_set, val_set, test_set, dataset_info, n_pred_steps):
    """
    Train and evaluate TimeMixer model following test.py structure.
    
    Args:
        timemixer: TimeMixer model
        train_set: Training dataset
        val_set: Validation dataset
        test_set: Test dataset
        dataset_info: Dataset information
        n_pred_steps: Number of prediction steps
        
    Returns:
        Predictions and evaluation results
    """
    print(f"\n🚀 Training TimeMixer Model")
    print("=" * 50)
    
    try:
        # Train the model (following test.py)
        print("Starting training...")
        timemixer.fit(train_set=train_set, val_set=val_set)
        print("✅ Training completed!")
        
        # Make predictions (following test.py)
        print("\n🔮 Making predictions...")
        timemixer_results = timemixer.predict(test_set)
        timemixer_prediction = timemixer_results["forecasting"]
        
        print(f"✅ Predictions completed")
        print(f"   📊 Prediction shape: {timemixer_prediction.shape}")
        
        # Denormalize predictions
        mean_val = dataset_info["mean_val"]
        std_val = dataset_info["std_val"]
        
        pred_denorm = timemixer_prediction * std_val + mean_val
        
        print(f"   🔄 Predictions denormalized")
        
        # Calculate evaluation metrics (similar to test.py)
        try:
            from pypots.nn.functional import calc_mae

            # Get ground truth from test set (the target part)
            ground_truth = test_set["X_pred"]  # This is the target we want to predict
            ground_truth_denorm = ground_truth * std_val + mean_val
            
            # Calculate MAE (following test.py)
            testing_mae = calc_mae(
                pred_denorm,
                ground_truth_denorm,
                np.ones_like(ground_truth_denorm)  # No missing values mask
            )
            
            print(f"📊 Testing Mean Absolute Error: {testing_mae:.4f}")
            
        except Exception as e:
            print(f"⚠️ Evaluation calculation failed: {e}")
            testing_mae = None
        
        return pred_denorm, testing_mae
        
    except Exception as e:
        print(f"❌ Training/prediction failed: {e}")
        return None, None

def visualize_timemixer_results(predictions, n_samples=4):
    """Visualize TimeMixer predictions."""
    print(f"\n📊 Creating TimeMixer Visualizations")
    
    try:
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        axes = axes.flatten()
        
        for i in range(min(n_samples, len(predictions))):
            ax = axes[i]
            
            days = range(1, len(predictions[i]) + 1)
            ax.plot(days, predictions[i].flatten(), 'b-', linewidth=2, marker='o', 
                   label='TimeMixer Prediction')
            
            ax.set_title(f'Sample {i+1}: TimeMixer Runoff Forecast')
            ax.set_xlabel('Forecast Days')
            ax.set_ylabel('Runoff')
            ax.legend()
            ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('timemixer_runoff_predictions.png', dpi=300, bbox_inches='tight')
        print("✅ Visualization saved: timemixer_runoff_predictions.png")
        plt.show()
        
    except Exception as e:
        print(f"⚠️ Visualization failed: {e}")

def save_timemixer_results(predictions, mae_score=None):
    """Save TimeMixer results to CSV."""
    print(f"\n💾 Saving TimeMixer Results")
    
    try:
        # Create results DataFrame
        results_data = []
        
        for sample_idx in range(min(10, len(predictions))):  # Save first 10 samples
            for day in range(len(predictions[sample_idx])):
                results_data.append({
                    'sample': sample_idx + 1,
                    'forecast_day': day + 1,
                    'predicted_runoff': predictions[sample_idx][day][0],
                })
        
        results_df = pd.DataFrame(results_data)
        results_df.to_csv('timemixer_predictions.csv', index=False)
        
        # Save summary statistics
        summary_data = {
            'metric': ['Mean', 'Std', 'Min', 'Max', 'MAE'],
            'value': [
                np.mean(predictions),
                np.std(predictions),
                np.min(predictions),
                np.max(predictions),
                mae_score if mae_score else 'N/A'
            ]
        }
        
        summary_df = pd.DataFrame(summary_data)
        summary_df.to_csv('timemixer_summary.csv', index=False)
        
        print("✅ Results saved:")
        print("   - timemixer_predictions.csv")
        print("   - timemixer_summary.csv")
        
    except Exception as e:
        print(f"⚠️ Saving failed: {e}")

def main():
    """Main function following test.py structure."""
    print("🌊 TIMEMIXER++ RUNOFF FORECASTING (Based on test.py)")
    print("=" * 70)
    print("This implementation follows the exact structure from test.py")
    print()
    
    # Configuration
    N_PRED_STEPS = 14  # Predict 14 days (2 weeks)
    N_INPUT_STEPS = 60  # Use 60 days of history (2 months)
    
    # Step 1: Load data
    data = load_runoff_data()
    if data is None:
        return
    
    # Step 2: Prepare PyPOTS dataset
    dataset_info, train_set, val_set, test_set = prepare_pypots_dataset(
        data, n_steps=N_INPUT_STEPS, n_pred_steps=N_PRED_STEPS
    )
    
    # Step 3: Create TimeMixer model
    timemixer = create_timemixer_model(dataset_info, N_PRED_STEPS)
    if timemixer is None:
        print("\n❌ Cannot proceed without TimeMixer model")
        print("💡 Please fix PyPOTS installation issues first")
        return
    
    # Step 4: Train and evaluate
    predictions, mae_score = train_and_evaluate_timemixer(
        timemixer, train_set, val_set, test_set, dataset_info, N_PRED_STEPS
    )
    
    if predictions is None:
        print("❌ Training failed")
        return
    
    # Step 5: Visualize results
    visualize_timemixer_results(predictions)
    
    # Step 6: Save results
    save_timemixer_results(predictions, mae_score)
    
    # Final summary
    print(f"\n🎉 TIMEMIXER++ FORECASTING COMPLETED!")
    print("=" * 70)
    print("✅ Successfully implemented TimeMixer++ based on test.py")
    print(f"📊 Configuration: {N_INPUT_STEPS} days → {N_PRED_STEPS} days")
    if mae_score:
        print(f"📈 Mean Absolute Error: {mae_score:.4f}")
    print(f"📁 Generated files:")
    print(f"   - timemixer_runoff_predictions.png")
    print(f"   - timemixer_predictions.csv")
    print(f"   - timemixer_summary.csv")
    print(f"   - Model saved in: timemixer_runoff_results/")
    print(f"\n✨ Your TimeMixer++ runoff forecasting system is ready!")

if __name__ == "__main__":
    main()
