"""
Univariate Time Series Data Loader
==================================

This module handles loading and preprocessing of CSV time series data
for univariate forecasting tasks using TimeMixer++.
"""

import pandas as pd
import numpy as np
from typing import Dict, Tuple, Optional, List
from datetime import datetime
import warnings

warnings.filterwarnings('ignore')


class UnivariateTimeSeriesLoader:
    """
    Data loader for univariate time series forecasting.
    
    Handles CSV data with DATE and target variable columns.
    """
    
    def __init__(self, 
                 date_column: str = 'DATA',
                 target_column: str = 'runoff',
                 date_format: str = '%Y/%m/%d'):
        """
        Initialize the data loader.
        
        Args:
            date_column: Name of the date column
            target_column: Name of the target variable column
            date_format: Format of the date strings
        """
        self.date_column = date_column
        self.target_column = target_column
        self.date_format = date_format
        self.data = None
        self.processed_data = None
        self.scaler_params = None
        
    def load_csv(self, file_path: str) -> pd.DataFrame:
        """
        Load CSV or Excel file and parse dates.

        Args:
            file_path: Path to the CSV or Excel file

        Returns:
            Loaded DataFrame
        """
        try:
            # Determine file type and load accordingly
            if file_path.lower().endswith(('.xlsx', '.xls')):
                self.data = pd.read_excel(file_path)
                print(f"✓ Loaded Excel file with shape: {self.data.shape}")
            elif file_path.lower().endswith('.csv'):
                self.data = pd.read_csv(file_path)
                print(f"✓ Loaded CSV file with shape: {self.data.shape}")
            else:
                # Try both methods
                try:
                    self.data = pd.read_excel(file_path)
                    print(f"✓ Loaded as Excel file with shape: {self.data.shape}")
                except:
                    self.data = pd.read_csv(file_path)
                    print(f"✓ Loaded as CSV file with shape: {self.data.shape}")

            # Parse dates - try different formats
            try:
                self.data[self.date_column] = pd.to_datetime(
                    self.data[self.date_column],
                    format=self.date_format
                )
            except:
                # Try automatic date parsing
                self.data[self.date_column] = pd.to_datetime(self.data[self.date_column])
                print("✓ Used automatic date parsing")

            # Sort by date
            self.data = self.data.sort_values(self.date_column).reset_index(drop=True)

            # Basic info
            print(f"✓ Date range: {self.data[self.date_column].min()} to {self.data[self.date_column].max()}")
            print(f"✓ Target variable '{self.target_column}' statistics:")
            print(f"  - Mean: {self.data[self.target_column].mean():.2f}")
            print(f"  - Std: {self.data[self.target_column].std():.2f}")
            print(f"  - Min: {self.data[self.target_column].min():.2f}")
            print(f"  - Max: {self.data[self.target_column].max():.2f}")

            return self.data

        except Exception as e:
            raise ValueError(f"Error loading file: {e}")
    
    def check_missing_values(self) -> Dict[str, int]:
        """
        Check for missing values in the data.
        
        Returns:
            Dictionary with missing value counts
        """
        if self.data is None:
            raise ValueError("No data loaded. Call load_csv() first.")
        
        missing_info = {}
        missing_info['total_rows'] = len(self.data)
        missing_info['missing_dates'] = self.data[self.date_column].isnull().sum()
        missing_info['missing_values'] = self.data[self.target_column].isnull().sum()
        missing_info['missing_percentage'] = (missing_info['missing_values'] / missing_info['total_rows']) * 100
        
        print(f"Missing Values Analysis:")
        print(f"  - Total rows: {missing_info['total_rows']}")
        print(f"  - Missing dates: {missing_info['missing_dates']}")
        print(f"  - Missing values: {missing_info['missing_values']} ({missing_info['missing_percentage']:.2f}%)")
        
        return missing_info
    
    def normalize_data(self, method: str = 'minmax') -> np.ndarray:
        """
        Normalize the target variable.
        
        Args:
            method: Normalization method ('minmax' or 'zscore')
            
        Returns:
            Normalized data array
        """
        if self.data is None:
            raise ValueError("No data loaded. Call load_csv() first.")
        
        values = self.data[self.target_column].values
        
        if method == 'minmax':
            min_val = np.nanmin(values)
            max_val = np.nanmax(values)
            normalized = (values - min_val) / (max_val - min_val)
            self.scaler_params = {'method': 'minmax', 'min': min_val, 'max': max_val}
            
        elif method == 'zscore':
            mean_val = np.nanmean(values)
            std_val = np.nanstd(values)
            normalized = (values - mean_val) / std_val
            self.scaler_params = {'method': 'zscore', 'mean': mean_val, 'std': std_val}
            
        else:
            raise ValueError("Method must be 'minmax' or 'zscore'")
        
        print(f"✓ Data normalized using {method} method")
        return normalized
    
    def denormalize_data(self, normalized_data: np.ndarray) -> np.ndarray:
        """
        Denormalize the data back to original scale.
        
        Args:
            normalized_data: Normalized data array
            
        Returns:
            Denormalized data array
        """
        if self.scaler_params is None:
            raise ValueError("No scaler parameters found. Call normalize_data() first.")
        
        if self.scaler_params['method'] == 'minmax':
            denormalized = normalized_data * (self.scaler_params['max'] - self.scaler_params['min']) + self.scaler_params['min']
        elif self.scaler_params['method'] == 'zscore':
            denormalized = normalized_data * self.scaler_params['std'] + self.scaler_params['mean']
        
        return denormalized
    
    def create_sequences(self, 
                        data: np.ndarray, 
                        sequence_length: int, 
                        prediction_length: int = 1) -> Tuple[np.ndarray, np.ndarray]:
        """
        Create input-output sequences for time series forecasting.
        
        Args:
            data: Input time series data
            sequence_length: Length of input sequences
            prediction_length: Length of prediction sequences
            
        Returns:
            Tuple of (input_sequences, target_sequences)
        """
        X, y = [], []
        
        for i in range(len(data) - sequence_length - prediction_length + 1):
            # Input sequence
            X.append(data[i:(i + sequence_length)])
            # Target sequence
            y.append(data[(i + sequence_length):(i + sequence_length + prediction_length)])
        
        X = np.array(X)
        y = np.array(y)
        
        # Reshape for univariate case: (samples, sequence_length, 1)
        X = X.reshape(X.shape[0], X.shape[1], 1)
        y = y.reshape(y.shape[0], y.shape[1], 1)
        
        print(f"✓ Created sequences - Input shape: {X.shape}, Target shape: {y.shape}")
        
        return X, y
    
    def split_data(self, 
                   X: np.ndarray, 
                   y: np.ndarray, 
                   train_ratio: float = 0.7, 
                   val_ratio: float = 0.2) -> Tuple[Dict, Dict, Dict]:
        """
        Split data into train, validation, and test sets.
        
        Args:
            X: Input sequences
            y: Target sequences
            train_ratio: Ratio for training data
            val_ratio: Ratio for validation data
            
        Returns:
            Tuple of (train_data, val_data, test_data) dictionaries
        """
        n_samples = X.shape[0]
        train_end = int(n_samples * train_ratio)
        val_end = int(n_samples * (train_ratio + val_ratio))
        
        train_data = {
            'X': X[:train_end],
            'y': y[:train_end]
        }
        
        val_data = {
            'X': X[train_end:val_end],
            'y': y[train_end:val_end]
        }
        
        test_data = {
            'X': X[val_end:],
            'y': y[val_end:]
        }
        
        print(f"✓ Data split completed:")
        print(f"  - Train: {train_data['X'].shape[0]} samples")
        print(f"  - Validation: {val_data['X'].shape[0]} samples")
        print(f"  - Test: {test_data['X'].shape[0]} samples")
        
        return train_data, val_data, test_data
    
    def get_data_info(self) -> Dict:
        """
        Get comprehensive information about the loaded data.
        
        Returns:
            Dictionary with data information
        """
        if self.data is None:
            raise ValueError("No data loaded. Call load_csv() first.")
        
        info = {
            'total_samples': len(self.data),
            'date_range': {
                'start': self.data[self.date_column].min(),
                'end': self.data[self.date_column].max(),
                'days': (self.data[self.date_column].max() - self.data[self.date_column].min()).days
            },
            'target_stats': {
                'mean': self.data[self.target_column].mean(),
                'std': self.data[self.target_column].std(),
                'min': self.data[self.target_column].min(),
                'max': self.data[self.target_column].max(),
                'median': self.data[self.target_column].median()
            },
            'missing_values': self.data[self.target_column].isnull().sum(),
            'data_frequency': self._detect_frequency()
        }
        
        return info
    
    def _detect_frequency(self) -> str:
        """
        Detect the frequency of the time series data.
        
        Returns:
            Detected frequency string
        """
        if self.data is None or len(self.data) < 2:
            return "Unknown"
        
        # Calculate time differences
        time_diffs = self.data[self.date_column].diff().dropna()
        most_common_diff = time_diffs.mode().iloc[0] if not time_diffs.empty else None
        
        if most_common_diff is None:
            return "Unknown"
        
        days = most_common_diff.days
        
        if days == 1:
            return "Daily"
        elif days == 7:
            return "Weekly"
        elif 28 <= days <= 31:
            return "Monthly"
        elif 365 <= days <= 366:
            return "Yearly"
        else:
            return f"Every {days} days"


def create_sample_data(file_path: str = "sample_runoff_data.csv"):
    """
    Create a sample CSV file for testing.
    
    Args:
        file_path: Path where to save the sample file
    """
    # Generate sample data
    dates = pd.date_range(start='1950-01-01', end='1952-12-31', freq='D')
    
    # Create synthetic runoff data with seasonal patterns
    np.random.seed(42)
    base_runoff = 1200
    seasonal_component = 300 * np.sin(2 * np.pi * np.arange(len(dates)) / 365.25)
    trend_component = 0.1 * np.arange(len(dates))
    noise = np.random.normal(0, 50, len(dates))
    
    runoff = base_runoff + seasonal_component + trend_component + noise
    runoff = np.maximum(runoff, 100)  # Ensure positive values
    
    # Create DataFrame
    sample_data = pd.DataFrame({
        'DATA': dates.strftime('%Y/%m/%d'),
        'runoff': runoff.round(0).astype(int)
    })
    
    # Save to CSV
    sample_data.to_csv(file_path, index=False)
    print(f"✓ Sample data created: {file_path}")
    print(f"  - {len(sample_data)} records from {dates[0].date()} to {dates[-1].date()}")
    
    return file_path


if __name__ == "__main__":
    # Create sample data for testing
    sample_file = create_sample_data()
    
    # Test the data loader
    loader = UnivariateTimeSeriesLoader()
    
    # Load data
    data = loader.load_csv(sample_file)
    
    # Check missing values
    missing_info = loader.check_missing_values()
    
    # Get data info
    info = loader.get_data_info()
    print(f"\nData Info:")
    print(f"  - Frequency: {info['data_frequency']}")
    print(f"  - Date range: {info['date_range']['days']} days")
    
    # Normalize data
    normalized_data = loader.normalize_data('minmax')
    
    # Create sequences
    X, y = loader.create_sequences(normalized_data, sequence_length=30, prediction_length=7)
    
    # Split data
    train_data, val_data, test_data = loader.split_data(X, y)
    
    print("\n✓ Data loader test completed successfully!")
