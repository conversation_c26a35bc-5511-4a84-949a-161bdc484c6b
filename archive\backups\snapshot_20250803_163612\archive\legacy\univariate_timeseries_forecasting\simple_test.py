"""
Simple test to verify data loading
"""

print("Starting simple test...")

try:
    import pandas as pd
    print("✓ pandas imported")
    
    import numpy as np
    print("✓ numpy imported")
    
    # Test data loading
    file_path = r'C:\Users\<USER>\Desktop\timemix\1964-2017dailyRunoff.csv'
    print(f"Attempting to load: {file_path}")
    
    # Try Excel first
    try:
        data = pd.read_excel(file_path)
        print(f"✓ Loaded as Excel: {data.shape}")
    except Exception as e:
        print(f"Excel load failed: {e}")
        try:
            data = pd.read_csv(file_path)
            print(f"✓ Loaded as CSV: {data.shape}")
        except Exception as e2:
            print(f"CSV load failed: {e2}")
            exit(1)
    
    # Check columns
    print(f"Columns: {list(data.columns)}")
    
    # Check first few rows
    print("First 5 rows:")
    print(data.head())
    
    # Basic stats
    print(f"Runoff stats: min={data['runoff'].min()}, max={data['runoff'].max()}, mean={data['runoff'].mean():.2f}")
    
    print("✅ Simple test completed successfully!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
