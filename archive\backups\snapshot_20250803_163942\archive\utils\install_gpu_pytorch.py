"""
GPU PyTorch 安装脚本
===================

自动检测并安装适合的GPU版本PyTorch。
"""

import subprocess
import sys
import platform
import torch

def check_current_pytorch():
    """检查当前PyTorch版本"""
    print("当前PyTorch信息:")
    print(f"  版本: {torch.__version__}")
    print(f"  CUDA可用: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        print(f"  CUDA版本: {torch.version.cuda}")
        print(f"  GPU数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"  GPU {i}: {torch.cuda.get_device_name(i)}")
        return True
    else:
        print("  当前为CPU版本")
        return False

def check_nvidia_driver():
    """检查NVIDIA驱动"""
    try:
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✓ NVIDIA驱动已安装")
            lines = result.stdout.split('\n')
            for line in lines:
                if 'CUDA Version' in line:
                    cuda_version = line.split('CUDA Version: ')[1].split()[0]
                    print(f"  支持的CUDA版本: {cuda_version}")
                    return cuda_version
            return "Unknown"
        else:
            print("✗ NVIDIA驱动未安装或不可用")
            return None
    except FileNotFoundError:
        print("✗ nvidia-smi 命令不存在，请安装NVIDIA驱动")
        return None

def get_installation_command():
    """获取安装命令"""
    system = platform.system().lower()
    
    print(f"\n检测到系统: {platform.system()} {platform.machine()}")
    
    # 检查包管理器
    has_conda = False
    has_pip = True
    
    try:
        subprocess.run(['conda', '--version'], capture_output=True, check=True)
        has_conda = True
        print("✓ 检测到 Conda")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("✗ 未检测到 Conda")
    
    try:
        subprocess.run([sys.executable, '-m', 'pip', '--version'], capture_output=True, check=True)
        print("✓ 检测到 pip")
    except subprocess.CalledProcessError:
        has_pip = False
        print("✗ pip 不可用")
    
    if not has_conda and not has_pip:
        print("错误: 没有可用的包管理器")
        return None, None
    
    # 推荐的安装命令
    if has_conda:
        conda_cmd = [
            "conda", "uninstall", "pytorch", "torchvision", "torchaudio", "-y",
            "&&",
            "conda", "install", "pytorch", "torchvision", "torchaudio", "pytorch-cuda=11.8", "-c", "pytorch", "-c", "nvidia"
        ]
        conda_cmd_str = " ".join(conda_cmd)
    else:
        conda_cmd_str = None
    
    if has_pip:
        pip_cmd = [
            sys.executable, "-m", "pip", "uninstall", "torch", "torchvision", "torchaudio", "-y",
            "&&",
            sys.executable, "-m", "pip", "install", "torch", "torchvision", "torchaudio", "--index-url", "https://download.pytorch.org/whl/cu118"
        ]
        pip_cmd_str = " ".join(pip_cmd)
    else:
        pip_cmd_str = None
    
    return conda_cmd_str, pip_cmd_str

def install_gpu_pytorch(method='auto'):
    """安装GPU版本的PyTorch"""
    
    print("GPU PyTorch 安装向导")
    print("="*50)
    
    # 检查当前状态
    has_gpu = check_current_pytorch()
    if has_gpu:
        print("\n✓ GPU版本的PyTorch已经安装!")
        return True
    
    # 检查NVIDIA驱动
    cuda_version = check_nvidia_driver()
    if not cuda_version:
        print("\n❌ 错误: 需要先安装NVIDIA驱动")
        print("\n请按以下步骤操作:")
        print("1. 访问 https://www.nvidia.com/drivers/")
        print("2. 下载并安装适合您显卡的驱动")
        print("3. 重启电脑")
        print("4. 重新运行此脚本")
        return False
    
    # 获取安装命令
    conda_cmd, pip_cmd = get_installation_command()
    
    print(f"\n可用的安装方法:")
    methods = []
    if conda_cmd:
        methods.append(("conda", conda_cmd))
        print(f"1. Conda (推荐)")
    if pip_cmd:
        methods.append(("pip", pip_cmd))
        print(f"{len(methods)+1}. pip")
    
    if not methods:
        print("❌ 没有可用的安装方法")
        return False
    
    # 选择安装方法
    if method == 'auto':
        if len(methods) == 1:
            method_name, cmd = methods[0]
        else:
            print(f"\n请选择安装方法:")
            for i, (name, _) in enumerate(methods, 1):
                print(f"{i}. {name}")
            
            while True:
                try:
                    choice = int(input("请输入选择 (1-{}): ".format(len(methods))))
                    if 1 <= choice <= len(methods):
                        method_name, cmd = methods[choice-1]
                        break
                    else:
                        print("无效选择，请重试")
                except ValueError:
                    print("请输入数字")
    else:
        # 查找指定方法
        method_found = False
        for name, cmd in methods:
            if name == method:
                method_name, cmd = name, cmd
                method_found = True
                break
        
        if not method_found:
            print(f"❌ 指定的方法 '{method}' 不可用")
            return False
    
    print(f"\n将使用 {method_name} 安装GPU版本的PyTorch")
    print(f"命令: {cmd}")
    
    confirm = input("\n确认安装? (y/n): ").strip().lower()
    if confirm != 'y':
        print("安装已取消")
        return False
    
    print(f"\n开始安装...")
    
    try:
        # 执行安装命令
        if method_name == 'conda':
            # Conda命令需要特殊处理
            print("正在卸载当前版本...")
            subprocess.run(['conda', 'uninstall', 'pytorch', 'torchvision', 'torchaudio', '-y'], check=True)
            
            print("正在安装GPU版本...")
            subprocess.run(['conda', 'install', 'pytorch', 'torchvision', 'torchaudio', 'pytorch-cuda=11.8', '-c', 'pytorch', '-c', 'nvidia'], check=True)
            
        else:  # pip
            print("正在卸载当前版本...")
            subprocess.run([sys.executable, '-m', 'pip', 'uninstall', 'torch', 'torchvision', 'torchaudio', '-y'], check=True)
            
            print("正在安装GPU版本...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'torch', 'torchvision', 'torchaudio', '--index-url', 'https://download.pytorch.org/whl/cu118'], check=True)
        
        print("✓ 安装完成!")
        
        # 验证安装
        print("\n验证安装...")
        
        # 重新导入torch
        import importlib
        importlib.reload(torch)
        
        if torch.cuda.is_available():
            print("✓ GPU版本安装成功!")
            print(f"  CUDA版本: {torch.version.cuda}")
            print(f"  GPU数量: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                print(f"  GPU {i}: {torch.cuda.get_device_name(i)}")
            return True
        else:
            print("⚠ 安装完成但GPU不可用，可能需要重启")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 安装过程中出现错误: {e}")
        return False

def main():
    """主函数"""
    print("TimeMixer++ GPU环境配置")
    print("="*50)
    
    success = install_gpu_pytorch()
    
    if success:
        print(f"\n🎉 GPU环境配置成功!")
        print(f"\n现在您可以:")
        print(f"1. 运行 python check_gpu.py 验证GPU")
        print(f"2. 运行 python advanced_batch_training.py 进行GPU加速训练")
        print(f"3. 在训练参数中设置 'device': 'cuda' 使用GPU")
    else:
        print(f"\n❌ GPU环境配置失败")
        print(f"\n请尝试:")
        print(f"1. 检查NVIDIA驱动是否正确安装")
        print(f"2. 重启电脑后重试")
        print(f"3. 手动安装GPU版本的PyTorch")

if __name__ == "__main__":
    main()
