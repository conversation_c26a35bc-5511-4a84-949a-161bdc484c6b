"""
运行PSO优化并将结果添加到参数配置文件
=====================================

此脚本执行PSO超参数优化，并将找到的最优配置添加到my_parameters.py文件中
"""

import json
import os
from datetime import datetime
from pso_hyperparameter_optimization import PSOHyperparameterOptimizer, get_timemixer_param_space, mock_objective_function


def generate_pso_configs(n_configs: int = 5) -> list:
    """
    使用PSO算法生成多个优化的参数配置
    
    Args:
        n_configs: 要生成的配置数量
        
    Returns:
        优化后的参数配置列表
    """
    param_bounds, param_types = get_timemixer_param_space()
    
    all_configs = []
    all_histories = []
    
    for i in range(n_configs):
        print(f"\n{'='*60}")
        print(f"🔍 运行第 {i+1}/{n_configs} 次PSO优化")
        print(f"{'='*60}")
        
        # 创建PSO优化器 - 每次使用不同的随机种子
        optimizer = PSOHyperparameterOptimizer(
            param_bounds=param_bounds,
            param_types=param_types,
            n_particles=12,  # 适中的粒子数量
            max_iterations=15,  # 适中的迭代次数
            w=0.7,
            c1=1.5,
            c2=1.5
        )
        
        # 执行优化
        best_params, best_score, history = optimizer.optimize(
            objective_function=mock_objective_function,
            verbose=True
        )
        
        # 添加固定参数
        config = best_params.copy()
        config.update({
            'name': f'PSO优化配置_{i+1}',
            'use_norm': True,
            'device': 'cuda',
            'num_workers': 0
        })
        
        all_configs.append(config)
        all_histories.append({
            'config_id': i+1,
            'best_score': best_score,
            'history': history
        })
        
        print(f"\n✅ 第 {i+1} 次优化完成，最优分数: {best_score:.4f}")
    
    return all_configs, all_histories


def format_config_for_file(config: dict, config_num: int) -> str:
    """将配置格式化为文件中的字符串格式"""
    
    # 根据分数和特点生成更具描述性的名称
    if config_num <= 2:
        name_suffix = "高性能"
    elif config_num <= 4:
        name_suffix = "平衡型"
    else:
        name_suffix = "探索型"
    
    config_str = f"""
        # 🤖 配置{11 + config_num}: PSO优化配置 ({name_suffix})
        {{
            'name': 'PSO优化_{name_suffix}_{config_num}',
            # 数据相关参数 (PSO优化)
            'n_steps': {config['n_steps']},                    # 输入序列长度
            'n_pred_steps': {config['n_pred_steps']},               # 预测序列长度

            # 模型架构参数 (PSO优化)
            'n_layers': {config['n_layers']},                    # 模型层数
            'd_model': {config['d_model']},                   # 模型维度
            'd_ffn': {config['d_ffn']},                     # 前馈网络维度
            'top_k': {config['top_k']},                       # TimeMixer的top-k参数
            'moving_avg': {config['moving_avg']},                  # 移动平均窗口大小
            'downsampling_window': {config['downsampling_window']},         # 下采样窗口大小
            'downsampling_layers': {config['downsampling_layers']},         # 下采样层数

            # 正则化参数 (PSO优化)
            'dropout': {config['dropout']:.3f},                   # Dropout率
            'use_norm': {config['use_norm']},                 # 是否使用层归一化

            # 训练参数 (PSO优化)
            'epochs': {config['epochs']},                     # 训练轮次
            'batch_size': {config['batch_size']},                 # 批次大小
            'learning_rate': {config['learning_rate']:.2e},            # 学习率
            'patience': {config['patience']},                   # 早停耐心值

            # 系统参数
            'device': '{config['device']}',                 # 计算设备
            'num_workers': {config['num_workers']}                  # 数据加载进程数
        }}"""
    
    return config_str


def add_pso_configs_to_file(configs: list, histories: list):
    """将PSO优化的配置添加到my_parameters.py文件中"""
    
    # 读取当前文件内容
    with open('my_parameters.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 找到配置列表的结束位置 (在最后一个}之后，]之前)
    # 我们需要在最后一个配置后添加新配置
    
    # 生成PSO配置字符串
    pso_configs_str = ""
    for i, config in enumerate(configs, 1):
        pso_configs_str += "," + format_config_for_file(config, i)
    
    # 在文件末尾的 ] 之前插入新配置
    # 找到最后一个 } 和 ] 之间的位置
    last_brace_pos = content.rfind('}')
    bracket_pos = content.find(']', last_brace_pos)
    
    if last_brace_pos != -1 and bracket_pos != -1:
        # 在最后一个}后面插入新配置
        new_content = (content[:last_brace_pos + 1] + 
                      pso_configs_str + 
                      content[bracket_pos:])
        
        # 写入文件
        with open('my_parameters.py', 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"✅ 成功添加 {len(configs)} 个PSO优化配置到 my_parameters.py")
    else:
        print("❌ 无法找到合适的插入位置")
    
    # 保存优化历史
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    history_file = f'pso_optimization_results_{timestamp}.json'
    
    with open(history_file, 'w', encoding='utf-8') as f:
        json.dump({
            'timestamp': timestamp,
            'configs': configs,
            'optimization_histories': histories,
            'summary': {
                'total_configs': len(configs),
                'best_scores': [h['best_score'] for h in histories],
                'average_score': sum(h['best_score'] for h in histories) / len(histories)
            }
        }, f, indent=2, ensure_ascii=False)
    
    print(f"📊 优化结果已保存到: {history_file}")


def main():
    """主函数"""
    print("🚀 开始PSO超参数优化流程...")
    print("=" * 60)
    
    # 生成PSO优化配置
    configs, histories = generate_pso_configs(n_configs=5)
    
    print("\n" + "=" * 60)
    print("📋 PSO优化结果摘要:")
    print("=" * 60)
    
    for i, (config, history) in enumerate(zip(configs, histories), 1):
        print(f"\n配置 {i}:")
        print(f"  最优分数: {history['best_score']:.4f}")
        print(f"  关键参数: n_layers={config['n_layers']}, d_model={config['d_model']}, "
              f"lr={config['learning_rate']:.2e}")
    
    # 添加到参数文件
    print(f"\n📝 将优化结果添加到参数配置文件...")
    add_pso_configs_to_file(configs, histories)
    
    print("\n🎉 PSO优化流程完成!")
    print("💡 提示: 您现在可以运行 python run_my_training.py 来测试这些优化配置")


if __name__ == "__main__":
    main()
