"""
Basic Demo - Test Data Loading and Processing
"""

print("🌊 Basic Runoff Data Demo")
print("=" * 40)

# Step 1: Test imports
print("1. Testing imports...")
try:
    import pandas as pd
    import numpy as np
    print("   ✅ pandas and numpy imported")
except Exception as e:
    print(f"   ❌ Import error: {e}")
    exit(1)

# Step 2: Test data loading
print("\n2. Testing data loading...")
file_path = r'C:\Users\<USER>\Desktop\timemix\1964-2017dailyRunoff.csv'

try:
    # Try Excel format first
    data = pd.read_excel(file_path)
    print(f"   ✅ Data loaded successfully")
    print(f"   📊 Shape: {data.shape}")
    print(f"   📋 Columns: {list(data.columns)}")
    
    # Parse dates
    data['DATA'] = pd.to_datetime(data['DATA'])
    print(f"   📅 Date parsing successful")
    
    # Basic stats
    print(f"   📈 Date range: {data['DATA'].min().date()} to {data['DATA'].max().date()}")
    print(f"   💧 Runoff range: {data['runoff'].min():.1f} to {data['runoff'].max():.1f}")
    print(f"   📊 Average runoff: {data['runoff'].mean():.1f}")
    
except Exception as e:
    print(f"   ❌ Data loading error: {e}")
    exit(1)

# Step 3: Test sequence creation
print("\n3. Testing sequence creation...")
try:
    values = data['runoff'].values
    
    # Simple normalization
    min_val = np.min(values)
    max_val = np.max(values)
    normalized = (values - min_val) / (max_val - min_val)
    
    # Create a few sequences
    lookback = 30
    forecast = 7
    
    X, y = [], []
    for i in range(100):  # Just first 100 sequences for testing
        if i + lookback + forecast < len(normalized):
            X.append(normalized[i:(i + lookback)])
            y.append(normalized[(i + lookback):(i + lookback + forecast)])
    
    X = np.array(X).reshape(-1, lookback, 1)
    y = np.array(y).reshape(-1, forecast, 1)
    
    print(f"   ✅ Sequences created successfully")
    print(f"   📊 Input shape: {X.shape}")
    print(f"   📊 Target shape: {y.shape}")
    
except Exception as e:
    print(f"   ❌ Sequence creation error: {e}")
    exit(1)

# Step 4: Test PyPOTS availability
print("\n4. Testing PyPOTS availability...")
try:
    import pypots
    print(f"   ✅ PyPOTS version: {pypots.__version__}")

    try:
        from pypots.forecasting.timemixer import TimeMixer
        print("   ✅ TimeMixer imported successfully")

        # Try to create a simple model (don't train)
        model = TimeMixer(
            n_steps=30,
            n_features=1,
            n_pred_steps=7,
            n_pred_features=1,
            n_layers=1,
            d_model=64,
            epochs=1,  # Minimal for testing
            verbose=False
        )
        print("   ✅ TimeMixer model created successfully")

    except Exception as e:
        print(f"   ⚠️  TimeMixer creation error: {e}")

except ImportError as e:
    print("   ❌ PyPOTS not installed")
    print("   💡 Install with: pip install pypots")
except ValueError as e:
    if "packaging" in str(e):
        print("   ❌ PyPOTS dependency conflict detected")
        print("   🔧 Dependency issue: packaging version conflict")
        print("   💡 Try fixing with:")
        print("      pip install --upgrade packaging")
        print("      pip install --upgrade transformers")
        print("      pip install --force-reinstall pypots")
    else:
        print(f"   ❌ PyPOTS error: {e}")
except Exception as e:
    print(f"   ❌ Unexpected PyPOTS error: {e}")
    print("   💡 This might be a dependency conflict")

# Step 5: Summary
print("\n" + "=" * 40)
print("📋 DEMO SUMMARY")
print("=" * 40)
print("✅ Data loading: SUCCESS")
print("✅ Data processing: SUCCESS")
print("✅ Sequence creation: SUCCESS")

try:
    import pypots
    print("✅ PyPOTS: AVAILABLE")
except:
    print("❌ PyPOTS: NOT AVAILABLE")

print("\n🎯 Next steps:")
print("1. If PyPOTS is available, you can run full forecasting")
print("2. If not, install PyPOTS: pip install pypots")
print("3. Then run the complete forecasting demo")

print("\n✨ Basic demo completed!")
