"""
强制重新加载配置
===============

清除Python模块缓存并重新加载配置
"""

import sys
import importlib

def reload_and_check():
    """强制重新加载并检查配置"""
    
    # 清除模块缓存
    if 'my_parameters' in sys.modules:
        del sys.modules['my_parameters']
    
    # 重新导入
    import my_parameters
    importlib.reload(my_parameters)
    
    # 获取配置
    configs = my_parameters.get_my_parameters()
    config7 = configs[-1]
    
    print("🔄 强制重新加载后的配置7参数")
    print("="*50)
    print(f"配置名称: {config7['name']}")
    print(f"epochs: {config7['epochs']}")
    print(f"patience: {config7['patience']}")
    print(f"n_pred_steps: {config7['n_pred_steps']}")
    print(f"moving_avg: {config7['moving_avg']}")
    print(f"downsampling_window: {config7['downsampling_window']}")
    print()
    
    # 验证参数
    if config7['epochs'] == 500:
        print("✅ epochs参数正确: 500")
    else:
        print(f"❌ epochs参数仍然错误: {config7['epochs']}")
    
    if config7['patience'] == 30:
        print("✅ patience参数正确: 30")
    else:
        print(f"❌ patience参数仍然错误: {config7['patience']}")
    
    if config7['n_pred_steps'] == 15:
        print("✅ n_pred_steps参数正确: 15")
    else:
        print(f"❌ n_pred_steps参数错误: {config7['n_pred_steps']}")
    
    return config7

if __name__ == "__main__":
    config = reload_and_check()
