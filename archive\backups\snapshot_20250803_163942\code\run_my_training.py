"""
运行我的训练配置
================

使用 my_parameters.py 中定义的参数进行训练。
"""

from my_parameters import get_my_parameters
from compatible_training_runner import run_compatible_training
import time

def main():
    """主函数"""
    
    print("🎯 运行我的训练配置")
    print("="*50)
    
    # 获取您定义的参数
    my_configs = get_my_parameters()
    
    print(f"📋 您定义了 {len(my_configs)} 个配置:")
    for i, config in enumerate(my_configs, 1):
        print(f"{i}. {config['name']}")
        print(f"   序列: {config['n_steps']}→{config['n_pred_steps']}")
        print(f"   模型: {config['n_layers']}层, {config['d_model']}维度")
        print(f"   训练: {config['epochs']}轮")
        print(f"   设备: {config['device']}")
        print()
    
    print("选择运行方式:")
    print("1. 运行单个配置")
    print("2. 运行所有配置")
    print("3. 查看配置详情")
    
    choice = input("请选择 (1-3): ").strip()
    
    if choice == "1":
        # 运行单个配置
        while True:
            try:
                config_idx = int(input(f"选择配置编号 (1-{len(my_configs)}): ")) - 1
                if 0 <= config_idx < len(my_configs):
                    selected_config = my_configs[config_idx]
                    break
                else:
                    print("无效编号，请重试")
            except ValueError:
                print("请输入数字")
        
        print(f"\n🚀 开始训练: {selected_config['name']}")
        print("参数详情:")
        for key, value in selected_config.items():
            if key != 'name':
                print(f"  {key}: {value}")
        
        result = run_compatible_training(selected_config)
        
        if result:
            print(f"\n✅ 训练完成!")
            print(f"训练ID: {result['training_id']}")
            print(f"MAE: {result['mae']:.6f}")
            print(f"RMSE: {result['rmse']:.6f}")
            print(f"NSE: {result['nse']:.6f}")
            print(f"R²: {result['r2']:.6f}")
        else:
            print(f"\n❌ 训练失败")
    
    elif choice == "2":
        # 运行所有配置
        print(f"\n🚀 开始批量训练 (共{len(my_configs)}个配置)")
        
        results = []
        for i, config in enumerate(my_configs, 1):
            print(f"\n{'='*50}")
            print(f"进度: {i}/{len(my_configs)} - {config['name']}")
            print(f"{'='*50}")
            
            try:
                result = run_compatible_training(config)
                if result:
                    results.append({
                        'config_name': config['name'],
                        'training_id': result['training_id'],
                        'mae': result['mae'],
                        'rmse': result['rmse'],
                        'nse': result['nse'],
                        'r2': result['r2']
                    })
                    print(f"✅ {config['name']} 完成")
                    print(f"   MAE: {result['mae']:.6f}")
                    print(f"   R²: {result['r2']:.6f}")
                else:
                    print(f"❌ {config['name']} 失败")
            except Exception as e:
                print(f"❌ {config['name']} 异常: {e}")
            
            # 间隔时间
            if i < len(my_configs):
                print("等待 3 秒后继续...")
                time.sleep(3)
        
        # 显示结果汇总
        if results:
            print(f"\n📊 批量训练结果汇总:")
            print("="*80)
            print(f"{'配置名称':<20} {'训练ID':<12} {'MAE':<12} {'RMSE':<12} {'NSE':<8} {'R²':<8}")
            print("-" * 80)
            
            for result in results:
                print(f"{result['config_name']:<20} {result['training_id']:<12} "
                      f"{result['mae']:<12.6f} {result['rmse']:<12.6f} "
                      f"{result['nse']:<8.4f} {result['r2']:<8.4f}")
            
            # 找出最佳结果
            best_mae = min(results, key=lambda x: x['mae'])
            best_r2 = max(results, key=lambda x: x['r2'])
            
            print(f"\n🏆 最佳结果:")
            print(f"最低 MAE: {best_mae['config_name']} (MAE: {best_mae['mae']:.6f})")
            print(f"最高 R²: {best_r2['config_name']} (R²: {best_r2['r2']:.6f})")
            
            print(f"\n📈 结果已保存到: timemixer_evaluation_results.csv")
        else:
            print(f"\n❌ 没有成功的训练结果")
    
    elif choice == "3":
        # 查看配置详情
        print(f"\n📖 配置详情:")
        for i, config in enumerate(my_configs, 1):
            print(f"\n{i}. {config['name']}")
            print("-" * 30)
            for key, value in config.items():
                if key != 'name':
                    print(f"  {key}: {value}")
        
        print(f"\n💡 要修改这些参数，请编辑 my_parameters.py 文件")
    
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
