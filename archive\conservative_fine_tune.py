"""
保守精细微调
============

基于配置17进行最小改动的微调，争取稳定提升R²
"""

import os
from datetime import datetime

def run_conservative_fine_tune():
    """运行保守的精细微调"""
    print("🔧 保守精细微调 - 最小改动策略")
    print("="*50)
    
    # 基于配置17，只做最小的改动
    conservative_config = {
        'name': '保守微调_最小改动',
        # 保持配置17的所有成功参数
        'n_steps': 90,                    # 保持
        'n_pred_steps': 5,                # 保持
        'n_layers': 3,                    # 保持
        'd_model': 256,                   # 保持
        'd_ffn': 512,                     # 保持
        'top_k': 10,                      # 保持
        'moving_avg': 7,                  # 保持
        'downsampling_window': 3,         # 保持
        'downsampling_layers': 2,         # 保持
        'use_norm': True,                 # 保持
        'batch_size': 32,                 # 保持
        'device': 'cuda',                 # 保持
        'num_workers': 0,                 # 保持
        
        # 只微调这几个关键参数
        'learning_rate': 2.8e-4,          # 微调: 3e-4 → 2.8e-4 (-6.7%)
        'dropout': 0.19,                  # 微调: 0.2 → 0.19 (-5%)
        'epochs': 220,                    # 微调: 200 → 220 (+10%)
        'patience': 35                    # 微调: 30 → 35 (+16.7%)
    }
    
    print(f"🔧 微调策略:")
    print(f"  • 保持配置17的所有架构参数")
    print(f"  • 只微调训练相关参数")
    print(f"  • 学习率: 3e-4 → 2.8e-4 (-6.7%)")
    print(f"  • dropout: 0.2 → 0.19 (-5%)")
    print(f"  • epochs: 200 → 220 (+10%)")
    print(f"  • patience: 30 → 35 (+16.7%)")
    
    try:
        import enhanced_compatible_training_runner
        
        print(f"\n🔥 开始保守微调训练...")
        start_time = datetime.now()
        
        result = enhanced_compatible_training_runner.run_compatible_training(conservative_config)
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        if result:
            print(f"\n✅ 保守微调完成! 用时: {duration:.1f}秒")
            
            # 读取结果
            try:
                import pandas as pd
                df = pd.read_csv('timemixer_evaluation_results.csv')
                latest_result = df.iloc[-1]
                r2_value = latest_result['R2']
                
                print(f"\n🎯 保守微调结果:")
                print(f"  R² Score: {r2_value:.4f}")
                print(f"  MAE: {latest_result['MAE']:.2f}")
                print(f"  RMSE: {latest_result['RMSE']:.2f}")
                
                # 与基线对比
                baseline_r2 = 0.7301
                improvement = r2_value - baseline_r2
                
                print(f"\n📈 与配置17对比:")
                print(f"  基线R²: {baseline_r2:.4f}")
                print(f"  当前R²: {r2_value:.4f}")
                print(f"  提升: {improvement:+.4f} ({improvement/baseline_r2*100:+.1f}%)")
                
                if r2_value > 0.8:
                    print(f"\n🎉 恭喜！保守策略成功突破R²=0.8！")
                    return True, r2_value
                elif r2_value > 0.78:
                    print(f"\n🔥 非常接近！R²>0.78")
                    return False, r2_value
                elif r2_value > baseline_r2:
                    print(f"\n📈 保守策略有效！超过基线")
                    return False, r2_value
                else:
                    print(f"\n📊 保守策略效果有限")
                    return False, r2_value
                
            except Exception as e:
                print(f"📊 结果读取失败: {e}")
                return False, None
        else:
            print(f"❌ 保守微调失败")
            return False, None
            
    except Exception as e:
        print(f"❌ 训练过程出错: {e}")
        return False, None

def run_ultra_conservative():
    """运行超保守微调 - 几乎不改动"""
    print("\n🛡️ 超保守微调 - 几乎不改动")
    print("="*50)
    
    # 几乎完全复制配置17，只做最微小的改动
    ultra_conservative_config = {
        'name': '超保守微调_几乎不变',
        # 完全保持配置17的参数
        'n_steps': 90,
        'n_pred_steps': 5,
        'n_layers': 3,
        'd_model': 256,
        'd_ffn': 512,
        'top_k': 10,
        'moving_avg': 7,
        'downsampling_window': 3,
        'downsampling_layers': 2,
        'use_norm': True,
        'batch_size': 32,
        'device': 'cuda',
        'num_workers': 0,
        'dropout': 0.2,                   # 保持不变
        
        # 只改动这两个参数
        'learning_rate': 2.9e-4,          # 微调: 3e-4 → 2.9e-4 (-3.3%)
        'epochs': 210,                    # 微调: 200 → 210 (+5%)
        'patience': 32                    # 微调: 30 → 32 (+6.7%)
    }
    
    print(f"🔧 超保守策略:")
    print(f"  • 几乎完全复制配置17")
    print(f"  • 学习率: 3e-4 → 2.9e-4 (-3.3%)")
    print(f"  • epochs: 200 → 210 (+5%)")
    print(f"  • patience: 30 → 32 (+6.7%)")
    
    try:
        import enhanced_compatible_training_runner
        
        print(f"\n🔥 开始超保守微调训练...")
        start_time = datetime.now()
        
        result = enhanced_compatible_training_runner.run_compatible_training(ultra_conservative_config)
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        if result:
            print(f"\n✅ 超保守微调完成! 用时: {duration:.1f}秒")
            
            # 读取结果
            try:
                import pandas as pd
                df = pd.read_csv('timemixer_evaluation_results.csv')
                latest_result = df.iloc[-1]
                r2_value = latest_result['R2']
                
                print(f"\n🎯 超保守微调结果:")
                print(f"  R² Score: {r2_value:.4f}")
                print(f"  MAE: {latest_result['MAE']:.2f}")
                print(f"  RMSE: {latest_result['RMSE']:.2f}")
                
                # 与基线对比
                baseline_r2 = 0.7301
                improvement = r2_value - baseline_r2
                
                print(f"\n📈 与配置17对比:")
                print(f"  基线R²: {baseline_r2:.4f}")
                print(f"  当前R²: {r2_value:.4f}")
                print(f"  提升: {improvement:+.4f} ({improvement/baseline_r2*100:+.1f}%)")
                
                if r2_value > 0.8:
                    print(f"\n🎉 恭喜！超保守策略成功突破R²=0.8！")
                    return True, r2_value
                elif r2_value > 0.78:
                    print(f"\n🔥 非常接近！R²>0.78")
                    return False, r2_value
                elif r2_value > baseline_r2:
                    print(f"\n📈 超保守策略有效！")
                    return False, r2_value
                else:
                    print(f"\n📊 需要其他策略")
                    return False, r2_value
                
            except Exception as e:
                print(f"📊 结果读取失败: {e}")
                return False, None
        else:
            print(f"❌ 超保守微调失败")
            return False, None
            
    except Exception as e:
        print(f"❌ 训练过程出错: {e}")
        return False, None

def main():
    """主函数"""
    print("🛡️ 保守精细微调程序")
    print("="*50)
    
    print("📊 策略:")
    print("  • 基于配置17的成功")
    print("  • 最小改动，降低风险")
    print("  • 稳步提升R²值")
    
    # 先尝试保守微调
    success1, r2_1 = run_conservative_fine_tune()
    
    if success1:
        print(f"\n🎉 保守微调成功突破R²=0.8！")
        return
    
    # 如果保守微调没有突破，尝试超保守
    if r2_1 and r2_1 < 0.78:
        print(f"\n🤔 保守微调效果有限，尝试超保守策略...")
        success2, r2_2 = run_ultra_conservative()
        
        if success2:
            print(f"\n🎉 超保守微调成功突破R²=0.8！")
        else:
            print(f"\n💭 分析:")
            if r2_1 and r2_2:
                best_r2 = max(r2_1, r2_2)
                print(f"  最佳R²: {best_r2:.4f}")
                gap = 0.8 - best_r2
                print(f"  距离目标: {gap:.4f}")
                
                if gap < 0.05:
                    print(f"  💡 建议: 尝试集成学习或更多训练轮次")
                else:
                    print(f"  💡 建议: 可能需要更多水文学特征或不同的模型架构")

if __name__ == "__main__":
    main()
