"""
TimeMixer++ 训练启动器
=====================

一个简单的启动脚本，帮您选择合适的训练方式。
"""

import os
import pandas as pd

def show_banner():
    """显示欢迎横幅"""
    print("="*60)
    print("    TimeMixer++ 训练系统")
    print("    时间序列预测模型训练平台")
    print("="*60)

def check_data_file():
    """检查数据文件是否存在"""
    data_file = '1964-2017dailyRunoff.csv'
    if os.path.exists(data_file):
        print(f"✓ 数据文件已找到: {data_file}")
        return True
    else:
        print(f"✗ 数据文件未找到: {data_file}")
        print("请确保数据文件在当前目录中")
        return False

def show_previous_results():
    """显示之前的训练结果"""
    csv_file = 'timemixer_evaluation_results.csv'
    if os.path.exists(csv_file):
        try:
            df = pd.read_csv(csv_file)
            print(f"\n📊 历史训练结果 (共{len(df)}次训练):")
            print("-" * 50)
            
            if len(df) > 0:
                # 显示最近3次结果
                recent = df.tail(3)
                for _, row in recent.iterrows():
                    print(f"{row['Training_ID']}: MAE={row['MAE']:.4f}, R²={row['R2']:.4f}")
                
                # 显示最佳结果
                best_mae = df.loc[df['MAE'].idxmin()]
                best_r2 = df.loc[df['R2'].idxmax()]
                
                print(f"\n🏆 最佳结果:")
                print(f"  最低MAE: {best_mae['Training_ID']} (MAE: {best_mae['MAE']:.4f})")
                print(f"  最高R²: {best_r2['Training_ID']} (R²: {best_r2['R2']:.4f})")
            
        except Exception as e:
            print(f"读取历史结果时出错: {e}")
    else:
        print("\n📊 暂无历史训练结果")

def check_gpu_available():
    """检查GPU是否可用"""
    try:
        import torch
        return torch.cuda.is_available()
    except:
        return False

def show_training_options():
    """显示训练选项"""
    print(f"\n🚀 可用的训练选项:")
    print("="*50)

    gpu_available = check_gpu_available()

    options = [
        ("1", "快速验证测试", "python tests/quick_test_training.py", "3轮训练，约10秒，验证系统"),
        ("2", "兼容模式训练", "python compatible_training_runner.py", "适用于当前环境，多种配置选择"),
    ]

    if gpu_available:
        options.append(("3", "🎮 GPU加速训练", "python gpu_training_runner.py", "使用显卡加速，4种GPU优化配置"))
        options.extend([
            ("4", "查看训练参数", "python advanced_parameter_configs.py", "查看所有可调参数说明"),
            ("5", "查看历史结果", "查看CSV文件", "分析之前的训练结果"),
            ("6", "环境诊断", "python utils/check_gpu.py", "检查PyPOTS版本和依赖"),
        ])
    else:
        options.extend([
            ("3", "查看训练参数", "python advanced_parameter_configs.py", "查看所有可调参数说明"),
            ("4", "查看历史结果", "查看CSV文件", "分析之前的训练结果"),
            ("5", "环境诊断", "python utils/check_gpu.py", "检查PyPOTS版本和依赖"),
        ])

    for num, name, cmd, desc in options:
        print(f"{num}. {name}")
        print(f"   命令: {cmd}")
        print(f"   说明: {desc}")
        print()

    if gpu_available:
        print("🎮 检测到GPU可用！推荐使用选项3进行GPU加速训练")
    else:
        print("⚠ 未检测到GPU，建议使用CPU训练选项")

def run_environment_check():
    """运行环境检查"""
    print("\n🔍 环境诊断:")
    print("-" * 30)
    
    # 检查Python版本
    import sys
    print(f"Python版本: {sys.version.split()[0]}")
    
    # 检查关键包
    packages = ['numpy', 'pandas', 'sklearn', 'torch', 'pypots']
    for pkg in packages:
        try:
            module = __import__(pkg)
            version = getattr(module, '__version__', 'unknown')
            print(f"✓ {pkg}: {version}")
        except ImportError:
            print(f"✗ {pkg}: 未安装")
    
    # 检查GPU
    try:
        import torch
        if torch.cuda.is_available():
            print(f"✓ GPU: {torch.cuda.get_device_name(0)}")
        else:
            print("⚠ GPU: 不可用 (使用CPU)")
    except:
        print("? GPU: 无法检测")
    
    print(f"\n💡 建议:")
    print(f"- 当前环境适合使用兼容模式训练")
    print(f"- 运行选项2进行训练实验")

def view_results():
    """查看详细结果"""
    csv_file = 'timemixer_evaluation_results.csv'
    if os.path.exists(csv_file):
        try:
            df = pd.read_csv(csv_file)
            print(f"\n📈 详细训练结果:")
            print("="*80)
            
            # 显示表格
            print(f"{'训练ID':<12} {'时间':<19} {'MAE':<10} {'RMSE':<10} {'NSE':<8} {'R²':<8}")
            print("-" * 80)
            
            for _, row in df.iterrows():
                print(f"{row['Training_ID']:<12} {row['Timestamp']:<19} "
                      f"{row['MAE']:<10.4f} {row['RMSE']:<10.4f} "
                      f"{row['NSE']:<8.4f} {row['R2']:<8.4f}")
            
            # 统计信息
            print(f"\n📊 统计信息:")
            print(f"总训练次数: {len(df)}")
            print(f"平均MAE: {df['MAE'].mean():.4f}")
            print(f"平均R²: {df['R2'].mean():.4f}")
            print(f"MAE范围: {df['MAE'].min():.4f} - {df['MAE'].max():.4f}")
            print(f"R²范围: {df['R2'].min():.4f} - {df['R2'].max():.4f}")
            
        except Exception as e:
            print(f"读取结果文件时出错: {e}")
    else:
        print("暂无训练结果文件")

def main():
    """主函数"""
    show_banner()
    
    # 检查数据文件
    if not check_data_file():
        print("\n❌ 请先准备数据文件后再运行训练")
        return
    
    # 显示历史结果
    show_previous_results()
    
    # 显示选项
    show_training_options()
    
    # 用户选择
    gpu_available = check_gpu_available()
    max_choice = 6 if gpu_available else 5

    while True:
        choice = input(f"请选择操作 (1-{max_choice}, q退出): ").strip().lower()

        if choice == 'q':
            print("再见！")
            break
        elif choice == '1':
            print("\n🚀 启动快速验证测试...")
            os.system("python tests/quick_test_training.py")
            break
        elif choice == '2':
            print("\n🚀 启动兼容模式训练...")
            os.system("python compatible_training_runner.py")
            break
        elif choice == '3':
            if gpu_available:
                print("\n🎮 启动GPU加速训练...")
                os.system("python gpu_training_runner.py")
                break
            else:
                print("\n📖 查看训练参数说明...")
                os.system("python advanced_parameter_configs.py")
                input("\n按回车键返回主菜单...")
        elif choice == '4':
            if gpu_available:
                print("\n📖 查看训练参数说明...")
                os.system("python advanced_parameter_configs.py")
                input("\n按回车键返回主菜单...")
            else:
                view_results()
                input("\n按回车键返回主菜单...")
        elif choice == '5':
            if gpu_available:
                view_results()
                input("\n按回车键返回主菜单...")
            else:
                run_environment_check()
                input("\n按回车键返回主菜单...")
        elif choice == '6' and gpu_available:
            print("\n🔍 启动环境诊断...")
            os.system("python utils/check_gpu.py")
            input("\n按回车键返回主菜单...")
        else:
            print("无效选择，请重试")

if __name__ == "__main__":
    main()
