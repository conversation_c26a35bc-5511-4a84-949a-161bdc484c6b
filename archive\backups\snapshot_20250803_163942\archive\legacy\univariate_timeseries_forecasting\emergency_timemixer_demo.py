"""
Emergency TimeMixer Demo
========================

This script uses a minimal TimeMixer implementation that works
even when PyPOTS has dependency conflicts.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# Try to import the minimal TimeMixer
try:
    from minimal_timemixer import MinimalTimeMixer
    TIMEMIXER_AVAILABLE = True
    print("✅ Minimal TimeMixer imported successfully")
except ImportError:
    TIMEMIXER_AVAILABLE = False
    print("❌ Minimal TimeMixer not available. Run emergency_fix.py first.")

def load_runoff_data():
    """Load the runoff data."""
    print("\n🌊 Loading Runoff Data")
    print("=" * 30)
    
    file_path = r'C:\Users\<USER>\Desktop\timemix\1964-2017dailyRunoff.csv'
    
    try:
        data = pd.read_excel(file_path)
        data['DATA'] = pd.to_datetime(data['DATA'])
        data = data.sort_values('DATA').reset_index(drop=True)
        
        print(f"✅ Data loaded: {data.shape}")
        print(f"📅 Range: {data['DATA'].min().date()} to {data['DATA'].max().date()}")
        print(f"💧 Runoff: {data['runoff'].min():.1f} - {data['runoff'].max():.1f}")
        
        return data
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def prepare_data_for_minimal_timemixer(data, n_steps=30, n_pred_steps=7):
    """Prepare data for the minimal TimeMixer."""
    print(f"\n🔧 Preparing Data for Minimal TimeMixer")
    print(f"   📥 Input: {n_steps} days")
    print(f"   📤 Output: {n_pred_steps} days")
    
    values = data['runoff'].values
    
    # Create sequences
    X = []
    for i in range(len(values) - n_steps - n_pred_steps + 1):
        X.append(values[i:(i + n_steps)])
    
    X = np.array(X).reshape(-1, n_steps, 1)
    
    # Split data chronologically
    n_train = int(0.7 * len(X))
    n_val = int(0.2 * len(X))
    
    train_X = X[:n_train]
    val_X = X[n_train:n_train+n_val]
    test_X = X[n_train+n_val:]
    
    print(f"   ✅ Prepared: Train={len(train_X)}, Val={len(val_X)}, Test={len(test_X)}")
    
    return train_X, val_X, test_X

def run_minimal_timemixer_forecasting(train_X, val_X, test_X):
    """Run forecasting with minimal TimeMixer."""
    print(f"\n🤖 Running Minimal TimeMixer Forecasting")
    print("=" * 50)
    
    if not TIMEMIXER_AVAILABLE:
        print("❌ Minimal TimeMixer not available")
        return None
    
    try:
        # Create model
        model = MinimalTimeMixer(
            n_steps=30,
            n_pred_steps=7,
            d_model=64,  # Smaller for faster training
            n_layers=2
        )
        
        print("✅ Model created")
        
        # Prepare data
        train_data = {'X': train_X}
        val_data = {'X': val_X}
        test_data = {'X': test_X}
        
        # Train model
        print("\n🚀 Training model...")
        model.fit(train_data, val_data, epochs=15)
        
        # Make predictions
        print("\n🔮 Making predictions...")
        results = model.predict(test_data)
        predictions = results['forecasting']
        
        print(f"✅ Predictions completed: {predictions.shape}")
        
        # Save model
        try:
            model.save("minimal_timemixer_runoff.pth")
            print("✅ Model saved")
        except:
            print("⚠️ Could not save model")
        
        return predictions
        
    except Exception as e:
        print(f"❌ Forecasting failed: {e}")
        return None

def visualize_minimal_predictions(predictions):
    """Visualize predictions from minimal TimeMixer."""
    print(f"\n📊 Creating Visualizations")
    
    try:
        # Plot first 4 samples
        fig, axes = plt.subplots(2, 2, figsize=(12, 8))
        axes = axes.flatten()
        
        for i in range(min(4, len(predictions))):
            ax = axes[i]
            days = range(len(predictions[i]))
            ax.plot(days, predictions[i].flatten(), 'b-', linewidth=2, marker='o')
            ax.set_title(f'Sample {i+1}: 7-Day Runoff Forecast')
            ax.set_xlabel('Days')
            ax.set_ylabel('Runoff')
            ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('minimal_timemixer_results.png', dpi=300, bbox_inches='tight')
        print("✅ Saved: minimal_timemixer_results.png")
        plt.show()
        
        # Calculate basic statistics
        print(f"\n📈 Prediction Statistics:")
        print(f"   Mean prediction: {np.mean(predictions):.2f}")
        print(f"   Std prediction: {np.std(predictions):.2f}")
        print(f"   Min prediction: {np.min(predictions):.2f}")
        print(f"   Max prediction: {np.max(predictions):.2f}")
        
    except Exception as e:
        print(f"⚠️ Visualization failed: {e}")

def fallback_simple_forecasting(train_X, test_X):
    """Fallback simple forecasting if TimeMixer fails."""
    print(f"\n🔄 Fallback: Simple Moving Average Forecasting")
    
    try:
        from sklearn.linear_model import LinearRegression
        
        # Prepare data for simple regression
        X_train_flat = train_X.reshape(len(train_X), -1)
        
        # Create simple targets (next 7 values)
        y_train = []
        for i in range(len(train_X) - 7):
            # Use the last 7 values of the next sequence as target
            next_seq = train_X[i + 1]
            if len(next_seq) >= 7:
                y_train.append(next_seq[-7:].flatten())
        
        y_train = np.array(y_train)
        X_train_simple = X_train_flat[:len(y_train)]
        
        # Train simple model
        model = LinearRegression()
        model.fit(X_train_simple, y_train)
        
        # Make predictions
        X_test_flat = test_X.reshape(len(test_X), -1)
        predictions = model.predict(X_test_flat)
        predictions = predictions.reshape(-1, 7, 1)
        
        print(f"✅ Simple forecasting completed: {predictions.shape}")
        
        # Visualize
        fig, axes = plt.subplots(2, 2, figsize=(12, 8))
        axes = axes.flatten()
        
        for i in range(min(4, len(predictions))):
            ax = axes[i]
            days = range(len(predictions[i]))
            ax.plot(days, predictions[i].flatten(), 'r--', linewidth=2, marker='s')
            ax.set_title(f'Sample {i+1}: Simple Forecast')
            ax.set_xlabel('Days')
            ax.set_ylabel('Runoff')
            ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('simple_forecasting_results.png', dpi=300, bbox_inches='tight')
        print("✅ Saved: simple_forecasting_results.png")
        plt.show()
        
        return predictions
        
    except Exception as e:
        print(f"❌ Fallback forecasting failed: {e}")
        return None

def main():
    """Main function."""
    print("🚨 EMERGENCY TIMEMIXER DEMO")
    print("=" * 40)
    print("This demo works even with PyPOTS dependency issues")
    print()
    
    # Load data
    data = load_runoff_data()
    if data is None:
        return
    
    # Prepare data
    train_X, val_X, test_X = prepare_data_for_minimal_timemixer(data)
    
    # Try minimal TimeMixer first
    predictions = None
    if TIMEMIXER_AVAILABLE:
        predictions = run_minimal_timemixer_forecasting(train_X, val_X, test_X)
    
    # If minimal TimeMixer worked, visualize
    if predictions is not None:
        visualize_minimal_predictions(predictions)
        
        print(f"\n🎉 SUCCESS WITH MINIMAL TIMEMIXER!")
        print("=" * 50)
        print("✅ Minimal TimeMixer forecasting completed")
        print("📁 Generated files:")
        print("   - minimal_timemixer_results.png")
        print("   - minimal_timemixer_runoff.pth")
        
    else:
        # Fallback to simple forecasting
        print(f"\n🔄 Trying fallback simple forecasting...")
        fallback_predictions = fallback_simple_forecasting(train_X, test_X)
        
        if fallback_predictions is not None:
            print(f"\n✅ SUCCESS WITH SIMPLE FORECASTING!")
            print("=" * 50)
            print("📁 Generated: simple_forecasting_results.png")
        else:
            print(f"\n❌ All forecasting methods failed")
            print("Please run: python emergency_fix.py")
    
    print(f"\n💡 Next steps:")
    print("1. If minimal TimeMixer worked, you have a working solution!")
    print("2. If not, run 'python emergency_fix.py' to set up minimal TimeMixer")
    print("3. Consider creating a new conda environment for best results")

if __name__ == "__main__":
    main()
