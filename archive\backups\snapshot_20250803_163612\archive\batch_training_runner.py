"""
增强版批量训练运行器
==================

支持GPU训练和更多参数控制的批量训练系统。
"""

from single_training_runner import run_timemixer_training
import time
import torch
import itertools

def check_device():
    """检查可用设备"""
    if torch.cuda.is_available():
        device = 'cuda'
        print(f"✓ 检测到GPU: {torch.cuda.get_device_name(0)}")
        print(f"✓ GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    else:
        device = 'cpu'
        print("⚠ 未检测到GPU，将使用CPU训练")
    return device

def generate_parameter_grid():
    """生成参数网格搜索配置"""

    # 定义参数搜索空间
    param_grid = {
        'n_steps': [24, 48, 72, 96],
        'n_pred_steps': [6, 12, 18, 24],
        'n_layers': [1, 2, 3],
        'd_model': [32, 64, 96, 128],
        'd_ffn': [64, 128, 192, 256],
        'top_k': [3, 5, 7],
        'moving_avg': [15, 25, 35],
        'downsampling_window': [2, 3, 4],
        'downsampling_layers': [1, 2],
        'dropout': [0.1, 0.15, 0.2, 0.25],
        'epochs': [5, 8, 10, 15, 20],
        'learning_rate': [5e-4, 8e-4, 1e-3, 2e-3],
        'patience': [3, 5, 8],
        'batch_size': [16, 32, 64],
        'use_norm': [True, False]
    }

    return param_grid

def create_custom_parameter_sets():
    """创建自定义参数配置集合"""

    device = check_device()

    # 基础配置模板
    base_config = {
        'device': device,
        'use_norm': True,
        'term': 'short',
        'patience': 5,
        'batch_size': 32
    }

    # 定义多个实验配置
    parameter_sets = [
        # 实验组1: 小模型快速实验
        {**base_config, 'name': '小模型_短序列_GPU', 'n_steps': 24, 'n_pred_steps': 6, 'n_layers': 1, 'd_model': 32, 'd_ffn': 64, 'top_k': 3, 'moving_avg': 15, 'dropout': 0.1, 'epochs': 5, 'learning_rate': 1e-3},
        {**base_config, 'name': '小模型_中序列_GPU', 'n_steps': 48, 'n_pred_steps': 12, 'n_layers': 1, 'd_model': 32, 'd_ffn': 64, 'top_k': 3, 'moving_avg': 25, 'dropout': 0.1, 'epochs': 5, 'learning_rate': 1e-3},
        {**base_config, 'name': '小模型_长序列_GPU', 'n_steps': 72, 'n_pred_steps': 18, 'n_layers': 1, 'd_model': 32, 'd_ffn': 64, 'top_k': 3, 'moving_avg': 35, 'dropout': 0.1, 'epochs': 5, 'learning_rate': 1e-3},

        # 实验组2: 中等模型实验
        {**base_config, 'name': '中模型_标准配置_GPU', 'n_steps': 48, 'n_pred_steps': 12, 'n_layers': 2, 'd_model': 64, 'd_ffn': 128, 'top_k': 5, 'moving_avg': 25, 'dropout': 0.15, 'epochs': 8, 'learning_rate': 8e-4},
        {**base_config, 'name': '中模型_高dropout_GPU', 'n_steps': 48, 'n_pred_steps': 12, 'n_layers': 2, 'd_model': 64, 'd_ffn': 128, 'top_k': 5, 'moving_avg': 25, 'dropout': 0.25, 'epochs': 8, 'learning_rate': 8e-4},
        {**base_config, 'name': '中模型_大batch_GPU', 'n_steps': 48, 'n_pred_steps': 12, 'n_layers': 2, 'd_model': 64, 'd_ffn': 128, 'top_k': 5, 'moving_avg': 25, 'dropout': 0.15, 'epochs': 8, 'learning_rate': 8e-4, 'batch_size': 64},

        # 实验组3: 大模型实验
        {**base_config, 'name': '大模型_深层网络_GPU', 'n_steps': 96, 'n_pred_steps': 24, 'n_layers': 3, 'd_model': 128, 'd_ffn': 256, 'top_k': 7, 'moving_avg': 35, 'dropout': 0.2, 'epochs': 10, 'learning_rate': 5e-4},
        {**base_config, 'name': '大模型_超大维度_GPU', 'n_steps': 96, 'n_pred_steps': 24, 'n_layers': 2, 'd_model': 256, 'd_ffn': 512, 'top_k': 7, 'moving_avg': 35, 'dropout': 0.2, 'epochs': 10, 'learning_rate': 5e-4, 'batch_size': 16},

        # 实验组4: 学习率实验
        {**base_config, 'name': '学习率_高_GPU', 'n_steps': 48, 'n_pred_steps': 12, 'n_layers': 2, 'd_model': 64, 'd_ffn': 128, 'top_k': 5, 'moving_avg': 25, 'dropout': 0.15, 'epochs': 8, 'learning_rate': 2e-3},
        {**base_config, 'name': '学习率_低_GPU', 'n_steps': 48, 'n_pred_steps': 12, 'n_layers': 2, 'd_model': 64, 'd_ffn': 128, 'top_k': 5, 'moving_avg': 25, 'dropout': 0.15, 'epochs': 8, 'learning_rate': 5e-4},

        # 实验组5: 架构参数实验
        {**base_config, 'name': '高top_k_GPU', 'n_steps': 48, 'n_pred_steps': 12, 'n_layers': 2, 'd_model': 64, 'd_ffn': 128, 'top_k': 10, 'moving_avg': 25, 'dropout': 0.15, 'epochs': 8, 'learning_rate': 1e-3},
        {**base_config, 'name': '大moving_avg_GPU', 'n_steps': 48, 'n_pred_steps': 12, 'n_layers': 2, 'd_model': 64, 'd_ffn': 128, 'top_k': 5, 'moving_avg': 50, 'dropout': 0.15, 'epochs': 8, 'learning_rate': 1e-3},
        {**base_config, 'name': '多下采样层_GPU', 'n_steps': 96, 'n_pred_steps': 24, 'n_layers': 2, 'd_model': 64, 'd_ffn': 128, 'top_k': 5, 'moving_avg': 25, 'dropout': 0.15, 'epochs': 8, 'learning_rate': 1e-3, 'downsampling_layers': 3},

        # 实验组6: 长训练实验
        {**base_config, 'name': '长训练_小模型_GPU', 'n_steps': 48, 'n_pred_steps': 12, 'n_layers': 1, 'd_model': 32, 'd_ffn': 64, 'top_k': 5, 'moving_avg': 25, 'dropout': 0.1, 'epochs': 20, 'learning_rate': 1e-3, 'patience': 8},
        {**base_config, 'name': '长训练_中模型_GPU', 'n_steps': 48, 'n_pred_steps': 12, 'n_layers': 2, 'd_model': 64, 'd_ffn': 128, 'top_k': 5, 'moving_avg': 25, 'dropout': 0.15, 'epochs': 20, 'learning_rate': 8e-4, 'patience': 8},
    ]

    return parameter_sets

def create_grid_search_configs(max_configs=20):
    """创建网格搜索配置（限制数量避免过多实验）"""

    device = check_device()
    param_grid = generate_parameter_grid()

    # 选择关键参数进行网格搜索
    key_params = {
        'n_steps': [24, 48, 96],
        'n_layers': [1, 2, 3],
        'd_model': [32, 64, 128],
        'learning_rate': [5e-4, 1e-3, 2e-3],
        'dropout': [0.1, 0.15, 0.2]
    }

    # 固定其他参数
    fixed_params = {
        'device': device,
        'n_pred_steps': 12,  # 固定预测步长
        'd_ffn': None,  # 将根据d_model自动设置
        'top_k': 5,
        'moving_avg': 25,
        'downsampling_window': 2,
        'downsampling_layers': 1,
        'epochs': 8,
        'patience': 5,
        'batch_size': 32,
        'use_norm': True,
        'term': 'short'
    }

    # 生成所有组合
    param_names = list(key_params.keys())
    param_values = list(key_params.values())

    configs = []
    for i, combination in enumerate(itertools.product(*param_values)):
        if i >= max_configs:  # 限制配置数量
            break

        config = fixed_params.copy()
        for name, value in zip(param_names, combination):
            config[name] = value

        # 自动设置d_ffn为d_model的2倍
        config['d_ffn'] = config['d_model'] * 2

        # 根据n_steps设置n_pred_steps
        config['n_pred_steps'] = max(6, config['n_steps'] // 4)

        # 添加配置名称
        config['name'] = f"网格搜索_{i+1}"

        configs.append(config)

    return configs

def run_batch_training(mode='custom'):
    """运行批量训练

    Args:
        mode: 'custom' - 使用自定义配置
              'grid' - 使用网格搜索配置
    """

    print("TimeMixer++ 增强版批量训练运行器")
    print("="*60)

    # 检查设备
    device = check_device()

    if mode == 'custom':
        parameter_sets = create_custom_parameter_sets()
        print(f"使用自定义配置模式，共 {len(parameter_sets)} 个配置")
    elif mode == 'grid':
        parameter_sets = create_grid_search_configs()
        print(f"使用网格搜索模式，共 {len(parameter_sets)} 个配置")
    else:
        raise ValueError("mode must be 'custom' or 'grid'")

    print(f"设备: {device}")
    print(f"准备运行 {len(parameter_sets)} 个训练配置")

    # 显示配置预览
    print(f"\n配置预览:")
    for i, config in enumerate(parameter_sets[:3], 1):
        print(f"  {i}. {config.get('name', f'配置{i}')}")
        print(f"     n_steps={config['n_steps']}, n_layers={config['n_layers']}, d_model={config['d_model']}")
    if len(parameter_sets) > 3:
        print(f"  ... 还有 {len(parameter_sets) - 3} 个配置")

    input(f"\n按回车键开始训练...")

    results = []
    successful_trainings = 0
    failed_trainings = 0

    start_time = time.time()

    for i, params in enumerate(parameter_sets, 1):
        print(f"\n{'='*60}")
        print(f"进度: {i}/{len(parameter_sets)}")
        print(f"配置名称: {params.get('name', f'配置{i}')}")
        print(f"{'='*60}")

        # 显示关键参数
        key_params = ['n_steps', 'n_pred_steps', 'n_layers', 'd_model', 'd_ffn', 'learning_rate', 'dropout', 'epochs', 'device']
        print("关键参数:")
        for key in key_params:
            if key in params:
                print(f"  {key}: {params[key]}")

        config_start_time = time.time()

        try:
            result = run_timemixer_training(params)
            config_end_time = time.time()
            config_time = config_end_time - config_start_time

            if result:
                result['config_name'] = params.get('name', f'配置{i}')
                result['config_time'] = config_time
                results.append(result)
                successful_trainings += 1
                print(f"✓ {params.get('name', f'配置{i}')} 训练成功 (耗时: {config_time:.1f}s)")
                print(f"  MAE: {result['mae']:.4f}, R²: {result['r2']:.4f}")
            else:
                failed_trainings += 1
                print(f"✗ {params.get('name', f'配置{i}')} 训练失败")
        except Exception as e:
            failed_trainings += 1
            print(f"✗ {params.get('name', f'配置{i}')} 训练出现异常: {e}")

        # 显示进度
        elapsed_time = time.time() - start_time
        avg_time_per_config = elapsed_time / i
        remaining_configs = len(parameter_sets) - i
        estimated_remaining_time = avg_time_per_config * remaining_configs

        print(f"已用时间: {elapsed_time/60:.1f}分钟")
        print(f"预计剩余时间: {estimated_remaining_time/60:.1f}分钟")

        # 训练间隔，避免系统过载
        if i < len(parameter_sets):
            if device == 'cuda':
                print("等待 5 秒后开始下一个训练...")
                time.sleep(5)
            else:
                print("等待 10 秒后开始下一个训练...")
                time.sleep(10)
    
    # 总结结果
    print(f"\n{'='*60}")
    print("批量训练完成总结")
    print(f"{'='*60}")
    print(f"总配置数: {len(parameter_sets)}")
    print(f"成功训练: {successful_trainings}")
    print(f"失败训练: {failed_trainings}")
    
    if results:
        print(f"\n成功训练的结果:")
        print(f"{'训练ID':<15} {'MAE':<12} {'RMSE':<12} {'NSE':<12} {'R²':<12}")
        print("-" * 70)
        for result in results:
            print(f"{result['training_id']:<15} {result['mae']:<12.6f} {result['rmse']:<12.6f} {result['nse']:<12.6f} {result['r2']:<12.6f}")
        
        # 找出最佳结果
        best_mae = min(results, key=lambda x: x['mae'])
        best_r2 = max(results, key=lambda x: x['r2'])
        best_nse = max(results, key=lambda x: x['nse'])
        
        print(f"\n最佳结果:")
        print(f"最低 MAE: {best_mae['training_id']} (MAE: {best_mae['mae']:.6f})")
        print(f"最高 R²: {best_r2['training_id']} (R²: {best_r2['r2']:.6f})")
        print(f"最高 NSE: {best_nse['training_id']} (NSE: {best_nse['nse']:.6f})")
    
    print(f"\n所有结果已保存到: C:/Users/<USER>/Desktop/timemix/timemixer_evaluation_results.csv")
    
    return results

def main():
    """主函数"""
    results = run_batch_training()
    return results

if __name__ == "__main__":
    main()
