"""
测试100轮训练配置
=================

验证您修改的100轮配置是否正确工作
"""

from my_parameters import get_my_parameters

def test_config():
    """测试配置"""
    configs = get_my_parameters()
    config = configs[0]
    
    print("🎯 您的100轮训练配置")
    print("="*50)
    print(f"配置名称: {config['name']}")
    print(f"训练轮次: {config['epochs']}")
    print(f"输入序列: {config['n_steps']}")
    print(f"预测序列: {config['n_pred_steps']}")
    print(f"模型层数: {config['n_layers']}")
    print(f"模型维度: {config['d_model']}")
    print(f"学习率: {config['learning_rate']}")
    print(f"批次大小: {config['batch_size']}")
    print(f"设备: {config['device']}")
    print()
    
    if config['epochs'] == 100:
        print("✅ epochs参数已正确设置为100")
        print("现在运行训练时应该会显示100轮")
    else:
        print(f"❌ epochs参数不是100，当前值: {config['epochs']}")
    
    print("\n💡 要运行此配置，请使用:")
    print("python run_my_training.py")
    print("然后选择第1个配置")

if __name__ == "__main__":
    test_config()
