"""
Fix PyPOTS Dependencies Script
=============================

This script helps fix the common dependency conflicts with PyPOTS,
particularly the packaging version issue you encountered.
"""

import subprocess
import sys
import importlib

def run_command(command, description):
    """Run a pip command and handle errors."""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"   ✅ Success")
            return True
        else:
            print(f"   ⚠️  Warning: {result.stderr.strip()}")
            return False
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def check_package(package_name):
    """Check if a package is installed and get its version."""
    try:
        module = importlib.import_module(package_name)
        version = getattr(module, '__version__', 'Unknown')
        print(f"   ✅ {package_name}: {version}")
        return True
    except ImportError:
        print(f"   ❌ {package_name}: Not installed")
        return False
    except Exception as e:
        print(f"   ⚠️  {package_name}: Error - {e}")
        return False

def main():
    print("🔧 PyPOTS Dependency Fix Script")
    print("=" * 50)
    print("This script will fix the packaging version conflict and reinstall PyPOTS")
    print()
    
    # Step 1: Check current status
    print("1. Checking current package status...")
    packages_to_check = ['packaging', 'transformers', 'torch', 'numpy', 'pandas']
    
    for package in packages_to_check:
        check_package(package)
    
    print()
    
    # Step 2: Fix packaging issue
    print("2. Fixing packaging dependency...")
    commands = [
        ("pip install --upgrade pip", "Upgrading pip"),
        ("pip install --upgrade packaging", "Upgrading packaging"),
        ("pip install --upgrade setuptools wheel", "Upgrading build tools"),
    ]
    
    for command, description in commands:
        run_command(command, description)
    
    print()
    
    # Step 3: Fix transformers
    print("3. Fixing transformers dependency...")
    transformers_commands = [
        ("pip uninstall transformers -y", "Removing old transformers"),
        ("pip install transformers", "Installing fresh transformers"),
    ]
    
    for command, description in transformers_commands:
        run_command(command, description)
    
    print()
    
    # Step 4: Reinstall PyPOTS
    print("4. Reinstalling PyPOTS...")
    pypots_commands = [
        ("pip uninstall pypots -y", "Removing old PyPOTS"),
        ("pip install pypots", "Installing fresh PyPOTS"),
    ]
    
    for command, description in pypots_commands:
        run_command(command, description)
    
    print()
    
    # Step 5: Test installation
    print("5. Testing PyPOTS installation...")
    try:
        import pypots
        print(f"   ✅ PyPOTS imported successfully")
        print(f"   📦 Version: {pypots.__version__}")
        
        try:
            from pypots.forecasting.timemixer import TimeMixer
            print(f"   ✅ TimeMixer imported successfully")
            
            # Test model creation
            model = TimeMixer(
                n_steps=10,
                n_features=1,
                n_pred_steps=3,
                n_pred_features=1,
                n_layers=1,
                d_model=32,
                epochs=1,
                verbose=False
            )
            print(f"   ✅ TimeMixer model created successfully")
            
        except Exception as e:
            print(f"   ⚠️  TimeMixer test failed: {e}")
            
    except Exception as e:
        print(f"   ❌ PyPOTS test failed: {e}")
        print(f"   💡 You may need to restart your Python environment")
    
    print()
    print("=" * 50)
    print("🎉 Dependency fix completed!")
    print()
    print("📋 Next steps:")
    print("1. Restart your Python environment/IDE")
    print("2. Run: python basic_demo.py")
    print("3. If successful, run: python run_forecasting_demo.py")
    print()
    print("💡 If issues persist, try:")
    print("   - Create a new conda environment")
    print("   - Use: conda install pytorch pandas numpy matplotlib")
    print("   - Then: pip install pypots")

if __name__ == "__main__":
    main()
