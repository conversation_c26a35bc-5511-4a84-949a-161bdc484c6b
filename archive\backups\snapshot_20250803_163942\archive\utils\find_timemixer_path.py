import pypots.forecasting.timemixer
import os

# 找到TimeMixer模块的路径
module_path = pypots.forecasting.timemixer.__file__
module_dir = os.path.dirname(module_path)

print(f"TimeMixer模块路径: {module_path}")
print(f"TimeMixer目录: {module_dir}")

# 列出目录中的文件
print(f"\nTimeMixer目录中的文件:")
for file in os.listdir(module_dir):
    print(f"  {file}")

# 查找model.py文件
model_py_path = os.path.join(module_dir, "model.py")
if os.path.exists(model_py_path):
    print(f"\n找到model.py: {model_py_path}")
else:
    print(f"\nmodel.py不存在于: {model_py_path}")
