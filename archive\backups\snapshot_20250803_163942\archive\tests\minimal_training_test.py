"""
Minimal Training Test for TimeMixer++ PyPOTS Integration
=======================================================

This script provides a minimal test to ensure the model can train successfully.
"""

import numpy as np
import pandas as pd
import warnings
from pypots.optim import <PERSON>
from pypots.forecasting import TimeMixer
from pypots.nn.functional import calc_mae

warnings.filterwarnings('ignore')

def create_synthetic_data(n_samples=1000, n_steps=48, n_pred_steps=12):
    """Create synthetic time series data for testing."""
    print("Creating synthetic data for testing...")
    
    # Create a simple sine wave with noise
    total_length = n_samples + n_steps + n_pred_steps
    t = np.linspace(0, 10 * np.pi, total_length)
    data = np.sin(t) + 0.1 * np.random.randn(total_length)
    
    # Create samples
    total_sample_len = n_steps + n_pred_steps
    dataset_X = np.zeros((n_samples, total_sample_len, 1), dtype=np.float32)
    
    for i in range(n_samples):
        dataset_X[i, :, 0] = data[i:i + total_sample_len]
    
    print(f"Synthetic dataset shape: {dataset_X.shape}")
    return dataset_X

def split_data(data_X, n_steps, n_pred_steps, train_ratio=0.7, val_ratio=0.15):
    """Split data for forecasting."""
    n_total_samples = data_X.shape[0]
    train_end = int(n_total_samples * train_ratio)
    val_end = int(n_total_samples * (train_ratio + val_ratio))
    
    # Separate input and prediction parts
    X_input = data_X[:, :n_steps, :]
    X_pred = data_X[:, n_steps:, :]
    
    train_data = {'X': X_input[:train_end], 'X_pred': X_pred[:train_end]}
    val_data = {'X': X_input[train_end:val_end], 'X_pred': X_pred[train_end:val_end]}
    test_data = {'X': X_input[val_end:], 'X_pred': X_pred[val_end:]}
    
    return train_data, val_data, test_data

def test_minimal_training():
    """Test minimal training with synthetic data."""
    print("Minimal TimeMixer++ Training Test")
    print("="*50)
    
    # Parameters
    n_steps = 24
    n_pred_steps = 6
    n_samples = 500
    
    # Create synthetic data
    dataset_X = create_synthetic_data(n_samples, n_steps, n_pred_steps)
    
    # Split data
    train_data, val_data, test_data = split_data(dataset_X, n_steps, n_pred_steps)
    
    print(f"\nData split:")
    print(f"  Train: {train_data['X'].shape}")
    print(f"  Val: {val_data['X'].shape}")
    print(f"  Test: {test_data['X'].shape}")
    
    # Create model with minimal configuration
    print(f"\nCreating TimeMixer model...")
    model = TimeMixer(
        n_steps=n_steps,
        n_features=1,
        n_pred_steps=n_pred_steps,
        n_pred_features=1,
        term="short",
        n_layers=1,
        top_k=3,
        d_model=32,
        d_ffn=64,
        moving_avg=5,
        downsampling_window=2,
        downsampling_layers=1,
        use_norm=True,
        dropout=0.1,
        epochs=5,  # Very few epochs for quick test
        patience=3,
        optimizer=Adam(lr=1e-3),
        num_workers=0,
        device=None,
        saving_path="minimal_test_results",
        model_saving_strategy="best",
    )
    
    print(f"Model created successfully!")
    print(f"  Input steps: {model.n_steps}")
    print(f"  Prediction steps: {model.n_pred_steps}")
    print(f"  Features: {model.n_features}")
    
    # Train model
    print(f"\nStarting training...")
    try:
        model.fit(train_set=train_data, val_set=val_data)
        print("✓ Training completed successfully!")
        
        # Make predictions
        print(f"\nMaking predictions...")
        results = model.predict(test_data)
        predictions = results["forecasting"]
        
        # Calculate error
        true_values = test_data['X_pred']
        mae = calc_mae(predictions, true_values, np.ones_like(true_values, dtype=int))
        
        print(f"\nResults:")
        print(f"  Prediction shape: {predictions.shape}")
        print(f"  Test MAE: {mae:.6f}")
        
        print(f"\n✓ All tests passed! The model is working correctly.")
        return True
        
    except Exception as e:
        print(f"✗ Training failed with error: {e}")
        return False

def test_real_data():
    """Test with real data if available."""
    print("\n" + "="*50)
    print("Testing with Real Data")
    print("="*50)
    
    try:
        # Load real data
        df = pd.read_csv('1964-2017dailyRunoff.csv')
        df['DATA'] = pd.to_datetime(df['DATA'], format='%Y/%m/%d')
        df = df.set_index('DATA')
        
        # Get time series data (use only first 1000 points for quick test)
        time_series = df['runoff'].values[:1000].astype(np.float32)
        
        print(f"Real data length: {len(time_series)}")
        
        # Handle missing values
        if np.isnan(time_series).any():
            print("Filling missing values...")
            mean_value = np.nanmean(time_series)
            time_series = np.nan_to_num(time_series, nan=mean_value)
        
        # Parameters for quick test
        n_steps = 24
        n_pred_steps = 6
        
        # Create samples
        total_sample_len = n_steps + n_pred_steps
        n_samples = len(time_series) - total_sample_len + 1
        
        if n_samples <= 0:
            print("Data too short for real data test")
            return False
        
        # Use only first 200 samples for quick test
        n_samples = min(n_samples, 200)
        
        dataset_X = np.zeros((n_samples, total_sample_len, 1), dtype=np.float32)
        for i in range(n_samples):
            dataset_X[i, :, 0] = time_series[i:i + total_sample_len]
        
        print(f"Real dataset shape: {dataset_X.shape}")
        
        # Split data
        train_data, val_data, test_data = split_data(dataset_X, n_steps, n_pred_steps)
        
        # Create and train model
        model = TimeMixer(
            n_steps=n_steps,
            n_features=1,
            n_pred_steps=n_pred_steps,
            n_pred_features=1,
            term="short",
            n_layers=1,
            top_k=3,
            d_model=32,
            d_ffn=64,
            moving_avg=5,
            downsampling_window=2,
            downsampling_layers=1,
            use_norm=True,
            dropout=0.1,
            epochs=3,  # Very few epochs for quick test
            patience=2,
            optimizer=Adam(lr=1e-3),
            num_workers=0,
            device=None,
            saving_path="real_data_test_results",
            model_saving_strategy="best",
        )
        
        print(f"\nTraining on real data...")
        model.fit(train_set=train_data, val_set=val_data)
        
        # Make predictions
        results = model.predict(test_data)
        predictions = results["forecasting"]
        
        # Calculate error
        true_values = test_data['X_pred']
        mae = calc_mae(predictions, true_values, np.ones_like(true_values, dtype=int))
        
        print(f"\nReal data results:")
        print(f"  Prediction shape: {predictions.shape}")
        print(f"  Test MAE: {mae:.4f}")
        
        print(f"\n✓ Real data test passed!")
        return True
        
    except Exception as e:
        print(f"Real data test failed: {e}")
        return False

def main():
    """Main function."""
    print("TimeMixer++ Minimal Training Test")
    print("="*50)
    
    # Test 1: Synthetic data
    synthetic_ok = test_minimal_training()
    
    # Test 2: Real data (if available)
    real_data_ok = test_real_data()
    
    # Summary
    print("\n" + "="*50)
    print("Test Summary:")
    print(f"Synthetic data test: {'✓ PASS' if synthetic_ok else '✗ FAIL'}")
    print(f"Real data test: {'✓ PASS' if real_data_ok else '✗ FAIL'}")
    
    if synthetic_ok:
        print(f"\n✓ Core TimeMixer++ functionality is working!")
        print(f"The model can successfully train and make predictions.")
        print(f"\nYou can now:")
        print(f"1. Use the full training scripts with your data")
        print(f"2. Experiment with different configurations")
        print(f"3. Scale up to larger datasets and longer training")
    else:
        print(f"\n✗ There are issues with the basic functionality.")
        print(f"Please check the error messages above.")

if __name__ == "__main__":
    main()
