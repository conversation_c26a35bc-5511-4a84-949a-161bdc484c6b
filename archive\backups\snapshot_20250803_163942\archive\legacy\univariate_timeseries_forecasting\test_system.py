"""
System Test Script for Univariate Time Series Forecasting
=========================================================

This script tests all components of the forecasting system
to ensure everything works correctly.
"""

import sys
import os
import numpy as np
import pandas as pd
from pathlib import Path

def test_imports():
    """Test if all modules can be imported."""
    print("Testing module imports...")
    
    try:
        from data_loader import UnivariateTimeSeriesLoader, create_sample_data
        print("✓ data_loader imported successfully")
    except ImportError as e:
        print(f"✗ data_loader import failed: {e}")
        return False
    
    try:
        from model_config import (
            UnivariateTimeMixerConfig, 
            UnivariateModelFactory, 
            get_univariate_configs
        )
        print("✓ model_config imported successfully")
    except ImportError as e:
        print(f"✗ model_config import failed: {e}")
        return False
    
    try:
        from model_trainer import UnivariateModelTrainer
        print("✓ model_trainer imported successfully")
    except ImportError as e:
        print(f"✗ model_trainer import failed: {e}")
        return False
    
    return True


def test_data_loader():
    """Test the data loader functionality."""
    print("\nTesting data loader...")
    
    try:
        from data_loader import UnivariateTimeSeriesLoader, create_sample_data
        
        # Create sample data
        sample_file = create_sample_data("test_sample.csv")
        print("✓ Sample data created")
        
        # Test data loader
        loader = UnivariateTimeSeriesLoader()
        data = loader.load_csv(sample_file)
        print("✓ Data loaded successfully")
        
        # Test missing value check
        missing_info = loader.check_missing_values()
        print("✓ Missing value check completed")
        
        # Test normalization
        normalized_data = loader.normalize_data('minmax')
        print("✓ Data normalization completed")
        
        # Test sequence creation
        X, y = loader.create_sequences(normalized_data, sequence_length=10, prediction_length=3)
        print(f"✓ Sequences created: X{X.shape}, y{y.shape}")
        
        # Test data splitting
        train_data, val_data, test_data = loader.split_data(X, y)
        print("✓ Data splitting completed")
        
        # Clean up
        os.remove(sample_file)
        
        return True
        
    except Exception as e:
        print(f"✗ Data loader test failed: {e}")
        return False


def test_model_config():
    """Test the model configuration."""
    print("\nTesting model configuration...")
    
    try:
        from model_config import (
            UnivariateTimeMixerConfig, 
            get_univariate_configs,
            create_custom_univariate_config
        )
        
        # Test default config creation
        config = UnivariateTimeMixerConfig()
        print("✓ Default configuration created")
        
        # Test predefined configs
        configs = get_univariate_configs()
        print(f"✓ Predefined configurations loaded: {list(configs.keys())}")
        
        # Test custom config
        custom_config = create_custom_univariate_config(
            n_steps=20,
            n_pred_steps=5,
            epochs=5
        )
        print("✓ Custom configuration created")
        
        # Test config validation
        try:
            invalid_config = UnivariateTimeMixerConfig(n_features=2)  # Should fail
            print("✗ Config validation failed - invalid config accepted")
            return False
        except ValueError:
            print("✓ Config validation working correctly")
        
        return True
        
    except Exception as e:
        print(f"✗ Model config test failed: {e}")
        return False


def test_model_creation():
    """Test model creation (without PyPOTS)."""
    print("\nTesting model creation...")
    
    try:
        from model_config import UnivariateTimeMixerConfig, UnivariateModelFactory
        
        # Create a simple config
        config = UnivariateTimeMixerConfig(
            n_steps=10,
            n_pred_steps=3,
            epochs=1  # Very small for testing
        )
        
        # Try to create model (this will likely fail without PyPOTS)
        try:
            model = UnivariateModelFactory.create_forecasting_model(config)
            print("✓ Model created successfully")
            return True
        except ImportError:
            print("⚠ Model creation skipped - PyPOTS not available")
            print("  This is expected if PyPOTS is not installed")
            return True
        
    except Exception as e:
        print(f"✗ Model creation test failed: {e}")
        return False


def test_csv_format():
    """Test CSV format handling."""
    print("\nTesting CSV format handling...")
    
    try:
        # Create test CSV with your exact format
        test_data = {
            'DATA': ['1950/1/1', '1950/1/2', '1950/1/3', '1950/1/4', '1950/1/5'],
            'runoff': [1180, 1140, 1120, 1200, 1500]
        }
        
        test_df = pd.DataFrame(test_data)
        test_file = "test_format.csv"
        test_df.to_csv(test_file, index=False)
        print("✓ Test CSV file created")
        
        # Test loading
        from data_loader import UnivariateTimeSeriesLoader
        loader = UnivariateTimeSeriesLoader(
            date_column='DATA',
            target_column='runoff',
            date_format='%Y/%m/%d'
        )
        
        data = loader.load_csv(test_file)
        print("✓ CSV format loaded correctly")
        
        # Verify data
        assert len(data) == 5, "Wrong number of rows"
        assert 'DATA' in data.columns, "Date column missing"
        assert 'runoff' in data.columns, "Target column missing"
        print("✓ Data format verified")
        
        # Clean up
        os.remove(test_file)
        
        return True
        
    except Exception as e:
        print(f"✗ CSV format test failed: {e}")
        return False


def test_main_pipeline():
    """Test the main pipeline script."""
    print("\nTesting main pipeline...")
    
    try:
        # Test import
        import main_forecasting_pipeline
        print("✓ Main pipeline script imported")
        
        # Test validation function
        from main_forecasting_pipeline import validate_csv_file
        
        # Create a test file
        test_data = pd.DataFrame({
            'DATA': pd.date_range('2020-01-01', periods=100, freq='D').strftime('%Y/%m/%d'),
            'runoff': np.random.randint(1000, 2000, 100)
        })
        test_file = "pipeline_test.csv"
        test_data.to_csv(test_file, index=False)
        
        # Test validation
        is_valid = validate_csv_file(test_file)
        print(f"✓ CSV validation result: {is_valid}")
        
        # Clean up
        os.remove(test_file)
        
        return True
        
    except Exception as e:
        print(f"✗ Main pipeline test failed: {e}")
        return False


def run_all_tests():
    """Run all system tests."""
    print("Univariate Time Series Forecasting - System Test")
    print("=" * 60)
    
    tests = [
        ("Module Imports", test_imports),
        ("Data Loader", test_data_loader),
        ("Model Configuration", test_model_config),
        ("Model Creation", test_model_creation),
        ("CSV Format", test_csv_format),
        ("Main Pipeline", test_main_pipeline)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        icon = "✓" if result else "✗"
        print(f"{icon} {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! The system is ready to use.")
        print("\nNext steps:")
        print("1. Install PyPOTS: pip install pypots")
        print("2. Run: python main_forecasting_pipeline.py --use_sample")
    else:
        print(f"\n⚠ {total-passed} test(s) failed. Please check the errors above.")
    
    return passed == total


def create_your_data_template():
    """Create a template CSV file in your data format."""
    print("\nCreating data template...")
    
    # Create template with your exact format
    template_data = {
        'DATA': [
            '1950/1/1', '1950/1/2', '1950/1/3', '1950/1/4', '1950/1/5', '1950/1/6',
            '1950/1/7', '1950/1/8', '1950/1/9', '1950/1/10'
        ],
        'runoff': [1180, 1140, 1120, 1200, 1500, 1700, 1600, 1400, 1300, 1250]
    }
    
    template_df = pd.DataFrame(template_data)
    template_file = "your_data_template.csv"
    template_df.to_csv(template_file, index=False)
    
    print(f"✓ Template created: {template_file}")
    print("You can use this as a reference for your data format.")
    
    return template_file


if __name__ == "__main__":
    # Run system tests
    success = run_all_tests()
    
    # Create template file
    template_file = create_your_data_template()
    
    print(f"\n{'='*60}")
    print("SYSTEM TEST COMPLETED")
    print(f"{'='*60}")
    
    if success:
        print("✅ System is ready for use!")
    else:
        print("❌ Some tests failed. Please fix the issues before proceeding.")
    
    sys.exit(0 if success else 1)
