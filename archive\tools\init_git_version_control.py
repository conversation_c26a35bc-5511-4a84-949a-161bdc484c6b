"""
Git版本控制初始化工具
====================

为TimeMixer项目设置Git版本控制，创建合适的.gitignore文件，
并提交当前代码状态作为基线版本。
"""

import os
import subprocess
import sys
from pathlib import Path

class GitVersionControl:
    def __init__(self, project_root="."):
        self.project_root = Path(project_root)
        
    def check_git_installed(self):
        """检查Git是否已安装"""
        try:
            result = subprocess.run(['git', '--version'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✓ Git已安装: {result.stdout.strip()}")
                return True
            else:
                print("❌ Git未安装或不可用")
                return False
        except FileNotFoundError:
            print("❌ Git未安装")
            return False
    
    def is_git_repo(self):
        """检查当前目录是否已是Git仓库"""
        git_dir = self.project_root / ".git"
        return git_dir.exists()
    
    def create_gitignore(self):
        """创建适合深度学习项目的.gitignore文件"""
        gitignore_content = """# TimeMixer深度学习项目 .gitignore
# =====================================

# Python缓存文件
__pycache__/
*.py[cod]
*$py.class
*.so

# 分发/打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 单元测试/覆盖率报告
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# 环境变量
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE设置
.vscode/
.idea/
*.swp
*.swo
*~

# 深度学习相关
# ============

# 模型权重和检查点
*.pth
*.pt
*.ckpt
*.h5
*.hdf5
checkpoints/
models/
saved_models/

# 训练日志
logs/
runs/
tensorboard_logs/
wandb/

# 大型数据文件 (根据需要调整)
# *.csv  # 如果数据文件很大，取消注释
# *.pkl
# *.pickle
# *.npy
# *.npz

# 结果文件 (可选择性忽略)
results/
outputs/
predictions/
*.png
*.jpg
*.jpeg
*.pdf

# 临时文件
*.tmp
*.temp
*.log

# 系统文件
.DS_Store
Thumbs.db

# 备份文件
backups/
*.bak
*.backup

# 特定于项目的忽略
# =================

# 如果不想跟踪某些配置文件
# config_local.py
# secrets.py

# 如果不想跟踪某些结果文件
# timemixer_evaluation_results.csv  # 取消注释如果不想跟踪结果

# 如果不想跟踪archive目录
# archive/  # 取消注释如果不想跟踪归档文件
"""
        
        gitignore_path = self.project_root / ".gitignore"
        with open(gitignore_path, 'w', encoding='utf-8') as f:
            f.write(gitignore_content)
        
        print(f"✓ 已创建 .gitignore 文件")
        return gitignore_path
    
    def run_git_command(self, command, check=True):
        """运行Git命令"""
        try:
            result = subprocess.run(
                command, 
                cwd=self.project_root,
                capture_output=True, 
                text=True,
                check=check
            )
            return result
        except subprocess.CalledProcessError as e:
            print(f"❌ Git命令失败: {' '.join(command)}")
            print(f"错误信息: {e.stderr}")
            return None
    
    def init_git_repo(self):
        """初始化Git仓库"""
        if self.is_git_repo():
            print("⚠ 当前目录已是Git仓库")
            return True
        
        print("🔄 初始化Git仓库...")
        result = self.run_git_command(['git', 'init'])
        if result and result.returncode == 0:
            print("✓ Git仓库初始化成功")
            return True
        else:
            print("❌ Git仓库初始化失败")
            return False
    
    def configure_git_user(self):
        """配置Git用户信息"""
        print("\n🔧 配置Git用户信息...")
        
        # 检查是否已配置
        name_result = self.run_git_command(['git', 'config', 'user.name'], check=False)
        email_result = self.run_git_command(['git', 'config', 'user.email'], check=False)
        
        if name_result and name_result.returncode == 0 and name_result.stdout.strip():
            print(f"✓ 用户名已配置: {name_result.stdout.strip()}")
        else:
            name = input("请输入您的姓名: ").strip()
            if name:
                self.run_git_command(['git', 'config', 'user.name', name])
                print(f"✓ 用户名设置为: {name}")
        
        if email_result and email_result.returncode == 0 and email_result.stdout.strip():
            print(f"✓ 邮箱已配置: {email_result.stdout.strip()}")
        else:
            email = input("请输入您的邮箱: ").strip()
            if email:
                self.run_git_command(['git', 'config', 'user.email', email])
                print(f"✓ 邮箱设置为: {email}")
    
    def add_and_commit_initial(self):
        """添加文件并创建初始提交"""
        print("\n📁 添加文件到Git...")
        
        # 添加所有文件
        result = self.run_git_command(['git', 'add', '.'])
        if not result or result.returncode != 0:
            print("❌ 添加文件失败")
            return False
        
        # 检查状态
        status_result = self.run_git_command(['git', 'status', '--porcelain'])
        if status_result and status_result.stdout.strip():
            print("✓ 文件已添加到暂存区")
            
            # 创建初始提交
            commit_message = "Initial commit: TimeMixer深度学习项目基线版本"
            commit_result = self.run_git_command(['git', 'commit', '-m', commit_message])
            
            if commit_result and commit_result.returncode == 0:
                print("✓ 初始提交创建成功")
                print(f"提交信息: {commit_message}")
                return True
            else:
                print("❌ 创建提交失败")
                return False
        else:
            print("⚠ 没有文件需要提交")
            return True
    
    def create_development_branch(self):
        """创建开发分支"""
        print("\n🌿 创建开发分支...")
        
        # 创建并切换到开发分支
        result = self.run_git_command(['git', 'checkout', '-b', 'development'])
        if result and result.returncode == 0:
            print("✓ 已创建并切换到 'development' 分支")
            
            # 切换回主分支
            self.run_git_command(['git', 'checkout', 'main'], check=False)
            if self.run_git_command(['git', 'checkout', 'master'], check=False):
                print("✓ 已切换回主分支")
            
            return True
        else:
            print("❌ 创建开发分支失败")
            return False
    
    def show_git_status(self):
        """显示Git状态"""
        print("\n📊 Git仓库状态:")
        print("="*40)
        
        # 显示分支信息
        branch_result = self.run_git_command(['git', 'branch'], check=False)
        if branch_result and branch_result.returncode == 0:
            print("分支信息:")
            print(branch_result.stdout)
        
        # 显示提交历史
        log_result = self.run_git_command(['git', 'log', '--oneline', '-5'], check=False)
        if log_result and log_result.returncode == 0:
            print("最近提交:")
            print(log_result.stdout)
        
        # 显示状态
        status_result = self.run_git_command(['git', 'status', '--short'], check=False)
        if status_result and status_result.returncode == 0:
            if status_result.stdout.strip():
                print("工作区状态:")
                print(status_result.stdout)
            else:
                print("✓ 工作区干净")

def main():
    """主函数"""
    print("🎯 TimeMixer项目Git版本控制初始化")
    print("="*50)
    
    git_vc = GitVersionControl()
    
    # 检查Git是否安装
    if not git_vc.check_git_installed():
        print("\n请先安装Git:")
        print("Windows: https://git-scm.com/download/win")
        print("macOS: brew install git")
        print("Linux: sudo apt-get install git")
        return
    
    # 检查是否已是Git仓库
    if git_vc.is_git_repo():
        print("\n⚠ 当前目录已是Git仓库")
        choice = input("是否要查看仓库状态? (y/n): ").lower()
        if choice == 'y':
            git_vc.show_git_status()
        return
    
    print("\n🚀 开始设置Git版本控制...")
    
    # 1. 创建.gitignore文件
    git_vc.create_gitignore()
    
    # 2. 初始化Git仓库
    if not git_vc.init_git_repo():
        return
    
    # 3. 配置用户信息
    git_vc.configure_git_user()
    
    # 4. 创建初始提交
    if git_vc.add_and_commit_initial():
        print("\n✅ Git版本控制设置完成!")
        
        # 5. 创建开发分支
        git_vc.create_development_branch()
        
        # 6. 显示状态
        git_vc.show_git_status()
        
        print("\n💡 使用建议:")
        print("- 使用 'git status' 查看文件状态")
        print("- 使用 'git add <文件>' 添加文件到暂存区")
        print("- 使用 'git commit -m \"提交信息\"' 创建提交")
        print("- 使用 'git checkout development' 切换到开发分支")
        print("- 使用 'git log --oneline' 查看提交历史")
    else:
        print("\n❌ Git版本控制设置失败")

if __name__ == "__main__":
    main()
