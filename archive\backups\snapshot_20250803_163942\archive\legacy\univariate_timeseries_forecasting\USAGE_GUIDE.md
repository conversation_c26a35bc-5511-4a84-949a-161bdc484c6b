# 🌊 Runoff Forecasting System - 使用指南

## 📋 系统概述

这个系统专门为您的 `1964-2017dailyRunoff.csv` 数据文件设计，使用TimeMixer++模型进行单变量时间序列预测。

### 📊 数据信息
- **文件**: `C:\Users\<USER>\Desktop\timemix\1964-2017dailyRunoff.csv`
- **格式**: Excel文件 (.csv扩展名但实际是Excel格式)
- **数据量**: 24,836行 (约68年的日数据)
- **列名**: `DATA` (日期), `runoff` (径流量)
- **日期范围**: 1950年到2017年
- **预测任务**: 使用30天历史数据预测未来7天径流量

## 🚀 快速开始

### 方法1: 使用批处理文件 (推荐)
```bash
# 双击运行或在命令行执行
run_demo.bat
```

### 方法2: 手动步骤
```bash
# 1. 安装依赖
pip install pandas numpy matplotlib openpyxl pypots

# 2. 运行基础测试
python basic_demo.py

# 3. 运行完整预测
python run_forecasting_demo.py
```

### 方法3: 使用原始系统
```bash
# 使用完整的配置系统
python main_forecasting_pipeline.py --data_path "C:\Users\<USER>\Desktop\timemix\1964-2017dailyRunoff.csv"
```

## 📁 文件说明

### 核心文件
- **`run_forecasting_demo.py`** - 完整的预测演示脚本
- **`basic_demo.py`** - 基础功能测试脚本
- **`run_demo.bat`** - 一键运行批处理文件

### 系统文件
- **`data_loader.py`** - 数据加载和预处理
- **`model_config.py`** - 模型配置管理
- **`model_trainer.py`** - 模型训练器
- **`main_forecasting_pipeline.py`** - 主执行管道

## 🎯 预期输出

### 运行成功后会生成：

1. **控制台输出**:
   ```
   🌊 RUNOFF FORECASTING WITH TIMEMIXER++
   ✅ Successfully loaded data
   📊 Shape: (24836, 2)
   📅 Date range: 1950-01-01 to 2017-12-31
   💧 Runoff range: [具体数值]
   📈 Performance Metrics: RMSE, MAE等
   ```

2. **生成文件**:
   - `runoff_forecasting_results.png` - 预测结果可视化
   - `runoff_timemixer_model.pypots` - 训练好的模型
   - `results/` 文件夹 - 详细结果

3. **可视化图表**:
   - 5个测试样本的实际值vs预测值对比
   - 7天预测曲线
   - 性能指标展示

## ⚙️ 配置说明

### 默认配置 (在 `run_forecasting_demo.py` 中)
```python
配置参数:
- 输入序列长度: 30天
- 预测长度: 7天  
- 模型维度: 128
- 层数: 2
- 训练轮数: 30
- 批次大小: 32
```

### 自定义配置
如需修改配置，编辑 `run_forecasting_demo.py` 中的 `create_timemixer_model` 函数。

## 🔧 故障排除

### 问题1: 数据加载失败
```
❌ Error loading data: [错误信息]
```
**解决方案**:
- 确认文件路径正确
- 安装 openpyxl: `pip install openpyxl`

### 问题2: PyPOTS导入失败
```
❌ PyPOTS not installed
```
**解决方案**:
```bash
pip install pypots
# 或者
pip install torch pypots
```

### 问题3: 内存不足
```
❌ CUDA out of memory
```
**解决方案**:
- 减小批次大小 (batch_size)
- 使用CPU训练
- 减少模型维度

### 问题4: 训练时间过长
**解决方案**:
- 减少训练轮数 (epochs)
- 使用GPU加速
- 使用更小的模型配置

## 📊 性能预期

### 基于数据特征的预期性能:
- **数据量**: 24,836个样本 (充足)
- **时间跨度**: 68年 (包含多种模式)
- **预测难度**: 中等 (7天短期预测)

### 预期指标:
- **RMSE**: 取决于数据的变异性
- **MAE**: 平均绝对误差
- **训练时间**: 5-15分钟 (取决于硬件)

## 🎯 使用建议

### 1. 首次使用
1. 运行 `basic_demo.py` 确认数据加载正常
2. 运行 `run_forecasting_demo.py` 进行完整预测
3. 检查生成的图表和指标

### 2. 优化模型
- 调整输入序列长度 (n_steps)
- 修改预测长度 (n_pred_steps)
- 调整模型复杂度 (d_model, n_layers)

### 3. 生产使用
- 保存训练好的模型
- 定期重新训练
- 监控预测性能

## 📞 技术支持

### 常用命令
```bash
# 检查Python环境
python --version

# 检查已安装包
pip list | grep -E "(pandas|numpy|pypots)"

# 重新安装依赖
pip install --upgrade pandas numpy matplotlib pypots
```

### 日志和调试
- 所有脚本都包含详细的进度输出
- 错误信息会显示具体的失败原因
- 可以通过修改 `verbose=True` 获得更多训练信息

## 🎉 成功标志

当您看到以下输出时，表示系统运行成功：

```
🎉 FORECASTING COMPLETED!
📊 Final Performance: RMSE=X.XX, MAE=X.XX
📁 Generated files:
   - runoff_forecasting_results.png (visualization)
   - runoff_timemixer_model.pypots (trained model)
✨ Your runoff forecasting system is ready!
```

---

**注意**: 这个系统专门为您的runoff数据优化，可以直接使用无需修改代码。如有问题，请检查依赖安装和文件路径。
