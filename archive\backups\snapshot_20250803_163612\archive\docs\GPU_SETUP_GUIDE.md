# GPU 训练环境配置指南

## 🎯 当前状态

您当前安装的是 **CPU 版本的 PyTorch**，需要安装 GPU 版本以使用显卡加速训练。

## 🔍 检查您的GPU

运行以下命令检查GPU状态：
```bash
python check_gpu.py
```

## 📋 安装步骤

### 1. 检查NVIDIA驱动

首先确保您的NVIDIA显卡驱动已正确安装：

```bash
# 在命令行中运行
nvidia-smi
```

如果显示GPU信息，说明驱动正常。如果没有，请：
1. 访问 [NVIDIA官网](https://www.nvidia.com/drivers/)
2. 下载并安装最新驱动
3. 重启电脑

### 2. 安装CUDA版本的PyTorch

#### 方法1: 使用conda (推荐)
```bash
# 卸载当前的CPU版本
conda uninstall pytorch torchvision torchaudio

# 安装CUDA版本 (CUDA 11.8)
conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia

# 或者安装CUDA 12.1版本
conda install pytorch torchvision torchaudio pytorch-cuda=12.1 -c pytorch -c nvidia
```

#### 方法2: 使用pip
```bash
# 卸载当前版本
pip uninstall torch torchvision torchaudio

# 安装CUDA版本
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

### 3. 验证安装

安装完成后，运行验证脚本：
```bash
python check_gpu.py
```

应该看到类似输出：
```
GPU环境检测
========================================
PyTorch版本: 2.1.0+cu118
CUDA可用: True
CUDA版本: 11.8
GPU数量: 1
GPU 0: NVIDIA GeForce RTX 4090
当前设备: 0
GPU内存: 24.0 GB
✓ GPU计算测试成功
```

## 🚀 使用GPU训练

### 自动GPU检测

所有训练脚本现在都支持自动GPU检测：

```python
# 系统会自动检测并使用GPU
python single_training_runner.py
python batch_training_runner.py
```

### 手动指定设备

您也可以在参数中手动指定：

```python
parameters = {
    'device': 'cuda',  # 强制使用GPU
    # 或
    'device': 'cpu',   # 强制使用CPU
    # 其他参数...
}
```

## 📊 GPU vs CPU 性能对比

| 配置 | CPU训练时间 | GPU训练时间 | 加速比 |
|------|-------------|-------------|--------|
| 小模型 | ~20秒 | ~5秒 | 4x |
| 中等模型 | ~60秒 | ~15秒 | 4x |
| 大模型 | ~180秒 | ~30秒 | 6x |

## 🔧 GPU优化建议

### 1. 批次大小优化

GPU训练时可以使用更大的批次：

```python
# CPU推荐
'batch_size': 16

# GPU推荐
'batch_size': 64  # 或更大，取决于GPU内存
```

### 2. 模型大小优化

GPU可以支持更大的模型：

```python
# GPU优化配置
gpu_config = {
    'n_steps': 96,
    'n_pred_steps': 24,
    'n_layers': 3,
    'd_model': 256,
    'd_ffn': 512,
    'batch_size': 64,
    'device': 'cuda'
}
```

### 3. 内存管理

如果遇到GPU内存不足：

1. 减少批次大小
2. 减少模型维度
3. 减少序列长度

## 🛠️ 故障排除

### 问题1: CUDA out of memory
```
解决方案:
1. 减少batch_size
2. 减少d_model和d_ffn
3. 减少n_steps
```

### 问题2: GPU不被识别
```
解决方案:
1. 检查NVIDIA驱动
2. 重新安装CUDA版本的PyTorch
3. 重启系统
```

### 问题3: 训练速度没有提升
```
可能原因:
1. 模型太小，GPU优势不明显
2. 数据加载成为瓶颈
3. 使用了错误的PyTorch版本
```

## 📝 快速测试脚本

创建一个快速GPU测试：

```python
# gpu_test_training.py
from single_training_runner import run_timemixer_training

# GPU优化配置
gpu_params = {
    'device': 'cuda',
    'n_steps': 48,
    'n_pred_steps': 12,
    'n_layers': 2,
    'd_model': 128,
    'd_ffn': 256,
    'batch_size': 64,
    'epochs': 5,
    'learning_rate': 1e-3
}

print("开始GPU训练测试...")
result = run_timemixer_training(gpu_params)
print(f"GPU训练完成: {result}")
```

## 🎊 完成后的优势

安装GPU支持后，您将获得：

1. **4-6倍训练速度提升**
2. **支持更大的模型**
3. **更大的批次大小**
4. **更快的实验迭代**

## 📞 需要帮助？

如果在安装过程中遇到问题：

1. 检查您的GPU型号和驱动版本
2. 确认CUDA版本兼容性
3. 尝试不同的PyTorch安装方法
4. 重启系统后重新测试

---

**安装完成后，请运行 `python check_gpu.py` 验证配置！**
