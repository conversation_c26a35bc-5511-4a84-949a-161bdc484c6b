"""
粒子群优化(PSO)算法用于TimeMixer超参数优化
==============================================

使用PSO算法自动搜索TimeMixer模型的最优超参数组合
优化目标: 最小化验证损失或最大化R²分数
"""

import numpy as np
import random
import json
from typing import Dict, List, Tuple, Any
import copy

class PSOHyperparameterOptimizer:
    """粒子群优化超参数优化器"""
    
    def __init__(self, 
                 param_bounds: Dict[str, Tuple[float, float]],
                 param_types: Dict[str, str],
                 n_particles: int = 20,
                 max_iterations: int = 30,
                 w: float = 0.7,
                 c1: float = 1.5,
                 c2: float = 1.5):
        """
        初始化PSO优化器
        
        Args:
            param_bounds: 参数边界 {'param_name': (min_val, max_val)}
            param_types: 参数类型 {'param_name': 'int'/'float'/'choice'}
            n_particles: 粒子数量
            max_iterations: 最大迭代次数
            w: 惯性权重
            c1: 个体学习因子
            c2: 社会学习因子
        """
        self.param_bounds = param_bounds
        self.param_types = param_types
        self.n_particles = n_particles
        self.max_iterations = max_iterations
        self.w = w
        self.c1 = c1
        self.c2 = c2
        
        # 参数名称列表
        self.param_names = list(param_bounds.keys())
        self.n_dims = len(self.param_names)
        
        # 初始化粒子群
        self.particles = []
        self.velocities = []
        self.personal_best_positions = []
        self.personal_best_scores = []
        self.global_best_position = None
        self.global_best_score = float('inf')
        
        self._initialize_particles()
    
    def _initialize_particles(self):
        """初始化粒子群"""
        for _ in range(self.n_particles):
            # 随机初始化粒子位置
            particle = []
            velocity = []
            
            for param_name in self.param_names:
                min_val, max_val = self.param_bounds[param_name]
                
                # 初始化位置
                if self.param_types[param_name] == 'int':
                    pos = random.randint(int(min_val), int(max_val))
                else:  # float
                    pos = random.uniform(min_val, max_val)
                particle.append(pos)
                
                # 初始化速度
                vel = random.uniform(-abs(max_val - min_val) * 0.1, 
                                   abs(max_val - min_val) * 0.1)
                velocity.append(vel)
            
            self.particles.append(particle)
            self.velocities.append(velocity)
            self.personal_best_positions.append(particle.copy())
            self.personal_best_scores.append(float('inf'))
    
    def _position_to_params(self, position: List[float]) -> Dict[str, Any]:
        """将粒子位置转换为参数字典"""
        params = {}
        for i, param_name in enumerate(self.param_names):
            val = position[i]
            
            # 确保值在边界内
            min_val, max_val = self.param_bounds[param_name]
            val = max(min_val, min(max_val, val))
            
            # 根据类型转换
            if self.param_types[param_name] == 'int':
                params[param_name] = int(round(val))
            else:  # float
                params[param_name] = float(val)
                
        return params
    
    def _update_velocity_and_position(self, particle_idx: int):
        """更新粒子速度和位置"""
        for dim in range(self.n_dims):
            # 计算速度更新
            r1, r2 = random.random(), random.random()
            
            cognitive_component = self.c1 * r1 * (
                self.personal_best_positions[particle_idx][dim] - 
                self.particles[particle_idx][dim]
            )
            
            social_component = self.c2 * r2 * (
                self.global_best_position[dim] - 
                self.particles[particle_idx][dim]
            )
            
            # 更新速度
            self.velocities[particle_idx][dim] = (
                self.w * self.velocities[particle_idx][dim] + 
                cognitive_component + social_component
            )
            
            # 更新位置
            self.particles[particle_idx][dim] += self.velocities[particle_idx][dim]
            
            # 边界处理
            min_val, max_val = self.param_bounds[self.param_names[dim]]
            if self.particles[particle_idx][dim] < min_val:
                self.particles[particle_idx][dim] = min_val
                self.velocities[particle_idx][dim] = 0
            elif self.particles[particle_idx][dim] > max_val:
                self.particles[particle_idx][dim] = max_val
                self.velocities[particle_idx][dim] = 0
    
    def optimize(self, objective_function, verbose: bool = True) -> Tuple[Dict[str, Any], float]:
        """
        执行PSO优化
        
        Args:
            objective_function: 目标函数，输入参数字典，返回损失值(越小越好)
            verbose: 是否打印优化过程
            
        Returns:
            最优参数字典和最优分数
        """
        optimization_history = []
        
        for iteration in range(self.max_iterations):
            if verbose:
                print(f"\n=== PSO迭代 {iteration + 1}/{self.max_iterations} ===")
            
            # 评估所有粒子
            for i in range(self.n_particles):
                # 获取当前粒子的参数
                params = self._position_to_params(self.particles[i])
                
                # 评估目标函数
                try:
                    score = objective_function(params)
                    
                    if verbose:
                        print(f"粒子 {i+1}: 分数 = {score:.4f}")
                    
                    # 更新个体最优
                    if score < self.personal_best_scores[i]:
                        self.personal_best_scores[i] = score
                        self.personal_best_positions[i] = self.particles[i].copy()
                    
                    # 更新全局最优
                    if score < self.global_best_score:
                        self.global_best_score = score
                        self.global_best_position = self.particles[i].copy()
                        
                        if verbose:
                            print(f"🎯 发现新的全局最优! 分数: {score:.4f}")
                            best_params = self._position_to_params(self.global_best_position)
                            print(f"最优参数: {best_params}")
                
                except Exception as e:
                    if verbose:
                        print(f"粒子 {i+1} 评估失败: {e}")
                    # 给失败的粒子一个很大的惩罚分数
                    score = float('inf')
            
            # 记录历史
            optimization_history.append({
                'iteration': iteration + 1,
                'best_score': self.global_best_score,
                'best_params': self._position_to_params(self.global_best_position) if self.global_best_position else None
            })
            
            # 更新所有粒子的速度和位置
            if self.global_best_position is not None:
                for i in range(self.n_particles):
                    self._update_velocity_and_position(i)
            
            if verbose:
                print(f"当前全局最优分数: {self.global_best_score:.4f}")
        
        # 返回最优结果
        best_params = self._position_to_params(self.global_best_position) if self.global_best_position else {}
        
        return best_params, self.global_best_score, optimization_history


def get_timemixer_param_space():
    """定义TimeMixer的超参数搜索空间"""
    
    # 参数边界
    param_bounds = {
        'n_steps': (30, 180),           # 输入序列长度
        'n_pred_steps': (5, 30),        # 预测序列长度
        'n_layers': (2, 6),             # 模型层数
        'd_model': (64, 512),           # 模型维度
        'd_ffn': (128, 1024),           # 前馈网络维度
        'top_k': (3, 20),               # top-k参数
        'moving_avg': (3, 21),          # 移动平均窗口
        'downsampling_window': (2, 8),  # 下采样窗口
        'downsampling_layers': (1, 4),  # 下采样层数
        'dropout': (0.05, 0.5),         # Dropout率
        'batch_size': (8, 128),         # 批次大小
        'learning_rate': (1e-5, 1e-2),  # 学习率
        'epochs': (50, 500),            # 训练轮次
        'patience': (10, 50)            # 早停耐心值
    }
    
    # 参数类型
    param_types = {
        'n_steps': 'int',
        'n_pred_steps': 'int', 
        'n_layers': 'int',
        'd_model': 'int',
        'd_ffn': 'int',
        'top_k': 'int',
        'moving_avg': 'int',
        'downsampling_window': 'int',
        'downsampling_layers': 'int',
        'dropout': 'float',
        'batch_size': 'int',
        'learning_rate': 'float',
        'epochs': 'int',
        'patience': 'int'
    }
    
    return param_bounds, param_types


def mock_objective_function(params: Dict[str, Any]) -> float:
    """
    模拟目标函数 - 在实际使用中应该替换为真实的模型训练和评估
    
    这里使用一些启发式规则来模拟合理的参数组合
    """
    score = 0.0
    
    # 模型复杂度惩罚
    complexity = params['n_layers'] * params['d_model'] * params['d_ffn'] / 1000000
    score += complexity * 0.1
    
    # 序列长度与预测长度的比例
    ratio = params['n_steps'] / params['n_pred_steps']
    if ratio < 2 or ratio > 10:
        score += 0.5
    
    # dropout与模型大小的匹配
    if params['d_model'] > 256 and params['dropout'] < 0.2:
        score += 0.3
    
    # 学习率与批次大小的匹配
    if params['learning_rate'] > 1e-3 and params['batch_size'] < 32:
        score += 0.2
    
    # moving_avg应该小于n_steps
    if params['moving_avg'] >= params['n_steps']:
        score += 1.0
    
    # 添加随机噪声模拟真实评估的不确定性
    score += random.uniform(0, 0.1)
    
    return score


if __name__ == "__main__":
    # 获取参数空间
    param_bounds, param_types = get_timemixer_param_space()
    
    # 创建PSO优化器
    optimizer = PSOHyperparameterOptimizer(
        param_bounds=param_bounds,
        param_types=param_types,
        n_particles=15,
        max_iterations=20,
        w=0.7,
        c1=1.5,
        c2=1.5
    )
    
    print("🚀 开始PSO超参数优化...")
    print(f"粒子数量: {optimizer.n_particles}")
    print(f"最大迭代次数: {optimizer.max_iterations}")
    print(f"优化参数数量: {len(param_bounds)}")
    
    # 执行优化
    best_params, best_score, history = optimizer.optimize(
        objective_function=mock_objective_function,
        verbose=True
    )
    
    print("\n" + "="*60)
    print("🎯 PSO优化完成!")
    print(f"最优分数: {best_score:.4f}")
    print("最优参数组合:")
    for param, value in best_params.items():
        print(f"  {param}: {value}")
    
    # 保存优化历史
    with open('pso_optimization_history.json', 'w', encoding='utf-8') as f:
        json.dump(history, f, indent=2, ensure_ascii=False)
    
    print(f"\n优化历史已保存到: pso_optimization_history.json")
