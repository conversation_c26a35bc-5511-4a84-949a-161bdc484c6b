"""
精确调优R²>0.8
===============

基于配置17 (R²=0.7301) 进行精确的参数调优
目标：通过精确调优突破R²=0.8
"""

import os
from datetime import datetime

def run_precision_tune_v1():
    """精确调优版本1 - 专注学习率和训练时间"""
    print("🎯 精确调优v1 - 学习率和训练优化")
    print("="*50)
    
    # 基于配置17，专注优化学习率和训练参数
    precision_config_v1 = {
        'name': '精确调优v1_学习率训练优化',
        # 完全保持配置17的架构参数
        'n_steps': 90,
        'n_pred_steps': 5,
        'n_layers': 3,
        'd_model': 256,
        'd_ffn': 512,
        'top_k': 10,
        'moving_avg': 7,
        'downsampling_window': 3,
        'downsampling_layers': 2,
        'use_norm': True,
        'batch_size': 32,
        'device': 'cuda',
        'num_workers': 0,
        'dropout': 0.2,                   # 保持不变
        
        # 精确调优这些参数
        'learning_rate': 2.7e-4,          # 精确调优: 3e-4 → 2.7e-4 (-10%)
        'epochs': 280,                    # 增加训练时间: 200 → 280 (+40%)
        'patience': 45                    # 增加耐心: 30 → 45 (+50%)
    }
    
    print(f"🔧 精确调优策略v1:")
    print(f"  • 保持配置17的所有架构参数")
    print(f"  • 学习率: 3e-4 → 2.7e-4 (-10%)")
    print(f"  • epochs: 200 → 280 (+40%)")
    print(f"  • patience: 30 → 45 (+50%)")
    print(f"  • 目标: 通过更长时间训练和精确学习率提升性能")
    
    return run_training(precision_config_v1)

def run_precision_tune_v2():
    """精确调优版本2 - 微调dropout和batch_size"""
    print("\n🎯 精确调优v2 - dropout和batch_size优化")
    print("="*50)
    
    # 基于配置17，微调正则化参数
    precision_config_v2 = {
        'name': '精确调优v2_正则化优化',
        # 保持配置17的主要参数
        'n_steps': 90,
        'n_pred_steps': 5,
        'n_layers': 3,
        'd_model': 256,
        'd_ffn': 512,
        'top_k': 10,
        'moving_avg': 7,
        'downsampling_window': 3,
        'downsampling_layers': 2,
        'use_norm': True,
        'device': 'cuda',
        'num_workers': 0,
        'learning_rate': 3e-4,            # 保持原始学习率
        'epochs': 240,                    # 适度增加训练时间
        'patience': 35,                   # 适度增加耐心
        
        # 精确调优这些参数
        'dropout': 0.18,                  # 微调: 0.2 → 0.18 (-10%)
        'batch_size': 28                  # 微调: 32 → 28 (-12.5%)
    }
    
    print(f"🔧 精确调优策略v2:")
    print(f"  • 保持配置17的架构和学习率")
    print(f"  • dropout: 0.2 → 0.18 (-10%)")
    print(f"  • batch_size: 32 → 28 (-12.5%)")
    print(f"  • epochs: 200 → 240 (+20%)")
    print(f"  • 目标: 通过降低正则化提升模型容量")
    
    return run_training(precision_config_v2)

def run_precision_tune_v3():
    """精确调优版本3 - 微调模型维度"""
    print("\n🎯 精确调优v3 - 模型维度微调")
    print("="*50)
    
    # 基于配置17，微调模型维度
    precision_config_v3 = {
        'name': '精确调优v3_模型维度微调',
        # 保持配置17的大部分参数
        'n_steps': 90,
        'n_pred_steps': 5,
        'n_layers': 3,
        'moving_avg': 7,
        'downsampling_window': 3,
        'downsampling_layers': 2,
        'use_norm': True,
        'device': 'cuda',
        'num_workers': 0,
        'dropout': 0.2,
        'learning_rate': 3e-4,
        'batch_size': 32,
        'epochs': 220,
        'patience': 35,
        
        # 微调模型维度
        'd_model': 272,                   # 微调: 256 → 272 (+6.25%)
        'd_ffn': 544,                     # 相应调整: 512 → 544 (+6.25%)
        'top_k': 11                       # 微调: 10 → 11 (+10%)
    }
    
    print(f"🔧 精确调优策略v3:")
    print(f"  • 保持配置17的训练参数")
    print(f"  • d_model: 256 → 272 (+6.25%)")
    print(f"  • d_ffn: 512 → 544 (+6.25%)")
    print(f"  • top_k: 10 → 11 (+10%)")
    print(f"  • 目标: 通过微调模型容量提升性能")
    
    return run_training(precision_config_v3)

def run_precision_tune_v4():
    """精确调优版本4 - 综合微调"""
    print("\n🎯 精确调优v4 - 综合微调")
    print("="*50)
    
    # 基于配置17，综合微调多个参数
    precision_config_v4 = {
        'name': '精确调优v4_综合微调',
        # 基于配置17进行综合微调
        'n_steps': 90,
        'n_pred_steps': 5,
        'n_layers': 3,
        'moving_avg': 7,
        'downsampling_window': 3,
        'downsampling_layers': 2,
        'use_norm': True,
        'device': 'cuda',
        'num_workers': 0,
        
        # 综合微调
        'd_model': 264,                   # 微调: 256 → 264 (+3.1%)
        'd_ffn': 528,                     # 相应调整: 512 → 528 (+3.1%)
        'top_k': 11,                      # 微调: 10 → 11 (+10%)
        'dropout': 0.19,                  # 微调: 0.2 → 0.19 (-5%)
        'learning_rate': 2.8e-4,          # 微调: 3e-4 → 2.8e-4 (-6.7%)
        'batch_size': 30,                 # 微调: 32 → 30 (-6.25%)
        'epochs': 260,                    # 增加: 200 → 260 (+30%)
        'patience': 40                    # 增加: 30 → 40 (+33%)
    }
    
    print(f"🔧 精确调优策略v4:")
    print(f"  • d_model: 256 → 264 (+3.1%)")
    print(f"  • d_ffn: 512 → 528 (+3.1%)")
    print(f"  • top_k: 10 → 11 (+10%)")
    print(f"  • dropout: 0.2 → 0.19 (-5%)")
    print(f"  • learning_rate: 3e-4 → 2.8e-4 (-6.7%)")
    print(f"  • batch_size: 32 → 30 (-6.25%)")
    print(f"  • epochs: 200 → 260 (+30%)")
    print(f"  • 目标: 综合微调多个参数")
    
    return run_training(precision_config_v4)

def run_training(config):
    """运行训练"""
    try:
        import enhanced_compatible_training_runner
        
        print(f"\n🔥 开始训练: {config['name']}")
        start_time = datetime.now()
        
        result = enhanced_compatible_training_runner.run_compatible_training(config)
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        if result:
            print(f"\n✅ 训练完成! 用时: {duration:.1f}秒")
            
            # 读取结果
            try:
                import pandas as pd
                df = pd.read_csv('timemixer_evaluation_results.csv')
                latest_result = df.iloc[-1]
                r2_value = latest_result['R2']
                
                print(f"\n🎯 训练结果:")
                print(f"  R² Score: {r2_value:.4f}")
                print(f"  MAE: {latest_result['MAE']:.2f}")
                print(f"  RMSE: {latest_result['RMSE']:.2f}")
                
                # 与基线对比
                baseline_r2 = 0.7301
                improvement = r2_value - baseline_r2
                
                print(f"\n📈 与配置17对比:")
                print(f"  基线R²: {baseline_r2:.4f}")
                print(f"  当前R²: {r2_value:.4f}")
                print(f"  提升: {improvement:+.4f} ({improvement/baseline_r2*100:+.1f}%)")
                
                if r2_value > 0.8:
                    print(f"\n🎉 恭喜！成功突破R²=0.8！")
                    return True, r2_value
                elif r2_value > 0.78:
                    print(f"\n🔥 非常接近！R²>0.78")
                    return False, r2_value
                elif r2_value > baseline_r2:
                    print(f"\n📈 有进步！超过基线")
                    return False, r2_value
                else:
                    print(f"\n📊 需要调整策略")
                    return False, r2_value
                
            except Exception as e:
                print(f"📊 结果读取失败: {e}")
                return False, None
        else:
            print(f"❌ 训练失败")
            return False, None
            
    except Exception as e:
        print(f"❌ 训练过程出错: {e}")
        return False, None

def main():
    """主函数"""
    print("🎯 精确调优R²>0.8程序")
    print("="*50)
    
    print("📊 策略:")
    print("  • 基于配置17 (R²=0.7301) 的成功")
    print("  • 进行精确的参数微调")
    print("  • 系统性尝试不同的优化方向")
    
    results = []
    
    # 按优先级顺序尝试
    strategies = [
        ("学习率和训练优化", run_precision_tune_v1),
        ("正则化优化", run_precision_tune_v2),
        ("模型维度微调", run_precision_tune_v3),
        ("综合微调", run_precision_tune_v4)
    ]
    
    for strategy_name, strategy_func in strategies:
        print(f"\n{'='*20} {strategy_name} {'='*20}")
        
        success, r2_value = strategy_func()
        
        results.append({
            'strategy': strategy_name,
            'success': success,
            'r2': r2_value
        })
        
        if success:
            print(f"\n🎉 {strategy_name} 成功突破R²=0.8！")
            break
        elif r2_value and r2_value > 0.78:
            print(f"\n🔥 {strategy_name} 非常接近目标！")
        
        print(f"{'='*60}")
    
    # 总结
    print(f"\n📊 精确调优总结:")
    print("="*30)
    
    successful = [r for r in results if r['success']]
    best_result = max([r for r in results if r['r2']], key=lambda x: x['r2']) if any(r['r2'] for r in results) else None
    
    print(f"总尝试策略: {len(results)}")
    print(f"成功突破0.8: {len(successful)} 次")
    
    if successful:
        print(f"🎉 成功策略: {successful[0]['strategy']}")
        print(f"🏆 达到R²: {successful[0]['r2']:.4f}")
    elif best_result:
        print(f"💪 最佳策略: {best_result['strategy']}")
        print(f"📊 达到R²: {best_result['r2']:.4f}")
        gap = 0.8 - best_result['r2']
        print(f"🎯 距离目标: {gap:.4f}")
        
        if gap < 0.02:
            print(f"💡 建议: 非常接近，可以尝试集成学习")
        elif gap < 0.05:
            print(f"💡 建议: 接近目标，继续精细调优")
        else:
            print(f"💡 建议: 可能需要更多水文学特征或不同策略")

if __name__ == "__main__":
    main()
