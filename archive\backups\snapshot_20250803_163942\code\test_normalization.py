"""
测试数据标准化功能
=================

验证添加的数据标准化是否正确工作
"""

from my_parameters import get_my_parameters
from compatible_training_runner import run_compatible_training

def test_normalization():
    """测试标准化功能"""
    
    print("🧪 测试数据标准化功能")
    print("="*60)
    
    # 获取一个配置进行测试
    configs = get_my_parameters()
    test_config = configs[0].copy()
    
    # 修改为快速测试
    test_config.update({
        'name': '标准化测试',
        'n_steps': 96,
        'n_pred_steps': 5,
        'epochs': 3,
        'max_samples': 200,
        'batch_size': 16,
        'moving_avg': 25,  # 确保moving_avg小于n_steps
        'device': 'cpu'  # 使用CPU确保兼容性
    })
    
    print(f"测试配置:")
    print(f"  配置名称: {test_config['name']}")
    print(f"  输入序列: {test_config['n_steps']} 天")
    print(f"  预测序列: {test_config['n_pred_steps']} 天")
    print(f"  训练轮次: {test_config['epochs']}")
    print(f"  样本数量: {test_config['max_samples']}")
    print()
    
    print("开始测试...")
    result = run_compatible_training(test_config)
    
    if result:
        print(f"\n✅ 标准化功能测试成功!")
        print(f"训练ID: {result['training_id']}")
        print(f"MAE: {result['mae']:.6f} m³/s")
        print(f"RMSE: {result['rmse']:.6f} m³/s")
        print(f"NSE: {result['nse']:.6f}")
        print(f"R²: {result['r2']:.6f}")
        
        print(f"\n📊 标准化效果分析:")
        print(f"- 数据已进行Z-score标准化")
        print(f"- 训练使用标准化数据")
        print(f"- 评估指标基于原始尺度")
        print(f"- MAE和RMSE单位为 m³/s")
        
        return True
    else:
        print(f"\n❌ 标准化功能测试失败!")
        return False

def compare_with_without_normalization():
    """对比标准化前后的效果"""
    
    print(f"\n🔍 标准化效果对比分析")
    print("="*60)
    
    print("📈 标准化的优势:")
    print("1. 数值稳定性: 避免梯度爆炸/消失")
    print("2. 收敛速度: 加快模型训练收敛")
    print("3. 特征平衡: 确保所有特征在相同尺度")
    print("4. 模型性能: 通常能获得更好的预测精度")
    
    print(f"\n📊 评估指标说明:")
    print("- MAE (平均绝对误差): 预测值与真实值的平均绝对差异")
    print("- RMSE (均方根误差): 对大误差更敏感的误差指标")
    print("- NSE (Nash-Sutcliffe效率): 水文模型常用指标，1为完美")
    print("- R² (决定系数): 模型解释方差的比例，1为完美")
    
    print(f"\n🎯 径流预测的合理指标范围:")
    print("- MAE: < 500 m³/s (优秀), < 1000 m³/s (良好)")
    print("- RMSE: < 800 m³/s (优秀), < 1500 m³/s (良好)")
    print("- NSE: > 0.8 (优秀), > 0.6 (良好)")
    print("- R²: > 0.8 (优秀), > 0.6 (良好)")

if __name__ == "__main__":
    # 测试标准化功能
    success = test_normalization()
    
    if success:
        # 显示对比分析
        compare_with_without_normalization()
        
        print(f"\n🎉 标准化功能已成功集成!")
        print(f"💡 现在可以使用标准化的训练流程了")
        print(f"🚀 运行 'python run_my_training.py' 开始正式训练")
    else:
        print(f"\n❌ 需要检查标准化实现")
