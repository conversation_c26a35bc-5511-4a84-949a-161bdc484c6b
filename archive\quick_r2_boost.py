"""
快速R²提升训练
==============

基于配置17的成功，进行保守的参数微调，快速尝试突破R²=0.8
"""

import os
import sys

def run_conservative_boost():
    """运行保守的R²提升训练"""
    print("🚀 快速R²提升训练")
    print("="*40)
    
    print("📊 策略: 基于配置17 (R²=0.7301) 进行保守微调")
    print("🎯 目标: 快速尝试突破R²=0.8")
    
    # 创建一个基于配置17的微调版本
    enhanced_config = {
        'name': '快速R2提升_保守微调',
        # 基于配置17的成功参数，进行小幅优化
        'n_steps': 90,                    # 保持成功的序列长度
        'n_pred_steps': 5,                # 保持成功的预测长度
        'n_layers': 3,                    # 保持成功的层数
        'd_model': 288,                   # 小幅增加模型维度 (256→288)
        'd_ffn': 576,                     # 相应增加FFN (512→576)
        'top_k': 12,                      # 小幅增加top_k (10→12)
        'moving_avg': 7,                  # 保持成功的移动平均
        'downsampling_window': 3,         # 保持成功的下采样
        'downsampling_layers': 2,         # 保持成功的下采样层数
        'dropout': 0.18,                  # 小幅降低dropout (0.2→0.18)
        'use_norm': True,                 # 保持层归一化
        'epochs': 250,                    # 增加训练轮次 (200→250)
        'batch_size': 28,                 # 小幅减小batch size (32→28)
        'learning_rate': 2.5e-4,          # 小幅降低学习率 (3e-4→2.5e-4)
        'patience': 40,                   # 增加耐心值 (30→40)
        'device': 'cuda',
        'num_workers': 0
    }
    
    print(f"\n🔧 微调参数:")
    print(f"  d_model: 256 → 288 (+12.5%)")
    print(f"  d_ffn: 512 → 576 (+12.5%)")
    print(f"  top_k: 10 → 12 (+20%)")
    print(f"  dropout: 0.2 → 0.18 (-10%)")
    print(f"  epochs: 200 → 250 (+25%)")
    print(f"  batch_size: 32 → 28 (-12.5%)")
    print(f"  learning_rate: 3e-4 → 2.5e-4 (-16.7%)")
    print(f"  patience: 30 → 40 (+33%)")
    
    # 导入训练模块
    try:
        import enhanced_compatible_training_runner
        
        print(f"\n🔥 开始快速提升训练...")
        result = enhanced_compatible_training_runner.run_compatible_training(enhanced_config)
        
        if result:
            print(f"\n✅ 快速提升训练完成!")
            
            # 读取结果
            try:
                import pandas as pd
                df = pd.read_csv('timemixer_evaluation_results.csv')
                latest_result = df.iloc[-1]
                r2_value = latest_result['R2']
                
                print(f"\n🎯 快速提升结果:")
                print(f"  R² Score: {r2_value:.4f}")
                print(f"  MAE: {latest_result['MAE']:.2f}")
                print(f"  RMSE: {latest_result['RMSE']:.2f}")
                
                # 与配置17对比
                baseline_r2 = 0.7301
                improvement = r2_value - baseline_r2
                
                print(f"\n📈 与配置17对比:")
                print(f"  基线R²: {baseline_r2:.4f}")
                print(f"  当前R²: {r2_value:.4f}")
                print(f"  提升: {improvement:+.4f} ({improvement/baseline_r2*100:+.1f}%)")
                
                if r2_value > 0.8:
                    print(f"\n🎉 恭喜！成功突破R²=0.8！")
                    print(f"🏆 这是一个重大突破！")
                elif r2_value > 0.75:
                    print(f"\n👍 很好！R²>0.75，非常接近目标")
                elif r2_value > baseline_r2:
                    print(f"\n📈 有进步！超过了基线配置")
                else:
                    print(f"\n📊 结果记录，可能需要更大的改动")
                
                return r2_value
                
            except Exception as e:
                print(f"📊 结果读取失败: {e}")
                return None
        else:
            print(f"❌ 快速提升训练失败")
            return None
            
    except Exception as e:
        print(f"❌ 训练过程出错: {e}")
        return None

def run_aggressive_boost():
    """运行激进的R²提升训练"""
    print("\n🚀 激进R²提升训练")
    print("="*40)
    
    print("📊 策略: 基于配置17进行激进优化")
    print("🎯 目标: 激进尝试突破R²=0.8")
    
    # 创建一个激进优化版本
    aggressive_config = {
        'name': '激进R2提升_大胆尝试',
        # 基于配置17，进行更大幅度的优化
        'n_steps': 90,                    # 保持成功的序列长度
        'n_pred_steps': 5,                # 保持成功的预测长度
        'n_layers': 4,                    # 增加层数 (3→4)
        'd_model': 384,                   # 大幅增加模型维度 (256→384)
        'd_ffn': 768,                     # 大幅增加FFN (512→768)
        'top_k': 15,                      # 大幅增加top_k (10→15)
        'moving_avg': 7,                  # 保持成功的移动平均
        'downsampling_window': 2,         # 减少下采样，保留更多信息
        'downsampling_layers': 1,         # 减少下采样层数
        'dropout': 0.15,                  # 降低dropout (0.2→0.15)
        'use_norm': True,                 # 保持层归一化
        'epochs': 400,                    # 大幅增加训练轮次 (200→400)
        'batch_size': 24,                 # 减小batch size (32→24)
        'learning_rate': 1.5e-4,          # 降低学习率 (3e-4→1.5e-4)
        'patience': 60,                   # 大幅增加耐心值 (30→60)
        'device': 'cuda',
        'num_workers': 0
    }
    
    print(f"\n🔧 激进优化参数:")
    print(f"  n_layers: 3 → 4 (+33%)")
    print(f"  d_model: 256 → 384 (+50%)")
    print(f"  d_ffn: 512 → 768 (+50%)")
    print(f"  top_k: 10 → 15 (+50%)")
    print(f"  downsampling: 减少以保留更多信息")
    print(f"  dropout: 0.2 → 0.15 (-25%)")
    print(f"  epochs: 200 → 400 (+100%)")
    print(f"  learning_rate: 3e-4 → 1.5e-4 (-50%)")
    print(f"  patience: 30 → 60 (+100%)")
    
    # 导入训练模块
    try:
        import enhanced_compatible_training_runner
        
        print(f"\n🔥 开始激进提升训练...")
        result = enhanced_compatible_training_runner.run_compatible_training(aggressive_config)
        
        if result:
            print(f"\n✅ 激进提升训练完成!")
            
            # 读取结果
            try:
                import pandas as pd
                df = pd.read_csv('timemixer_evaluation_results.csv')
                latest_result = df.iloc[-1]
                r2_value = latest_result['R2']
                
                print(f"\n🎯 激进提升结果:")
                print(f"  R² Score: {r2_value:.4f}")
                print(f"  MAE: {latest_result['MAE']:.2f}")
                print(f"  RMSE: {latest_result['RMSE']:.2f}")
                
                # 与配置17对比
                baseline_r2 = 0.7301
                improvement = r2_value - baseline_r2
                
                print(f"\n📈 与配置17对比:")
                print(f"  基线R²: {baseline_r2:.4f}")
                print(f"  当前R²: {r2_value:.4f}")
                print(f"  提升: {improvement:+.4f} ({improvement/baseline_r2*100:+.1f}%)")
                
                if r2_value > 0.8:
                    print(f"\n🎉 恭喜！激进策略成功突破R²=0.8！")
                    print(f"🏆 这证明了大胆尝试的价值！")
                elif r2_value > 0.75:
                    print(f"\n👍 激进策略效果很好！R²>0.75")
                elif r2_value > baseline_r2:
                    print(f"\n📈 激进策略有效果！")
                else:
                    print(f"\n📊 激进策略可能过度了，需要调整")
                
                return r2_value
                
            except Exception as e:
                print(f"📊 结果读取失败: {e}")
                return None
        else:
            print(f"❌ 激进提升训练失败")
            return None
            
    except Exception as e:
        print(f"❌ 训练过程出错: {e}")
        return None

def main():
    """主函数"""
    print("🎯 快速R²提升程序")
    print("="*50)
    
    print("💡 策略:")
    print("1. 🔧 保守微调: 基于配置17小幅优化")
    print("2. 🚀 激进优化: 大胆尝试突破")
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] == 'conservative':
            r2_result = run_conservative_boost()
        elif sys.argv[1] == 'aggressive':
            r2_result = run_aggressive_boost()
        else:
            print("❌ 无效参数，使用 'conservative' 或 'aggressive'")
            return
    else:
        # 默认先尝试保守方案
        print("🔧 默认运行保守微调方案...")
        r2_result = run_conservative_boost()
        
        if r2_result and r2_result < 0.8:
            print(f"\n🤔 保守方案R²={r2_result:.4f}，未达到0.8")
            choice = input("是否尝试激进方案？(y/n): ").strip().lower()
            if choice == 'y':
                run_aggressive_boost()
    
    print(f"\n💡 总结:")
    print(f"• 配置17已经是很好的基线 (R²=0.7301)")
    print(f"• 水文学特征工程是关键成功因素")
    print(f"• 继续尝试不同的参数组合")
    print(f"• 考虑集成多个模型的预测结果")

if __name__ == "__main__":
    main()
