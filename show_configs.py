"""
显示所有参数配置
================
"""

from my_parameters import get_my_parameters

def main():
    configs = get_my_parameters()
    
    print(f"📊 总共加载了 {len(configs)} 个配置")
    print("=" * 60)
    
    pso_configs = []
    other_configs = []
    
    for i, config in enumerate(configs, 1):
        if 'PSO' in config['name']:
            pso_configs.append((i, config))
        else:
            other_configs.append((i, config))
    
    print("🔧 原有配置:")
    for i, config in other_configs:
        print(f"  {i:2d}. {config['name']}")
    
    print(f"\n🤖 PSO优化配置 ({len(pso_configs)} 个):")
    for i, config in pso_configs:
        print(f"  {i:2d}. {config['name']}")
        print(f"      序列: {config['n_steps']} → {config['n_pred_steps']}")
        print(f"      架构: {config['n_layers']}层, d_model={config['d_model']}")
        print(f"      训练: lr={config['learning_rate']:.2e}, batch={config['batch_size']}")
        print()

if __name__ == "__main__":
    main()
