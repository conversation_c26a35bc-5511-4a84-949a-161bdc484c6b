"""
测试GPU训练
===========

验证GPU版本PyTorch是否能正常进行训练
"""

from my_parameters import get_my_parameters
from compatible_training_runner import run_compatible_training

def test_gpu_training():
    """测试GPU训练"""
    
    # 获取全数据训练配置
    configs = get_my_parameters()
    config = configs[-1].copy()  # 全数据训练配置
    
    # 修改为快速GPU测试
    config.update({
        'epochs': 3,           # 只训练3轮
        'max_samples': 200,    # 限制样本数量
        'batch_size': 32,      # 适中的批次大小
        'device': 'cuda'       # 确保使用GPU
    })
    
    print("🚀 开始GPU训练测试")
    print("="*50)
    print(f"配置名称: {config['name']}")
    print(f"设备: {config['device']}")
    print(f"训练轮次: {config['epochs']}")
    print(f"样本数量: {config['max_samples']}")
    print(f"批次大小: {config['batch_size']}")
    print()
    
    # 运行训练
    result = run_compatible_training(config)
    
    if result:
        print(f"\n✅ GPU训练测试成功!")
        print(f"训练ID: {result['training_id']}")
        print(f"MAE: {result['mae']:.6f}")
        print(f"R²: {result['r2']:.6f}")
        print("\n🎉 现在您可以使用GPU进行高速训练了!")
        return True
    else:
        print(f"\n❌ GPU训练测试失败!")
        return False

if __name__ == "__main__":
    test_gpu_training()
