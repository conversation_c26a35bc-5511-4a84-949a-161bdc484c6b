"""
Quick Training Demo for TimeMixer++ PyPOTS Integration
=====================================================

This script demonstrates how to quickly train a TimeMixer++ model with different configurations.
"""

import numpy as np
import pandas as pd
import os
import warnings
from pypots.optim import <PERSON>
from pypots.forecasting import TimeMixer
from pypots.nn.functional import calc_mae
from timemixer_plus_plus_config import get_default_configs, create_custom_config

warnings.filterwarnings('ignore')

def load_and_prepare_data(data_path='1964-2017dailyRunoff.csv', 
                         date_column='DATA', 
                         target_column='runoff',
                         n_steps=48, 
                         n_pred_steps=12):
    """Load and prepare data for training."""
    print(f"Loading data from {data_path}...")
    
    # Load data
    df = pd.read_csv(data_path)
    df[date_column] = pd.to_datetime(df[date_column], format='%Y/%m/%d')
    df = df.set_index(date_column)
    
    # Get time series data
    time_series = df[target_column].values.astype(np.float32)
    
    print(f"Original data length: {len(time_series)}")
    print(f"Missing values: {np.isnan(time_series).sum()}")
    
    # Handle missing values
    if np.isnan(time_series).any():
        print("Filling missing values with mean...")
        mean_value = np.nanmean(time_series)
        time_series = np.nan_to_num(time_series, nan=mean_value)
    
    # Create samples
    total_sample_len = n_steps + n_pred_steps
    n_samples = len(time_series) - total_sample_len + 1
    
    if n_samples <= 0:
        raise ValueError(f"Data too short. Need at least {total_sample_len} points.")
    
    # Reshape to (n_samples, total_sample_len, 1)
    dataset_X = np.zeros((n_samples, total_sample_len, 1), dtype=np.float32)
    for i in range(n_samples):
        dataset_X[i, :, 0] = time_series[i:i + total_sample_len]
    
    print(f"Dataset shape: {dataset_X.shape}")
    return dataset_X

def split_data(data_X, n_steps, n_pred_steps, train_ratio=0.7, val_ratio=0.15):
    """Split data for forecasting."""
    n_total_samples = data_X.shape[0]
    train_end = int(n_total_samples * train_ratio)
    val_end = int(n_total_samples * (train_ratio + val_ratio))
    
    # Separate input and prediction parts
    X_input = data_X[:, :n_steps, :]
    X_pred = data_X[:, n_steps:, :]
    
    train_data = {'X': X_input[:train_end], 'X_pred': X_pred[:train_end]}
    val_data = {'X': X_input[train_end:val_end], 'X_pred': X_pred[train_end:val_end]}
    test_data = {'X': X_input[val_end:], 'X_pred': X_pred[val_end:]}
    
    return train_data, val_data, test_data

def train_with_config(config_name='small', custom_config=None, data_path='1964-2017dailyRunoff.csv'):
    """Train TimeMixer++ with specified configuration."""
    print(f"\n{'='*60}")
    print(f"Training with {config_name} configuration")
    print(f"{'='*60}")
    
    # Get configuration
    if custom_config is not None:
        config = custom_config
        print("Using custom configuration")
    else:
        configs = get_default_configs()
        config = configs[config_name]
        print(f"Using {config_name} configuration")

    # Ensure n_pred_steps is set for forecasting
    if config.n_pred_steps is None:
        config.n_pred_steps = config.n_steps // 4  # Default to 1/4 of input steps
    
    print(f"Configuration details:")
    print(f"  - n_steps: {config.n_steps}")
    print(f"  - n_features: {config.n_features}")
    print(f"  - d_model: {config.d_model}")
    print(f"  - n_layers: {config.n_layers}")
    print(f"  - epochs: {config.epochs}")
    
    # Load and prepare data
    dataset_X = load_and_prepare_data(
        data_path=data_path,
        n_steps=config.n_steps,
        n_pred_steps=config.n_pred_steps
    )
    
    # Split data
    train_data, val_data, test_data = split_data(
        dataset_X, config.n_steps, config.n_pred_steps
    )
    
    print(f"\nData split:")
    print(f"  Train: {train_data['X'].shape}")
    print(f"  Val: {val_data['X'].shape}")
    print(f"  Test: {test_data['X'].shape}")
    
    # Create model
    model = TimeMixer(
        n_steps=config.n_steps,
        n_features=1,  # Single feature for univariate data
        n_pred_steps=config.n_pred_steps,
        n_pred_features=1,
        term="short",
        n_layers=config.n_layers,
        top_k=5,
        d_model=config.d_model,
        d_ffn=config.d_model,
        moving_avg=25,
        downsampling_window=2,
        downsampling_layers=1,
        use_norm=True,
        dropout=0.1,
        epochs=config.epochs,
        patience=5,
        optimizer=Adam(lr=1e-3),
        num_workers=0,
        device=None,
        saving_path=f"timemixer_runoff_results/{config_name}",
        model_saving_strategy="best",
    )
    
    print(f"\nModel initialized with {model.n_steps} input steps, {model.n_pred_steps} prediction steps")
    
    # Train model
    print("\nStarting training...")
    model.fit(train_set=train_data, val_set=val_data)
    print("Training completed!")
    
    # Make predictions
    print("\nMaking predictions...")
    results = model.predict(test_data)
    predictions = results["forecasting"]
    
    # Calculate error
    true_values = test_data['X_pred']
    mae = calc_mae(predictions, true_values, np.ones_like(true_values, dtype=int))
    
    print(f"\nResults:")
    print(f"  Prediction shape: {predictions.shape}")
    print(f"  Test MAE: {mae:.4f}")
    
    return model, mae

def main():
    """Main function to demonstrate different configurations."""
    print("TimeMixer++ Quick Training Demo")
    print("="*60)
    
    # Check if data file exists
    data_file = '1964-2017dailyRunoff.csv'
    if not os.path.exists(data_file):
        print(f"Error: Data file {data_file} not found!")
        print("Please make sure the data file is in the current directory.")
        return
    
    results = {}
    
    try:
        # Train with small configuration (fast)
        model_small, mae_small = train_with_config('small')
        results['small'] = mae_small
        
        # Train with custom configuration
        print(f"\n{'='*60}")
        print("Training with custom configuration")
        print(f"{'='*60}")
        
        custom_config = create_custom_config(
            n_steps=24,      # Shorter input sequence for faster training
            n_features=1,
            d_model=64,      # Smaller model
            n_layers=1,
            epochs=5         # Fewer epochs for demo
        )
        
        model_custom, mae_custom = train_with_config('custom', custom_config)
        results['custom'] = mae_custom
        
        # Summary
        print(f"\n{'='*60}")
        print("Training Summary")
        print(f"{'='*60}")
        
        for config_name, mae in results.items():
            print(f"{config_name:10s}: MAE = {mae:.4f}")
        
        print(f"\nAll models trained successfully!")
        print(f"Model files saved in 'timemixer_runoff_results/' directory")
        
    except Exception as e:
        print(f"Error during training: {e}")
        print("Please check the error message above and try again.")

if __name__ == "__main__":
    main()
