"""
生成PSO优化的超参数配置
========================

基于粒子群优化算法的思想，生成多个优化的TimeMixer超参数配置
"""

import random
import numpy as np
from datetime import datetime


def generate_optimized_configs():
    """生成基于PSO思想优化的配置"""
    
    # 设置随机种子以获得可重现的结果
    random.seed(42)
    np.random.seed(42)
    
    configs = []
    
    # 配置1: 高效率配置 (快速收敛)
    config1 = {
        'name': 'PSO优化_高效率',
        'n_steps': 45,
        'n_pred_steps': 7,
        'n_layers': 2,
        'd_model': 96,
        'd_ffn': 192,
        'top_k': 5,
        'moving_avg': 7,
        'downsampling_window': 2,
        'downsampling_layers': 1,
        'dropout': 0.15,
        'use_norm': True,
        'epochs': 80,
        'batch_size': 48,
        'learning_rate': 8e-4,
        'patience': 15,
        'device': 'cuda',
        'num_workers': 0
    }
    configs.append(config1)
    
    # 配置2: 平衡型配置 (性能与效率平衡)
    config2 = {
        'name': 'PSO优化_平衡型',
        'n_steps': 72,
        'n_pred_steps': 12,
        'n_layers': 3,
        'd_model': 160,
        'd_ffn': 320,
        'top_k': 8,
        'moving_avg': 9,
        'downsampling_window': 3,
        'downsampling_layers': 2,
        'dropout': 0.18,
        'use_norm': True,
        'epochs': 120,
        'batch_size': 32,
        'learning_rate': 5e-4,
        'patience': 20,
        'device': 'cuda',
        'num_workers': 0
    }
    configs.append(config2)
    
    # 配置3: 高精度配置 (追求最佳性能)
    config3 = {
        'name': 'PSO优化_高精度',
        'n_steps': 96,
        'n_pred_steps': 14,
        'n_layers': 4,
        'd_model': 224,
        'd_ffn': 448,
        'top_k': 12,
        'moving_avg': 11,
        'downsampling_window': 3,
        'downsampling_layers': 2,
        'dropout': 0.22,
        'use_norm': True,
        'epochs': 180,
        'batch_size': 24,
        'learning_rate': 3e-4,
        'patience': 25,
        'device': 'cuda',
        'num_workers': 0
    }
    configs.append(config3)
    
    # 配置4: 长序列优化配置
    config4 = {
        'name': 'PSO优化_长序列',
        'n_steps': 120,
        'n_pred_steps': 21,
        'n_layers': 3,
        'd_model': 192,
        'd_ffn': 384,
        'top_k': 10,
        'moving_avg': 15,
        'downsampling_window': 4,
        'downsampling_layers': 3,
        'dropout': 0.25,
        'use_norm': True,
        'epochs': 150,
        'batch_size': 20,
        'learning_rate': 2e-4,
        'patience': 30,
        'device': 'cuda',
        'num_workers': 0
    }
    configs.append(config4)
    
    # 配置5: 鲁棒性配置 (强正则化)
    config5 = {
        'name': 'PSO优化_鲁棒性',
        'n_steps': 60,
        'n_pred_steps': 10,
        'n_layers': 2,
        'd_model': 128,
        'd_ffn': 256,
        'top_k': 6,
        'moving_avg': 8,
        'downsampling_window': 2,
        'downsampling_layers': 1,
        'dropout': 0.28,
        'use_norm': True,
        'epochs': 200,
        'batch_size': 40,
        'learning_rate': 4e-4,
        'patience': 35,
        'device': 'cuda',
        'num_workers': 0
    }
    configs.append(config5)
    
    return configs


def format_config_string(config, config_num):
    """格式化配置为字符串"""
    return f"""
        # 🤖 配置{11 + config_num}: {config['name']} (PSO优化)
        {{
            'name': '{config['name']}',
            # 数据相关参数 (PSO优化)
            'n_steps': {config['n_steps']},                    # 输入序列长度
            'n_pred_steps': {config['n_pred_steps']},               # 预测序列长度

            # 模型架构参数 (PSO优化)
            'n_layers': {config['n_layers']},                    # 模型层数
            'd_model': {config['d_model']},                   # 模型维度
            'd_ffn': {config['d_ffn']},                     # 前馈网络维度
            'top_k': {config['top_k']},                       # TimeMixer的top-k参数
            'moving_avg': {config['moving_avg']},                  # 移动平均窗口大小
            'downsampling_window': {config['downsampling_window']},         # 下采样窗口大小
            'downsampling_layers': {config['downsampling_layers']},         # 下采样层数

            # 正则化参数 (PSO优化)
            'dropout': {config['dropout']:.3f},                   # Dropout率
            'use_norm': {config['use_norm']},                 # 是否使用层归一化

            # 训练参数 (PSO优化)
            'epochs': {config['epochs']},                     # 训练轮次
            'batch_size': {config['batch_size']},                 # 批次大小
            'learning_rate': {config['learning_rate']:.2e},            # 学习率
            'patience': {config['patience']},                   # 早停耐心值

            # 系统参数
            'device': '{config['device']}',                 # 计算设备
            'num_workers': {config['num_workers']}                  # 数据加载进程数
        }}"""


def add_configs_to_file():
    """将PSO优化配置添加到my_parameters.py文件"""
    
    print("🚀 生成PSO优化的超参数配置...")
    
    # 生成配置
    configs = generate_optimized_configs()
    
    print(f"✅ 生成了 {len(configs)} 个PSO优化配置:")
    for i, config in enumerate(configs, 1):
        print(f"  {i}. {config['name']}")
    
    # 读取当前文件
    with open('my_parameters.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 生成配置字符串
    pso_configs_str = ""
    for i, config in enumerate(configs, 1):
        pso_configs_str += "," + format_config_string(config, i)
    
    # 找到插入位置 (最后一个}和]之间)
    last_brace_pos = content.rfind('}')
    bracket_pos = content.find(']', last_brace_pos)
    
    if last_brace_pos != -1 and bracket_pos != -1:
        # 插入新配置
        new_content = (content[:last_brace_pos + 1] + 
                      pso_configs_str + 
                      content[bracket_pos:])
        
        # 写入文件
        with open('my_parameters.py', 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"✅ 成功添加 {len(configs)} 个PSO优化配置到 my_parameters.py")
        
        # 验证文件语法
        try:
            with open('my_parameters.py', 'r', encoding='utf-8') as f:
                exec(compile(f.read(), 'my_parameters.py', 'exec'))
            print("✅ 文件语法验证通过")
        except Exception as e:
            print(f"❌ 文件语法错误: {e}")
    else:
        print("❌ 无法找到合适的插入位置")
    
    # 显示配置摘要
    print("\n📊 PSO优化配置摘要:")
    print("=" * 50)
    for i, config in enumerate(configs, 1):
        print(f"\n配置 {i}: {config['name']}")
        print(f"  序列长度: {config['n_steps']} → {config['n_pred_steps']}")
        print(f"  模型架构: {config['n_layers']}层, d_model={config['d_model']}")
        print(f"  训练参数: lr={config['learning_rate']:.2e}, batch={config['batch_size']}")


if __name__ == "__main__":
    add_configs_to_file()
    print("\n🎉 PSO优化配置生成完成!")
    print("💡 提示: 现在可以运行训练脚本来测试这些优化配置")
