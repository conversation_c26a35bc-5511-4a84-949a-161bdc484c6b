"""
我的参数配置文件
================

在这里定义您想要测试的参数组合。
修改下面的参数值，然后运行训练。
"""

def get_my_parameters():
    """
    🎯 基于LightGBM特征选择的TimeMixer配置 - 5个最重要特征

    基于47年气象数据(1971-2017)和LightGBM特征重要性分析
    输入特征: 5个最重要变量 (RUNOFF, VISIB, SLP, PRCP, DEWP)
    输出目标: 1个径流变量 (RUNOFF未来值)
    特征选择: LightGBM分析，R²=0.9504
    """

    # 📊 LightGBM特征重要性分析结果：
    # - 总数据量: 17,169天 (47年: 1971-2017)
    # - 输入特征: 5个最重要变量
    #   1. RUNOFF (98.6% 重要性) - 径流历史值
    #   2. VISIB (0.2% 重要性) - 能见度
    #   3. SLP (0.2% 重要性) - 海平面气压
    #   4. PRCP (0.2% 重要性) - 降水量
    #   5. DEWP (0.2% 重要性) - 露点温度
    # - LightGBM性能: MAE=220.07, RMSE=478.20, R²=0.9504
    # - 优势: 减少特征数量，提高训练效率，保持预测精度

    # 🔧 完整参数说明：
    # 数据参数: n_steps, n_pred_steps
    # 模型架构: n_layers, d_model, d_ffn, top_k, moving_avg, downsampling_window, downsampling_layers
    # 正则化: dropout, use_norm
    # 训练参数: epochs, batch_size, learning_rate, patience
    # 系统参数: device, num_workers

    my_configs = [
        # 🚀 配置1: 快速验证 (LightGBM选择的5个特征)
        {
            'name': '快速验证_LightGBM5特征',
            # 数据相关参数
            'n_steps': 30,                    # 输入序列长度 (30天历史)
            'n_pred_steps': 7,                # 预测序列长度 (7天预测)
            'max_samples': 1000,              # 限制样本数量(快速验证)

            # 模型架构参数 (适配5个最重要特征)
            'n_layers': 2,                    # 模型层数
            'd_model': 128,                   # 模型维度 (适配5个特征)
            'd_ffn': 256,                     # 前馈网络维度
            'top_k': 5,                       # TimeMixer的top-k参数
            'moving_avg': 7,                  # 移动平均窗口大小
            'downsampling_window': 2,         # 下采样窗口大小
            'downsampling_layers': 1,         # 下采样层数

            # 正则化参数
            'dropout': 0.1,                   # Dropout率 (特征减少，降低正则化)
            'use_norm': True,                 # 是否使用层归一化

            # 训练参数
            'epochs': 50,                     # 训练轮次
            'batch_size': 32,                 # 批次大小
            'learning_rate': 1e-3,            # 学习率
            'patience': 10,                   # 早停耐心值

            # 系统参数
            'device': 'cuda',                 # 计算设备
            'num_workers': 0                  # 数据加载进程数
        },

        # 🌊 配置2: 季节性预测 (多变量中等复杂度)
        {
            'name': '季节性预测_多变量中等',
            # 数据相关参数
            'n_steps': 60,                    # 两个月历史数据
            'n_pred_steps': 14,               # 预测两周

            # 模型架构参数 (适配多变量)
            'n_layers': 3,                    # 更深网络
            'd_model': 192,                   # 更大维度 (适配9个特征)
            'd_ffn': 384,                     # 更大前馈网络
            'top_k': 7,                       # 更大top-k
            'moving_avg': 7,                  # 移动平均窗口
            'downsampling_window': 3,         # 更大下采样窗口
            'downsampling_layers': 2,         # 更多下采样层

            # 正则化参数
            'dropout': 0.2,                   # 中度正则化 (多变量需要更强)
            'use_norm': True,                 # 使用层归一化

            # 训练参数
            'epochs': 100,                    # 训练轮次
            'batch_size': 24,                 # 中等批次
            'learning_rate': 5e-4,            # 中等学习率
            'patience': 20,                   # 中等耐心

            # 系统参数
            'device': 'cuda',                 # GPU加速
            'num_workers': 0                  # 单进程
        },

        # 📅 配置3: 长期预测 (多变量高复杂度)
        {
            'name': '长期预测_多变量高复杂',
            # 数据相关参数
            'n_steps': 180,                   # 半年历史数据
            'n_pred_steps': 30,               # 预测一个月

            # 模型架构参数 (高复杂度多变量)
            'n_layers': 4,                    # 深层网络
            'd_model': 256,                   # 大维度
            'd_ffn': 512,                     # 大前馈网络
            'top_k': 10,                      # 大top-k
            'moving_avg': 14,                 # 移动平均窗口 (两周)
            'downsampling_window': 4,         # 大下采样窗口
            'downsampling_layers': 3,         # 多下采样层

            # 正则化参数
            'dropout': 0.25,                  # 强正则化 (防止多变量过拟合)
            'use_norm': True,                 # 使用层归一化

            # 训练参数
            'epochs': 150,                    # 长期训练
            'batch_size': 16,                 # 小批次(内存考虑)
            'learning_rate': 2e-4,            # 低学习率
            'patience': 25,                   # 大耐心

            # 系统参数
            'device': 'cuda',                 # GPU加速
            'num_workers': 0                  # 单进程
        },

        # 🎯 配置4: 极端事件预测 (最高复杂度)
        {
            'name': '极端事件预测_最高',
            # 数据相关参数
            'n_steps': 180,                   # 半年历史数据
            'n_pred_steps': 14,               # 预测两周

            # 模型架构参数
            'n_layers': 4,                    # 最深网络
            'd_model': 384,                   # 最大维度
            'd_ffn': 768,                     # 最大前馈网络
            'top_k': 15,                      # 最大top-k
            'moving_avg': 10,                 # 移动平均窗口 (避免维度问题)
            'downsampling_window': 5,         # 最大下采样窗口
            'downsampling_layers': 3,         # 最多下采样层

            # 正则化参数
            'dropout': 0.25,                  # 最强正则化
            'use_norm': True,                 # 使用层归一化

            # 训练参数
            'epochs': 100,                    # 最长训练
            'batch_size': 8,                  # 最小批次
            'learning_rate': 1e-4,            # 最低学习率
            'patience': 20,                   # 最大耐心

            # 系统参数
            'device': 'cuda',                 # GPU加速
            'num_workers': 0                  # 单进程
        },

        # ⚡ 配置5: 学习率实验
        {
            'name': '学习率实验_高LR',
            # 数据相关参数
            'n_steps': 72,                    # 中等序列长度
            'n_pred_steps': 18,               # 中等预测长度

            # 模型架构参数
            'n_layers': 2,                    # 标准层数
            'd_model': 96,                    # 中等维度
            'd_ffn': 192,                     # 中等前馈网络
            'top_k': 5,                       # 标准top-k
            'moving_avg': 5,                  # 移动平均窗口 (避免维度问题)
            'downsampling_window': 2,         # 标准下采样窗口
            'downsampling_layers': 1,         # 标准下采样层

            # 正则化参数
            'dropout': 0.15,                  # 中度正则化
            'use_norm': True,                 # 使用层归一化

            # 训练参数
            'epochs': 30,                     # 中等训练轮次
            'batch_size': 32,                 # 标准批次
            'learning_rate': 2e-3,            # 高学习率实验
            'patience': 8,                    # 中等耐心

            # 系统参数
            'device': 'cuda',                 # GPU加速
            'num_workers': 0                  # 单进程
        },

        # 🔬 配置6: 架构实验 (宽网络)
        {
            'name': '架构实验_宽网络',
            # 数据相关参数
            'n_steps': 96,                    # 标准序列长度
            'n_pred_steps': 24,               # 标准预测长度
            'max_samples': 5000,              # 中等样本数量

            # 模型架构参数
            'n_layers': 2,                    # 浅层但宽网络
            'd_model': 512,                   # 超大维度
            'd_ffn': 1024,                    # 超大前馈网络
            'top_k': 10,                      # 大top-k
            'moving_avg': 10,                 # 移动平均窗口 (避免维度问题)
            'downsampling_window': 3,         # 中等下采样窗口
            'downsampling_layers': 2,         # 中等下采样层

            # 正则化参数
            'dropout': 0.2,                   # 强正则化(防止过拟合)
            'use_norm': True,                 # 使用层归一化

            # 训练参数
            'epochs': 60,                     # 充分训练
            'batch_size': 16,                 # 小批次(内存限制)
            'learning_rate': 3e-4,            # 低学习率
            'patience': 12,                   # 大耐心

            # 系统参数
            'device': 'cuda',                 # GPU加速
            'num_workers': 0                  # 单进程
        },

        # 🌟 配置7: 全数据训练 (多变量最大规模)
        {
            'name': '全数据训练_多变量最大规模',
            # 数据相关参数
            'n_steps': 120,                    # 90天历史 (3个月)
            'n_pred_steps': 7,               # 预测15天
            # 不设置max_samples，使用全部17,169天数据

            # 模型架构参数 (最大规模多变量)
            'n_layers': 4,                    # 深层网络
            'd_model': 384,                   # 大维度 (适配9个特征)
            'd_ffn': 768,                     # 大前馈网络
            'top_k': 12,                      # 大top-k
            'moving_avg': 14,                 # 移动平均窗口 (两周)
            'downsampling_window': 3,         # 标准下采样窗口
            'downsampling_layers': 2,         # 标准下采样层

            # 正则化参数
            'dropout': 0.2,                   # 中度正则化
            'use_norm': True,                 # 使用层归一化

            # 训练参数
            'epochs': 300,                    # 200轮训练
            'batch_size': 32,                 # 中等批次 (多变量内存需求大)
            'learning_rate': 5e-4,            # 中等学习率
            'patience': 30,                   # 大耐心值

            # 系统参数
            'device': 'cuda',                 # GPU加速
            'num_workers': 0                  # 单进程
        },

        # 🎯 配置8: LightGBM优化配置 (3个最重要特征)
        {
            'name': '稳定防过拟合_TOP3_全数据',
            # 数据相关参数
            'n_steps': 60,                    # 60天历史 (2个月，稳定长度)
            'n_pred_steps': 5,               # 预测7天
            # 使用全部数据，不设置max_samples

            # 模型架构参数 (简单稳定，防过拟合)
            'n_layers': 2,                    # 浅网络防过拟合
            'd_model': 128,                   # 适中维度
            'd_ffn': 256,                     # 适中前馈网络
            'top_k': 3,                       # 与特征数相同
            'moving_avg': 5,                  # 短移动平均，避免维度问题
            'downsampling_window': 2,         # 小下采样窗口
            'downsampling_layers': 1,         # 少下采样层

            # 强正则化参数
            'dropout': 0.3,                   # 高dropout防过拟合
            'use_norm': True,                 # 使用层归一化

            # 训练参数
            'epochs': 150,                    # 适中训练轮次
            'batch_size': 64,                 # 大批次稳定训练
            'learning_rate': 5e-4,            # 较低学习率
            'patience': 20,                   # 早停防过拟合

            # 系统参数
            'device': 'cuda',                 # GPU加速
            'num_workers': 0                  # 单进程
        },

         # 🎯 配置9 : LightGBM优化配置 best
        {
            'name': '稳定防过拟合_TOP3_全数据',
            # 数据相关参数
            'n_steps': 60,                    # 60天历史 (2个月，稳定长度)
            'n_pred_steps': 5,               # 预测7天
            # 使用全部数据，不设置max_samples

            # 模型架构参数 (简单稳定，防过拟合)
            'n_layers': 2,                    # 浅网络防过拟合
            'd_model': 128,                   # 适中维度
            'd_ffn': 256,                     # 适中前馈网络
            'top_k': 3,                       # 与特征数相同
            'moving_avg': 7,                  # 短移动平均，避免维度问题
            'downsampling_window': 2,         # 小下采样窗口
            'downsampling_layers': 3,         # 少下采样层

            # 强正则化参数
            'dropout': 0.3,                   # 高dropout防过拟合
            'use_norm': True,                 # 使用层归一化

            # 训练参数
            'epochs': 300,                    # 适中训练轮次
            'batch_size': 64,                 # 大批次稳定训练
            'learning_rate': 5e-4,            # 较低学习率
            'patience': 20,                   # 早停防过拟合

            # 系统参数
            'device': 'cuda',                 # GPU加速
            'num_workers': 0                  # 单进程
        },

   # 🎯 配置10 : LightGBM优化配置测试用

        {
            'name': '稳定防过拟合_TOP3_全数据',
            # 数据相关参数
            'n_steps': 60,                    # 60天历史 (2个月，稳定长度)
            'n_pred_steps': 5,               # 预测7天
            # 使用全部数据，不设置max_samples

            # 模型架构参数 (简单稳定，防过拟合)
            'n_layers': 2,                    # 浅网络防过拟合
            'd_model': 128,                   # 适中维度
            'd_ffn': 256,                     # 适中前馈网络
            'top_k': 3,                       # 与特征数相同
            'moving_avg': 7,                  # 短移动平均，避免维度问题
            'downsampling_window': 2,         # 小下采样窗口
            'downsampling_layers': 3,         # 少下采样层

            # 强正则化参数
            'dropout': 0.3,                   # 高dropout防过拟合
            'use_norm': True,                 # 使用层归一化

            # 训练参数
            'epochs': 400,                    # 适中训练轮次
            'batch_size': 64,                 # 大批次稳定训练
            'learning_rate': 25e-5,            # 较低学习率
            'patience': 50,                   # 早停防过拟合

            # 系统参数
            'device': 'cuda',                 # GPU加速
            'num_workers': 0                  # 单进程
        },


           # 🎯 配置11 : LightGBM优化配置 (3个最重要特征)修改下采样层数，更改维度和层数
        {
            'name': '稳定防过拟合_TOP3_全数据',
            # 数据相关参数
            'n_steps': 90,                    # 60天历史 (2个月，稳定长度)
            'n_pred_steps': 5,               # 预测7天
            # 使用全部数据，不设置max_samples

            # 模型架构参数 (简单稳定，防过拟合)
            'n_layers': 3,                    # 浅网络防过拟合
            'd_model': 128,                   # 适中维度
            'd_ffn': 256,                     # 适中前馈网络
            'top_k': 3,                       # 与特征数相同
            'moving_avg': 7,                  # 短移动平均，避免维度问题
            'downsampling_window': 2,         # 小下采样窗口
            'downsampling_layers': 3,         # 少下采样层

            # 强正则化参数
            'dropout': 0.3,                   # 高dropout防过拟合
            'use_norm': True,                 # 使用层归一化

            # 训练参数
            'epochs': 400,                    # 适中训练轮次
            'batch_size': 64,                 # 大批次稳定训练
            'learning_rate': 5e-4,            # 较低学习率
            'patience': 40,                   # 早停防过拟合

            # 系统参数
            'device': 'cuda',                 # GPU加速
            'num_workers': 0                  # 单进程
        },

        # 🤖 配置12: PSO优化_高效率 (PSO优化)
        {
            'name': 'PSO优化_高效率',
            # 数据相关参数 (PSO优化)
            'n_steps': 45,                    # 输入序列长度
            'n_pred_steps': 7,               # 预测序列长度

            # 模型架构参数 (PSO优化)
            'n_layers': 2,                    # 模型层数
            'd_model': 96,                   # 模型维度
            'd_ffn': 192,                     # 前馈网络维度
            'top_k': 5,                       # TimeMixer的top-k参数
            'moving_avg': 7,                  # 移动平均窗口大小
            'downsampling_window': 2,         # 下采样窗口大小
            'downsampling_layers': 1,         # 下采样层数

            # 正则化参数 (PSO优化)
            'dropout': 0.150,                   # Dropout率
            'use_norm': True,                 # 是否使用层归一化

            # 训练参数 (PSO优化)
            'epochs': 80,                     # 训练轮次
            'batch_size': 48,                 # 批次大小
            'learning_rate': 8.00e-04,            # 学习率
            'patience': 15,                   # 早停耐心值

            # 系统参数
            'device': 'cuda',                 # 计算设备
            'num_workers': 0                  # 数据加载进程数
        },

        # 🤖 配置13: PSO优化_平衡型 (PSO优化)
        {
            'name': 'PSO优化_平衡型',
            # 数据相关参数 (PSO优化)
            'n_steps': 72,                    # 输入序列长度
            'n_pred_steps': 12,               # 预测序列长度

            # 模型架构参数 (PSO优化)
            'n_layers': 3,                    # 模型层数
            'd_model': 160,                   # 模型维度
            'd_ffn': 320,                     # 前馈网络维度
            'top_k': 8,                       # TimeMixer的top-k参数
            'moving_avg': 9,                  # 移动平均窗口大小
            'downsampling_window': 3,         # 下采样窗口大小
            'downsampling_layers': 2,         # 下采样层数

            # 正则化参数 (PSO优化)
            'dropout': 0.180,                   # Dropout率
            'use_norm': True,                 # 是否使用层归一化

            # 训练参数 (PSO优化)
            'epochs': 120,                     # 训练轮次
            'batch_size': 32,                 # 批次大小
            'learning_rate': 5.00e-04,            # 学习率
            'patience': 20,                   # 早停耐心值

            # 系统参数
            'device': 'cuda',                 # 计算设备
            'num_workers': 0                  # 数据加载进程数
        },

        # 🤖 配置14: PSO优化_高精度 (PSO优化)
        {
            'name': 'PSO优化_高精度',
            # 数据相关参数 (PSO优化)
            'n_steps': 96,                    # 输入序列长度
            'n_pred_steps': 14,               # 预测序列长度

            # 模型架构参数 (PSO优化)
            'n_layers': 4,                    # 模型层数
            'd_model': 224,                   # 模型维度
            'd_ffn': 448,                     # 前馈网络维度
            'top_k': 12,                       # TimeMixer的top-k参数
            'moving_avg': 11,                  # 移动平均窗口大小
            'downsampling_window': 3,         # 下采样窗口大小
            'downsampling_layers': 2,         # 下采样层数

            # 正则化参数 (PSO优化)
            'dropout': 0.220,                   # Dropout率
            'use_norm': True,                 # 是否使用层归一化

            # 训练参数 (PSO优化)
            'epochs': 180,                     # 训练轮次
            'batch_size': 24,                 # 批次大小
            'learning_rate': 3.00e-04,            # 学习率
            'patience': 25,                   # 早停耐心值

            # 系统参数
            'device': 'cuda',                 # 计算设备
            'num_workers': 0                  # 数据加载进程数
        },

        # 🤖 配置15: PSO优化_长序列 (PSO优化)
        {
            'name': 'PSO优化_长序列',
            # 数据相关参数 (PSO优化)
            'n_steps': 120,                    # 输入序列长度
            'n_pred_steps': 21,               # 预测序列长度

            # 模型架构参数 (PSO优化)
            'n_layers': 3,                    # 模型层数
            'd_model': 192,                   # 模型维度
            'd_ffn': 384,                     # 前馈网络维度
            'top_k': 10,                       # TimeMixer的top-k参数
            'moving_avg': 15,                  # 移动平均窗口大小
            'downsampling_window': 4,         # 下采样窗口大小
            'downsampling_layers': 3,         # 下采样层数

            # 正则化参数 (PSO优化)
            'dropout': 0.250,                   # Dropout率
            'use_norm': True,                 # 是否使用层归一化

            # 训练参数 (PSO优化)
            'epochs': 150,                     # 训练轮次
            'batch_size': 20,                 # 批次大小
            'learning_rate': 2.00e-04,            # 学习率
            'patience': 30,                   # 早停耐心值

            # 系统参数
            'device': 'cuda',                 # 计算设备
            'num_workers': 0                  # 数据加载进程数
        },

        # 🤖 配置16: PSO优化_鲁棒性 (PSO优化)
        {
            'name': 'PSO优化_鲁棒性',
            # 数据相关参数 (PSO优化)
            'n_steps': 60,                    # 输入序列长度
            'n_pred_steps': 10,               # 预测序列长度

            # 模型架构参数 (PSO优化)
            'n_layers': 2,                    # 模型层数
            'd_model': 128,                   # 模型维度
            'd_ffn': 256,                     # 前馈网络维度
            'top_k': 6,                       # TimeMixer的top-k参数
            'moving_avg': 8,                  # 移动平均窗口大小
            'downsampling_window': 2,         # 下采样窗口大小
            'downsampling_layers': 1,         # 下采样层数

            # 正则化参数 (PSO优化)
            'dropout': 0.280,                   # Dropout率
            'use_norm': True,                 # 是否使用层归一化

            # 训练参数 (PSO优化)
            'epochs': 200,                     # 训练轮次
            'batch_size': 40,                 # 批次大小
            'learning_rate': 4.00e-04,            # 学习率
            'patience': 35,                   # 早停耐心值

            # 系统参数
            'device': 'cuda',                 # 计算设备
            'num_workers': 0                  # 数据加载进程数
        }

    ]

    return my_configs

# 💡 使用示例：
# 1. 修改上面的参数值
# 2. 运行: python run_my_training.py
# 3. 查看结果: timemixer_evaluation_results.csv

if __name__ == "__main__":
    configs = get_my_parameters()
    print("您定义的参数配置:")
    for i, config in enumerate(configs, 1):
        print(f"\n{i}. {config['name']}")
        for key, value in config.items():
            if key != 'name':
                print(f"   {key}: {value}")

