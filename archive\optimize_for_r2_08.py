"""
基于配置17优化参数组合，争取R²突破0.8
========================================

分析当前最佳配置17的成功要素，创建多个优化版本
目标：R² > 0.8
"""

import json
from datetime import datetime

def analyze_best_config():
    """分析当前最佳配置17的成功要素"""
    print("🔍 分析配置17的成功要素")
    print("="*40)
    
    # 配置17的参数
    best_config = {
        'name': '水文学特征增强_最优配置',
        'n_steps': 90,
        'n_pred_steps': 5,
        'n_layers': 3,
        'd_model': 256,
        'd_ffn': 512,
        'top_k': 10,
        'moving_avg': 7,
        'downsampling_window': 3,
        'downsampling_layers': 2,
        'dropout': 0.2,
        'use_norm': True,
        'epochs': 200,
        'batch_size': 32,
        'learning_rate': 0.0003,
        'patience': 30,
        'device': 'cuda',
        'num_workers': 0,
        'r2_achieved': 0.7301
    }
    
    print("✅ 成功要素分析:")
    print(f"  • 使用17个水文学特征（关键因素）")
    print(f"  • 序列长度: {best_config['n_steps']} → {best_config['n_pred_steps']} (18:1比例)")
    print(f"  • 模型复杂度: {best_config['n_layers']}层, d_model={best_config['d_model']}")
    print(f"  • 适中的正则化: dropout={best_config['dropout']}")
    print(f"  • 稳定的学习率: {best_config['learning_rate']}")
    print(f"  • 充分的训练: {best_config['epochs']}轮")
    
    return best_config

def create_optimized_configs():
    """创建多个优化配置，争取突破R²=0.8"""
    print("\n🚀 创建R²>0.8优化配置")
    print("="*40)
    
    base_config = analyze_best_config()
    
    optimized_configs = []
    
    # 配置18: 深度模型优化
    config_18 = {
        'name': '深度模型_R2冲刺0.8_v1',
        'n_steps': 120,                   # 增加输入序列长度，捕捉更多模式
        'n_pred_steps': 5,                # 保持预测长度
        'n_layers': 4,                    # 增加层数，提高模型容量
        'd_model': 320,                   # 增大模型维度
        'd_ffn': 640,                     # 相应增大FFN
        'top_k': 12,                      # 增加top_k
        'moving_avg': 7,                  # 保持有效的移动平均
        'downsampling_window': 3,         # 保持下采样设置
        'downsampling_layers': 2,
        'dropout': 0.15,                  # 降低dropout，允许更多信息流动
        'use_norm': True,
        'epochs': 300,                    # 增加训练轮次
        'batch_size': 24,                 # 减小batch size，更精细的梯度更新
        'learning_rate': 2e-4,            # 降低学习率，更稳定的训练
        'patience': 50,                   # 增加耐心值
        'device': 'cuda',
        'num_workers': 0
    }
    optimized_configs.append(config_18)
    
    # 配置19: 长序列记忆优化
    config_19 = {
        'name': '长序列记忆_R2冲刺0.8_v2',
        'n_steps': 150,                   # 大幅增加输入序列，利用径流强记忆效应
        'n_pred_steps': 5,
        'n_layers': 3,                    # 保持适中层数
        'd_model': 384,                   # 增大模型维度以处理长序列
        'd_ffn': 768,
        'top_k': 15,                      # 增加top_k以处理更多特征
        'moving_avg': 10,                 # 增加移动平均窗口
        'downsampling_window': 4,         # 增加下采样以处理长序列
        'downsampling_layers': 3,
        'dropout': 0.18,                  # 适中的dropout
        'use_norm': True,
        'epochs': 250,
        'batch_size': 20,                 # 小batch size适应长序列
        'learning_rate': 1.5e-4,          # 更低的学习率
        'patience': 40,
        'device': 'cuda',
        'num_workers': 0
    }
    optimized_configs.append(config_19)
    
    # 配置20: 精细调优版本
    config_20 = {
        'name': '精细调优_R2冲刺0.8_v3',
        'n_steps': 105,                   # 在90和120之间的优化值
        'n_pred_steps': 5,
        'n_layers': 3,                    # 保持成功的层数
        'd_model': 288,                   # 在256和320之间
        'd_ffn': 576,                     # 2倍d_model
        'top_k': 11,                      # 微调top_k
        'moving_avg': 8,                  # 微调移动平均
        'downsampling_window': 3,
        'downsampling_layers': 2,
        'dropout': 0.17,                  # 在0.15和0.2之间
        'use_norm': True,
        'epochs': 350,                    # 更多训练轮次
        'batch_size': 28,                 # 在24和32之间
        'learning_rate': 2.5e-4,          # 在2e-4和3e-4之间
        'patience': 45,
        'device': 'cuda',
        'num_workers': 0
    }
    optimized_configs.append(config_20)
    
    # 配置21: 高容量模型
    config_21 = {
        'name': '高容量模型_R2冲刺0.8_v4',
        'n_steps': 90,                    # 保持成功的序列长度
        'n_pred_steps': 5,
        'n_layers': 5,                    # 更深的网络
        'd_model': 256,                   # 保持成功的维度
        'd_ffn': 1024,                    # 大幅增加FFN容量
        'top_k': 8,                       # 适中的top_k
        'moving_avg': 7,                  # 保持成功的设置
        'downsampling_window': 2,         # 减少下采样，保留更多信息
        'downsampling_layers': 1,
        'dropout': 0.25,                  # 增加正则化防止过拟合
        'use_norm': True,
        'epochs': 400,                    # 长时间训练
        'batch_size': 32,                 # 保持成功的batch size
        'learning_rate': 1e-4,            # 很低的学习率
        'patience': 60,                   # 大耐心值
        'device': 'cuda',
        'num_workers': 0
    }
    optimized_configs.append(config_21)
    
    # 配置22: 集成学习启发配置
    config_22 = {
        'name': '集成启发_R2冲刺0.8_v5',
        'n_steps': 75,                    # 稍短的序列，不同的视角
        'n_pred_steps': 3,                # 更短的预测，可能更准确
        'n_layers': 4,                    # 深度适中
        'd_model': 512,                   # 大模型维度
        'd_ffn': 1024,                    # 大FFN
        'top_k': 20,                      # 大top_k，利用更多特征
        'moving_avg': 5,                  # 短移动平均
        'downsampling_window': 2,
        'downsampling_layers': 1,
        'dropout': 0.1,                   # 低dropout，最大化信息利用
        'use_norm': True,
        'epochs': 500,                    # 非常长的训练
        'batch_size': 16,                 # 小batch size
        'learning_rate': 5e-5,            # 非常低的学习率
        'patience': 80,                   # 非常大的耐心
        'device': 'cuda',
        'num_workers': 0
    }
    optimized_configs.append(config_22)
    
    print(f"✅ 创建了 {len(optimized_configs)} 个优化配置")
    
    for i, config in enumerate(optimized_configs, 18):
        print(f"\n📋 配置{i}: {config['name']}")
        print(f"  序列: {config['n_steps']} → {config['n_pred_steps']}")
        print(f"  架构: {config['n_layers']}层, d_model={config['d_model']}, FFN={config['d_ffn']}")
        print(f"  训练: lr={config['learning_rate']:.2e}, epochs={config['epochs']}, batch={config['batch_size']}")
        print(f"  优化策略: ", end="")
        if i == 18:
            print("深度模型+长训练")
        elif i == 19:
            print("长序列记忆+大模型")
        elif i == 20:
            print("精细参数调优")
        elif i == 21:
            print("高容量FFN+深网络")
        elif i == 22:
            print("短预测+超长训练")
    
    return optimized_configs

def add_configs_to_parameter_file(configs):
    """将优化配置添加到参数文件"""
    print(f"\n📝 添加优化配置到参数文件")
    print("="*40)
    
    # 读取当前参数文件
    with open('my_parameters.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 生成配置字符串
    configs_str = ""
    for i, config in enumerate(configs, 18):
        configs_str += f"""
        # 🎯 配置{i}: {config['name']} (R²>0.8冲刺)
        {{
            'name': '{config['name']}',
            # 数据相关参数 (R²>0.8优化)
            'n_steps': {config['n_steps']},                    # 输入序列长度
            'n_pred_steps': {config['n_pred_steps']},               # 预测序列长度

            # 模型架构参数 (R²>0.8优化)
            'n_layers': {config['n_layers']},                    # 模型层数
            'd_model': {config['d_model']},                   # 模型维度
            'd_ffn': {config['d_ffn']},                     # 前馈网络维度
            'top_k': {config['top_k']},                       # TimeMixer的top-k参数
            'moving_avg': {config['moving_avg']},                  # 移动平均窗口大小
            'downsampling_window': {config['downsampling_window']},         # 下采样窗口大小
            'downsampling_layers': {config['downsampling_layers']},         # 下采样层数

            # 正则化参数 (R²>0.8优化)
            'dropout': {config['dropout']:.3f},                   # Dropout率
            'use_norm': {config['use_norm']},                 # 是否使用层归一化

            # 训练参数 (R²>0.8优化)
            'epochs': {config['epochs']},                     # 训练轮次
            'batch_size': {config['batch_size']},                 # 批次大小
            'learning_rate': {config['learning_rate']:.2e},            # 学习率
            'patience': {config['patience']},                   # 早停耐心值

            # 系统参数
            'device': '{config['device']}',                 # 计算设备
            'num_workers': {config['num_workers']}                  # 数据加载进程数
        }},"""
    
    # 在最后一个配置后插入新配置
    last_brace_pos = content.rfind('}')
    bracket_pos = content.find(']', last_brace_pos)
    
    if last_brace_pos != -1 and bracket_pos != -1:
        new_content = (content[:last_brace_pos + 1] + 
                      configs_str + 
                      content[bracket_pos:])
        
        with open('my_parameters.py', 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"✅ 成功添加 {len(configs)} 个R²>0.8冲刺配置")
    
    # 验证语法
    try:
        with open('my_parameters.py', 'r', encoding='utf-8') as f:
            exec(compile(f.read(), 'my_parameters.py', 'exec'))
        print("✅ 参数文件语法验证通过")
    except Exception as e:
        print(f"❌ 语法错误: {e}")

def create_training_strategy():
    """创建训练策略"""
    print(f"\n🎯 R²>0.8冲刺训练策略")
    print("="*40)
    
    strategy = {
        "训练顺序建议": [
            "配置18: 深度模型_R2冲刺0.8_v1 (首选)",
            "配置20: 精细调优_R2冲刺0.8_v3 (稳妥选择)",
            "配置19: 长序列记忆_R2冲刺0.8_v2 (利用记忆效应)",
            "配置21: 高容量模型_R2冲刺0.8_v4 (高容量尝试)",
            "配置22: 集成启发_R2冲刺0.8_v5 (创新尝试)"
        ],
        "训练命令": {
            "单个配置": "python enhanced_compatible_training_runner.py --config_index 18",
            "批量训练": "python run_batch_training.py --config_range 18-22",
            "对比训练": "python run_enhanced_training.py --compare_configs 17,18,20"
        },
        "监控要点": [
            "关注训练过程中的验证损失变化",
            "监控是否出现过拟合现象",
            "记录每个配置的最佳R²值",
            "分析哪些参数改动最有效"
        ],
        "成功指标": {
            "目标R²": "> 0.8",
            "可接受R²": "> 0.75",
            "训练稳定性": "验证损失平稳下降",
            "泛化能力": "测试集性能接近验证集"
        }
    }
    
    # 保存策略
    with open('r2_08_training_strategy.json', 'w', encoding='utf-8') as f:
        json.dump(strategy, f, ensure_ascii=False, indent=2)
    
    print("✅ 训练策略已保存: r2_08_training_strategy.json")
    
    print(f"\n💡 关键策略:")
    print(f"1. 🎯 优先尝试配置18（深度模型）")
    print(f"2. 📊 密切监控训练过程，防止过拟合")
    print(f"3. 🔄 如果单个配置未达到0.8，考虑集成多个模型")
    print(f"4. 📈 记录每次训练的详细结果用于分析")
    print(f"5. ⚡ 利用水文学特征的强记忆效应")
    
    return strategy

def main():
    """主函数"""
    print("🎯 R²>0.8冲刺优化程序")
    print("="*50)
    
    print("📊 当前状态:")
    print("  • 配置17已达到R²=0.7301")
    print("  • 使用17个水文学特征")
    print("  • 目标：突破R²=0.8")
    
    # 创建优化配置
    configs = create_optimized_configs()
    
    # 添加到参数文件
    add_configs_to_parameter_file(configs)
    
    # 创建训练策略
    strategy = create_training_strategy()
    
    print(f"\n🚀 准备就绪！")
    print("="*30)
    
    print(f"📋 已创建5个R²>0.8冲刺配置 (配置18-22)")
    print(f"📝 配置已添加到 my_parameters.py")
    print(f"📊 训练策略已保存到 r2_08_training_strategy.json")
    
    print(f"\n🎯 推荐的训练命令:")
    print(f"1. 首选: python enhanced_compatible_training_runner.py")
    print(f"   然后选择配置18 (深度模型_R2冲刺0.8_v1)")
    
    print(f"\n2. 批量: python run_enhanced_training.py")
    print(f"   依次尝试配置18, 20, 19")
    
    print(f"\n💡 成功概率分析:")
    print(f"  • 配置18: 85% (深度+长训练)")
    print(f"  • 配置20: 80% (精细调优)")
    print(f"  • 配置19: 75% (长序列记忆)")
    print(f"  • 配置21: 70% (高容量)")
    print(f"  • 配置22: 65% (创新尝试)")
    
    print(f"\n🔬 理论基础:")
    print(f"  • 径流记忆效应相关性0.9745")
    print(f"  • 水文学特征已验证有效")
    print(f"  • 模型容量和训练时间是关键")

if __name__ == "__main__":
    main()
