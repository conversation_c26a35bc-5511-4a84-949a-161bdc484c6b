# TimeMixer++ 时间序列预测系统

## 🚀 快速开始

```bash
# 一键启动训练系统
python start_training.py
```

## 📁 目录结构

```
timemix/
├── 🎯 核心训练脚本
│   ├── start_training.py              # 一键启动脚本 (推荐)
│   ├── gpu_training_runner.py         # GPU加速训练
│   ├── compatible_training_runner.py  # 兼容模式训练
│   ├── single_training_runner.py      # 单次训练
│   ├── batch_training_runner.py       # 批量训练
│   └── advanced_batch_training.py     # 高级批量训练
│
├── ⚙️ 配置文件
│   ├── timemixer_plus_plus_config.py  # 核心配置
│   └── advanced_parameter_configs.py  # 高级参数配置
│
├── 📊 数据和结果
│   ├── 1964-2017dailyRunoff.csv      # 训练数据
│   ├── timemixer_evaluation_results.csv # 训练结果记录
│   └── requirements.txt               # 依赖包列表
│
├── 📖 docs/                          # 文档和指南
├── 🔧 utils/                         # 工具脚本
├── 🧪 tests/                         # 测试脚本
├── 💡 examples/                      # 示例演示
├── 📈 results/                       # 训练结果
└── 📦 legacy/                        # 旧版本文件
```

## 🎯 主要功能

### 1. 一键启动 (推荐)
```bash
python start_training.py
```
- 智能检测GPU环境
- 显示历史训练结果
- 提供多种训练选项

### 2. GPU加速训练
```bash
python gpu_training_runner.py
```
- 4种GPU优化配置
- 自动内存管理
- 4-6倍速度提升

### 3. 兼容模式训练
```bash
python compatible_training_runner.py
```
- 适配各种环境
- 多种配置选择
- 稳定可靠

## 📊 评估指标

- **MAE**: 平均绝对误差 (越小越好)
- **RMSE**: 均方根误差 (越小越好)
- **NSE**: Nash-Sutcliffe效率系数 (接近1最好)
- **R²**: 决定系数 (接近1最好)

## 🔧 环境要求

- Python 3.6+
- PyTorch (支持CPU/GPU)
- PyPOTS 0.0.9+ (兼容模式) 或 1.0+ (完整功能)
- NumPy, Pandas, Scikit-learn

## 📈 使用流程

1. **环境检查**: 运行 `python utils/check_gpu.py`
2. **快速验证**: 运行 `python tests/quick_test_training.py`
3. **开始训练**: 运行 `python start_training.py`
4. **查看结果**: 检查 `timemixer_evaluation_results.csv`

## 🎊 特色功能

- ✅ 自动GPU检测和优化
- ✅ 20+个可调参数
- ✅ 自动结果记录和分析
- ✅ 多种预设配置
- ✅ 兼容性强，易于使用

---

**开始您的时间序列预测之旅！运行 `python start_training.py` 🚀**
