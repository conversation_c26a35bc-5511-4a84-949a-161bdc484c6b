"""
目录整理脚本
============

将TimeMixer++项目文件按功能分类到不同文件夹中。
"""

import os
import shutil
from pathlib import Path

def create_directory_structure():
    """创建目录结构"""
    
    directories = {
        'docs': '文档和指南',
        'utils': '工具和辅助脚本', 
        'tests': '测试脚本',
        'results': '训练结果',
        'legacy': '旧版本和备份文件',
        'examples': '示例和演示脚本'
    }
    
    print("创建目录结构...")
    for dir_name, description in directories.items():
        if not os.path.exists(dir_name):
            os.makedirs(dir_name)
            print(f"✓ 创建目录: {dir_name}/ ({description})")
        else:
            print(f"✓ 目录已存在: {dir_name}/")

def get_file_categories():
    """定义文件分类规则"""
    
    # 保留在主目录的核心文件
    main_directory_files = [
        # 核心训练脚本
        'start_training.py',
        'gpu_training_runner.py', 
        'compatible_training_runner.py',
        'single_training_runner.py',
        'batch_training_runner.py',
        'advanced_batch_training.py',
        
        # 核心配置文件
        'timemixer_plus_plus_config.py',
        'advanced_parameter_configs.py',
        
        # 主要示例
        'timemixer_plus_plus_example.py',
        
        # 数据和结果
        '1964-2017dailyRunoff.csv',
        'timemixer_evaluation_results.csv',
        'requirements.txt'
    ]
    
    # 文档类文件
    docs_files = [
        'AUTOMATED_TRAINING_GUIDE.md',
        'ENHANCED_SYSTEM_GUIDE.md', 
        'FINAL_SYSTEM_SUMMARY.md',
        'GPU_SETUP_GUIDE.md',
        'SUCCESS_GUIDE.md',
        'setup_guide.md'
    ]
    
    # 工具类文件
    utils_files = [
        'check_gpu.py',
        'install_gpu_pytorch.py',
        'check_data.py',
        'find_timemixer_path.py',
        'organize_directory.py'
    ]
    
    # 测试类文件
    tests_files = [
        'test_installation.py',
        'quick_test_training.py',
        'minimal_training_test.py',
        'simple_test.py',
        'test.py'
    ]
    
    # 示例和演示文件
    examples_files = [
        'demo_multiple_trainings.py',
        'quick_training_demo.py',
        'automated_training_system.py',
        'timemixer_modify.py'
    ]
    
    # 结果目录
    results_directories = [
        'minimal_test_results',
        'real_data_test_results', 
        'timemixer_runoff_results',
        'training_1_results',
        'training_2_results',
        'training_3_results',
        'training_4_results',
        'training_5_results',
        'your_runoff_results'
    ]
    
    # 旧版本和备份
    legacy_items = [
        'univariate_timeseries_forecasting',
        '__pycache__',
        '20.0',
        'timemixer_prediction_comparison.png'
    ]
    
    return {
        'main': main_directory_files,
        'docs': docs_files,
        'utils': utils_files,
        'tests': tests_files,
        'examples': examples_files,
        'results': results_directories,
        'legacy': legacy_items
    }

def move_files_and_directories():
    """移动文件和目录到对应文件夹"""
    
    categories = get_file_categories()
    
    print("\n开始整理文件...")
    
    for category, items in categories.items():
        if category == 'main':
            continue  # 主目录文件不移动
            
        print(f"\n整理 {category} 类文件:")
        
        for item in items:
            if os.path.exists(item):
                destination = os.path.join(category, item)
                
                try:
                    if os.path.isdir(item):
                        if os.path.exists(destination):
                            shutil.rmtree(destination)
                        shutil.move(item, destination)
                        print(f"  ✓ 移动目录: {item} -> {category}/")
                    else:
                        shutil.move(item, destination)
                        print(f"  ✓ 移动文件: {item} -> {category}/")
                        
                except Exception as e:
                    print(f"  ✗ 移动失败: {item} - {e}")
            else:
                print(f"  - 文件不存在: {item}")

def create_readme_files():
    """为每个目录创建README文件"""
    
    readme_contents = {
        'docs': """# 文档目录

本目录包含TimeMixer++项目的所有文档和指南：

- `AUTOMATED_TRAINING_GUIDE.md` - 自动化训练系统使用指南
- `ENHANCED_SYSTEM_GUIDE.md` - 增强版系统使用指南  
- `FINAL_SYSTEM_SUMMARY.md` - 最终系统总结
- `GPU_SETUP_GUIDE.md` - GPU配置指南
- `SUCCESS_GUIDE.md` - 成功安装指南
- `setup_guide.md` - 安装设置指南
""",
        
        'utils': """# 工具目录

本目录包含各种工具和辅助脚本：

- `check_gpu.py` - GPU环境检测工具
- `install_gpu_pytorch.py` - GPU PyTorch安装工具
- `check_data.py` - 数据检查工具
- `find_timemixer_path.py` - TimeMixer路径查找工具
- `organize_directory.py` - 目录整理脚本
""",
        
        'tests': """# 测试目录

本目录包含各种测试脚本：

- `test_installation.py` - 安装测试脚本
- `quick_test_training.py` - 快速训练测试
- `minimal_training_test.py` - 最小化训练测试
- `simple_test.py` - 简单测试脚本
- `test.py` - 通用测试脚本
""",
        
        'examples': """# 示例目录

本目录包含示例和演示脚本：

- `demo_multiple_trainings.py` - 多配置训练演示
- `quick_training_demo.py` - 快速训练演示
- `automated_training_system.py` - 自动化训练系统
- `timemixer_modify.py` - TimeMixer修改脚本
""",
        
        'results': """# 结果目录

本目录包含所有训练结果和输出：

- `minimal_test_results/` - 最小化测试结果
- `real_data_test_results/` - 真实数据测试结果
- `timemixer_runoff_results/` - TimeMixer径流预测结果
- `training_*_results/` - 各次训练的结果
- `your_runoff_results/` - 用户径流预测结果
""",
        
        'legacy': """# 旧版本目录

本目录包含旧版本文件和备份：

- `univariate_timeseries_forecasting/` - 旧版单变量时间序列预测系统
- `__pycache__/` - Python缓存文件
- 其他旧版本文件和图片
"""
    }
    
    print("\n创建README文件...")
    
    for directory, content in readme_contents.items():
        if os.path.exists(directory):
            readme_path = os.path.join(directory, 'README.md')
            with open(readme_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✓ 创建: {readme_path}")

def create_main_readme():
    """创建主目录README文件"""
    
    main_readme = """# TimeMixer++ 时间序列预测系统

## 🚀 快速开始

```bash
# 一键启动训练系统
python start_training.py
```

## 📁 目录结构

```
timemix/
├── 🎯 核心训练脚本
│   ├── start_training.py              # 一键启动脚本 (推荐)
│   ├── gpu_training_runner.py         # GPU加速训练
│   ├── compatible_training_runner.py  # 兼容模式训练
│   ├── single_training_runner.py      # 单次训练
│   ├── batch_training_runner.py       # 批量训练
│   └── advanced_batch_training.py     # 高级批量训练
│
├── ⚙️ 配置文件
│   ├── timemixer_plus_plus_config.py  # 核心配置
│   └── advanced_parameter_configs.py  # 高级参数配置
│
├── 📊 数据和结果
│   ├── 1964-2017dailyRunoff.csv      # 训练数据
│   ├── timemixer_evaluation_results.csv # 训练结果记录
│   └── requirements.txt               # 依赖包列表
│
├── 📖 docs/                          # 文档和指南
├── 🔧 utils/                         # 工具脚本
├── 🧪 tests/                         # 测试脚本
├── 💡 examples/                      # 示例演示
├── 📈 results/                       # 训练结果
└── 📦 legacy/                        # 旧版本文件
```

## 🎯 主要功能

### 1. 一键启动 (推荐)
```bash
python start_training.py
```
- 智能检测GPU环境
- 显示历史训练结果
- 提供多种训练选项

### 2. GPU加速训练
```bash
python gpu_training_runner.py
```
- 4种GPU优化配置
- 自动内存管理
- 4-6倍速度提升

### 3. 兼容模式训练
```bash
python compatible_training_runner.py
```
- 适配各种环境
- 多种配置选择
- 稳定可靠

## 📊 评估指标

- **MAE**: 平均绝对误差 (越小越好)
- **RMSE**: 均方根误差 (越小越好)
- **NSE**: Nash-Sutcliffe效率系数 (接近1最好)
- **R²**: 决定系数 (接近1最好)

## 🔧 环境要求

- Python 3.6+
- PyTorch (支持CPU/GPU)
- PyPOTS 0.0.9+ (兼容模式) 或 1.0+ (完整功能)
- NumPy, Pandas, Scikit-learn

## 📈 使用流程

1. **环境检查**: 运行 `python utils/check_gpu.py`
2. **快速验证**: 运行 `python tests/quick_test_training.py`
3. **开始训练**: 运行 `python start_training.py`
4. **查看结果**: 检查 `timemixer_evaluation_results.csv`

## 🎊 特色功能

- ✅ 自动GPU检测和优化
- ✅ 20+个可调参数
- ✅ 自动结果记录和分析
- ✅ 多种预设配置
- ✅ 兼容性强，易于使用

---

**开始您的时间序列预测之旅！运行 `python start_training.py` 🚀**
"""
    
    with open('README.md', 'w', encoding='utf-8') as f:
        f.write(main_readme)
    
    print("✓ 创建主目录README.md")

def main():
    """主函数"""
    
    print("TimeMixer++ 目录整理工具")
    print("="*50)
    
    # 确认操作
    print("本脚本将重新组织目录结构，将文件分类到不同文件夹中。")
    print("核心训练和配置文件将保留在主目录下。")
    
    confirm = input("\n确认继续? (y/n): ").strip().lower()
    if confirm != 'y':
        print("操作已取消")
        return
    
    try:
        # 创建目录结构
        create_directory_structure()
        
        # 移动文件
        move_files_and_directories()
        
        # 创建README文件
        create_readme_files()
        create_main_readme()
        
        print("\n" + "="*50)
        print("✅ 目录整理完成!")
        print("="*50)
        
        print("\n📁 新的目录结构:")
        print("主目录: 核心训练脚本和配置文件")
        print("docs/: 文档和指南")
        print("utils/: 工具脚本")
        print("tests/: 测试脚本")
        print("examples/: 示例演示")
        print("results/: 训练结果")
        print("legacy/: 旧版本文件")
        
        print("\n🚀 现在您可以运行:")
        print("python start_training.py")
        
    except Exception as e:
        print(f"\n❌ 整理过程中出现错误: {e}")
        print("请检查文件权限和磁盘空间")

if __name__ == "__main__":
    main()
