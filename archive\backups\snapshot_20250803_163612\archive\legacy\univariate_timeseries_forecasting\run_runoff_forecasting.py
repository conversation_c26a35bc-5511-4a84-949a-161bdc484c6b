"""
Run Runoff Forecasting with 1964-2017 Daily Data
================================================

This script specifically runs the forecasting pipeline on the
1964-2017dailyRunoff.csv file.
"""

import sys
import os
from pathlib import Path
import pandas as pd
import numpy as np

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_loader import UnivariateTimeSeriesLoader
from model_config import get_univariate_configs, create_custom_univariate_config, print_config_summary
from model_trainer import UnivariateModelTrainer


def analyze_runoff_data(file_path: str):
    """
    Analyze the runoff data to understand its characteristics.
    
    Args:
        file_path: Path to the runoff data file
    """
    print("Analyzing Runoff Data")
    print("=" * 40)
    
    # Load data
    loader = UnivariateTimeSeriesLoader(
        date_column='DATA',
        target_column='runoff',
        date_format='%Y-%m-%d'  # Updated for the actual format
    )
    
    data = loader.load_csv(file_path)
    
    # Get comprehensive info
    info = loader.get_data_info()
    
    print(f"\nData Analysis Results:")
    print(f"  - Total samples: {info['total_samples']:,}")
    print(f"  - Date range: {info['date_range']['start'].date()} to {info['date_range']['end'].date()}")
    print(f"  - Duration: {info['date_range']['days']:,} days ({info['date_range']['days']/365.25:.1f} years)")
    print(f"  - Data frequency: {info['data_frequency']}")
    print(f"  - Missing values: {info['missing_values']}")
    
    print(f"\nRunoff Statistics:")
    stats = info['target_stats']
    print(f"  - Mean: {stats['mean']:.2f}")
    print(f"  - Median: {stats['median']:.2f}")
    print(f"  - Std Dev: {stats['std']:.2f}")
    print(f"  - Min: {stats['min']:.2f}")
    print(f"  - Max: {stats['max']:.2f}")
    print(f"  - Range: {stats['max'] - stats['min']:.2f}")
    
    # Check for seasonality (basic)
    data['month'] = data['DATA'].dt.month
    monthly_avg = data.groupby('month')['runoff'].mean()
    print(f"\nSeasonal Pattern (Monthly Averages):")
    months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 
              'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
    for i, month in enumerate(months, 1):
        print(f"  - {month}: {monthly_avg[i]:.2f}")
    
    return loader, data, info


def recommend_configuration(data_info: dict):
    """
    Recommend the best configuration based on data characteristics.
    
    Args:
        data_info: Data information dictionary
        
    Returns:
        Recommended configuration name and custom parameters
    """
    total_samples = data_info['total_samples']
    duration_years = data_info['date_range']['days'] / 365.25
    
    print(f"\nConfiguration Recommendation:")
    print(f"=" * 40)
    
    if total_samples > 20000:  # Large dataset
        print(f"✓ Large dataset detected ({total_samples:,} samples, {duration_years:.1f} years)")
        print(f"  Recommended: Custom long-term configuration")
        
        custom_config = {
            'n_steps': 90,      # 3 months lookback
            'n_pred_steps': 30, # 1 month forecast
            'd_model': 256,     # Large model
            'n_layers': 3,
            'top_k': 5,
            'epochs': 100,      # Reduced from 150 for practical training time
            'batch_size': 16,
            'patience': 20
        }
        return 'custom', custom_config
        
    elif total_samples > 5000:  # Medium dataset
        print(f"✓ Medium dataset detected ({total_samples:,} samples)")
        print(f"  Recommended: long_term configuration")
        return 'long_term', None
        
    else:  # Smaller dataset
        print(f"✓ Medium-small dataset detected ({total_samples:,} samples)")
        print(f"  Recommended: medium_term configuration")
        return 'medium_term', None


def run_runoff_forecasting(file_path: str, 
                          config_name: str = None,
                          custom_config: dict = None,
                          quick_test: bool = False):
    """
    Run the complete runoff forecasting pipeline.
    
    Args:
        file_path: Path to the runoff data file
        config_name: Configuration name to use
        custom_config: Custom configuration parameters
        quick_test: Whether to run a quick test
    """
    print("Runoff Forecasting Pipeline")
    print("=" * 50)
    print(f"Data file: {file_path}")
    
    # Step 1: Analyze data
    loader, data, info = analyze_runoff_data(file_path)
    
    # Step 2: Get configuration recommendation
    if not config_name and not custom_config:
        if quick_test:
            config_name = 'quick_test'
            print(f"\n✓ Using quick_test configuration for fast testing")
        else:
            rec_config, rec_custom = recommend_configuration(info)
            config_name = rec_config if rec_config != 'custom' else None
            custom_config = rec_custom
    
    # Step 3: Create configuration
    if custom_config:
        config = create_custom_univariate_config(**custom_config)
        print_config_summary(config, "Custom Recommended")
    else:
        configs = get_univariate_configs()
        config = configs[config_name]
        print_config_summary(config, config_name.replace('_', ' ').title())
    
    # Step 4: Confirm training
    if not quick_test:
        print(f"\nEstimated training time: {estimate_training_time(config, info['total_samples'])}")
        confirm = input("\nProceed with training? (y/N): ").strip().lower()
        if confirm not in ['y', 'yes']:
            print("Training cancelled.")
            return None
    
    # Step 5: Initialize trainer
    trainer = UnivariateModelTrainer(config)
    
    # Step 6: Prepare data
    print(f"\nPreparing data for training...")
    train_data, val_data, test_data = trainer.prepare_data(
        csv_file_path=file_path,
        date_column='DATA',
        target_column='runoff',
        date_format='%Y-%m-%d',  # Updated format
        normalization='minmax',
        train_ratio=0.7,
        val_ratio=0.2
    )
    
    # Step 7: Create and train model
    print(f"\nCreating TimeMixer++ model...")
    trainer.create_model()
    
    print(f"\nStarting model training...")
    training_history = trainer.train_model(
        train_data, 
        val_data,
        save_model=True,
        model_save_path="runoff_forecasting_model.pypots"
    )
    
    # Step 8: Evaluate model
    print(f"\nEvaluating model performance...")
    evaluation_results = trainer.evaluate_model(test_data)
    
    # Step 9: Visualize results
    print(f"\nGenerating prediction plots...")
    trainer.plot_predictions(
        evaluation_results, 
        num_samples=5,
        save_path="runoff_predictions.png"
    )
    
    # Step 10: Save results
    print(f"\nSaving results...")
    trainer.save_results(evaluation_results, save_dir="runoff_results")
    
    # Step 11: Print summary
    print_final_summary(evaluation_results, training_history, config)
    
    return {
        'trainer': trainer,
        'training_history': training_history,
        'evaluation_results': evaluation_results,
        'config': config,
        'data_info': info
    }


def estimate_training_time(config, num_samples):
    """
    Estimate training time based on configuration and data size.
    
    Args:
        config: Model configuration
        num_samples: Number of data samples
        
    Returns:
        Estimated training time string
    """
    # Rough estimation based on typical performance
    base_time_per_epoch = (num_samples / config.batch_size) * 0.1  # seconds
    total_time = base_time_per_epoch * config.epochs
    
    if total_time < 60:
        return f"~{total_time:.0f} seconds"
    elif total_time < 3600:
        return f"~{total_time/60:.0f} minutes"
    else:
        return f"~{total_time/3600:.1f} hours"


def print_final_summary(evaluation_results, training_history, config):
    """
    Print final summary of results.
    
    Args:
        evaluation_results: Model evaluation results
        training_history: Training history
        config: Model configuration
    """
    print(f"\n" + "=" * 60)
    print("RUNOFF FORECASTING RESULTS SUMMARY")
    print("=" * 60)
    
    # Model performance
    metrics = evaluation_results['metrics']
    print(f"\nModel Performance Metrics:")
    print(f"  - RMSE: {metrics['RMSE']:.4f}")
    print(f"  - MAE: {metrics['MAE']:.4f}")
    print(f"  - MAPE: {metrics['MAPE']:.2f}%")
    print(f"  - R²: {metrics['R2']:.4f}")
    
    # Training info
    print(f"\nTraining Information:")
    print(f"  - Training time: {training_history['training_time']:.2f} seconds")
    print(f"  - Configuration: {config.n_steps}→{config.n_pred_steps} steps")
    print(f"  - Model size: {config.d_model}D, {config.n_layers} layers")
    
    # Files generated
    print(f"\nGenerated Files:")
    print(f"  - Model: runoff_forecasting_model.pypots")
    print(f"  - Predictions plot: runoff_predictions.png")
    print(f"  - Results folder: runoff_results/")
    print(f"    - evaluation_metrics.csv")
    print(f"    - training_config.csv")
    print(f"    - sample_predictions.csv")
    
    print(f"\n✅ Runoff forecasting completed successfully!")


def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Run runoff forecasting on 1964-2017 daily data')
    parser.add_argument('--file_path', type=str, 
                       default=r'C:\Users\<USER>\Desktop\timemix\1964-2017dailyRunoff.csv',
                       help='Path to the runoff data file')
    parser.add_argument('--config', type=str, 
                       choices=['quick_test', 'short_term', 'medium_term', 'long_term'],
                       help='Configuration to use')
    parser.add_argument('--quick_test', action='store_true',
                       help='Run quick test with minimal epochs')
    parser.add_argument('--analyze_only', action='store_true',
                       help='Only analyze data without training')
    
    args = parser.parse_args()
    
    # Check if file exists
    if not Path(args.file_path).exists():
        print(f"❌ Error: File not found: {args.file_path}")
        print("Please check the file path and try again.")
        sys.exit(1)
    
    try:
        if args.analyze_only:
            # Only analyze data
            analyze_runoff_data(args.file_path)
        else:
            # Run full forecasting pipeline
            results = run_runoff_forecasting(
                file_path=args.file_path,
                config_name=args.config,
                quick_test=args.quick_test
            )
            
            if results:
                print(f"\n🎉 Forecasting pipeline completed successfully!")
            
    except KeyboardInterrupt:
        print(f"\n\n⚠ Training interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Error during forecasting: {e}")
        print(f"\nTroubleshooting:")
        print(f"1. Make sure PyPOTS is installed: pip install pypots")
        print(f"2. Check if the file path is correct")
        print(f"3. Try --quick_test for faster testing")
        print(f"4. Use --analyze_only to check data first")
        sys.exit(1)


if __name__ == "__main__":
    main()
