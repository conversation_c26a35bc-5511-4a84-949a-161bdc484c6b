"""
版本控制管理器
==============

TimeMixer项目的统一版本控制和备份管理系统。
整合备份、Git版本控制、代码追踪和快速恢复功能。
"""

import os
import sys
from pathlib import Path

# 导入各个模块
try:
    from backup_project import ProjectBackup
    from init_git_version_control import GitVersionControl
    from track_code_changes import CodeChangeTracker
    from quick_restore import QuickRestore
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    print("请确保所有版本控制脚本都在同一目录中")
    sys.exit(1)

class VersionControlManager:
    def __init__(self, project_root="."):
        self.project_root = Path(project_root)
        
        # 初始化各个组件
        self.backup_tool = ProjectBackup(project_root)
        self.git_vc = GitVersionControl(project_root)
        self.change_tracker = CodeChangeTracker(project_root)
        self.restore_tool = QuickRestore(project_root)
    
    def show_system_status(self):
        """显示系统状态"""
        print("📊 版本控制系统状态")
        print("="*50)
        
        # Git状态
        if self.git_vc.is_git_repo():
            print("✅ Git仓库: 已初始化")
            try:
                import subprocess
                result = subprocess.run(
                    ['git', 'status', '--porcelain'],
                    cwd=self.project_root,
                    capture_output=True,
                    text=True
                )
                if result.returncode == 0:
                    if result.stdout.strip():
                        print("⚠️  工作区: 有未提交的更改")
                    else:
                        print("✅ 工作区: 干净")
            except:
                pass
        else:
            print("❌ Git仓库: 未初始化")
        
        # 备份状态
        backup_count = 0
        if (self.project_root / "backups").exists():
            backup_count = len([d for d in (self.project_root / "backups").iterdir() 
                              if d.is_dir() and d.name.startswith("snapshot_")])
        print(f"📦 项目备份: {backup_count} 个")
        
        # 代码追踪状态
        tracking_dir = self.project_root / ".code_tracking"
        if tracking_dir.exists():
            changes_log = tracking_dir / "changes.json"
            if changes_log.exists():
                print("✅ 代码追踪: 已启用")
            else:
                print("⚠️  代码追踪: 已配置但无记录")
        else:
            print("❌ 代码追踪: 未启用")
        
        print()
    
    def quick_setup(self):
        """快速设置版本控制"""
        print("🚀 快速设置版本控制系统")
        print("="*50)
        
        # 1. 检查Git
        if not self.git_vc.check_git_installed():
            print("❌ Git未安装，跳过Git设置")
            git_setup = False
        else:
            git_setup = True
        
        # 2. 创建初始备份
        print("\n📦 创建初始项目备份...")
        backup_dir, backup_info = self.backup_tool.create_snapshot_backup()
        
        # 3. 设置Git (如果可用)
        if git_setup and not self.git_vc.is_git_repo():
            print("\n🌿 设置Git版本控制...")
            self.git_vc.create_gitignore()
            if self.git_vc.init_git_repo():
                print("请配置Git用户信息:")
                self.git_vc.configure_git_user()
                self.git_vc.add_and_commit_initial()
                self.git_vc.create_development_branch()
        
        # 4. 初始化代码追踪
        print("\n🔍 初始化代码追踪...")
        changes = self.change_tracker.scan_for_changes()
        
        print("\n✅ 版本控制系统设置完成!")
        print(f"📍 初始备份: {backup_dir.name}")
        print(f"📊 追踪文件: {len(self.change_tracker.tracked_files)} 个")
        
        return True
    
    def daily_maintenance(self):
        """日常维护操作"""
        print("🔧 执行日常维护")
        print("="*30)
        
        # 1. 扫描代码变更
        print("1. 扫描代码变更...")
        changes = self.change_tracker.scan_for_changes()
        
        if changes:
            print(f"   检测到 {len(changes)} 个文件变更")
            
            # 2. 如果有变更，创建备份
            create_backup = input("   是否创建备份? (y/n): ").lower() == 'y'
            if create_backup:
                print("2. 创建增量备份...")
                self.backup_tool.create_snapshot_backup()
            
            # 3. Git提交 (如果是Git仓库)
            if self.git_vc.is_git_repo():
                commit_changes = input("   是否提交到Git? (y/n): ").lower() == 'y'
                if commit_changes:
                    print("3. 提交到Git...")
                    import subprocess
                    subprocess.run(['git', 'add', '.'], cwd=self.project_root)
                    
                    commit_msg = input("   输入提交信息: ").strip()
                    if not commit_msg:
                        commit_msg = f"自动提交 - {len(changes)} 个文件变更"
                    
                    subprocess.run(['git', 'commit', '-m', commit_msg], 
                                 cwd=self.project_root)
        else:
            print("   没有检测到变更")
        
        print("✅ 日常维护完成")
    
    def emergency_backup(self):
        """紧急备份"""
        print("🚨 创建紧急备份")
        print("="*30)
        
        # 创建备份
        backup_dir, backup_info = self.backup_tool.create_snapshot_backup()
        
        # 如果是Git仓库，也创建提交
        if self.git_vc.is_git_repo():
            try:
                import subprocess
                subprocess.run(['git', 'add', '.'], cwd=self.project_root)
                subprocess.run([
                    'git', 'commit', '-m', 
                    f"紧急备份 - {backup_info['timestamp']}"
                ], cwd=self.project_root)
                print("✅ 已同时创建Git提交")
            except:
                pass
        
        print(f"🆘 紧急备份完成: {backup_dir.name}")
        return backup_dir
    
    def show_main_menu(self):
        """显示主菜单"""
        while True:
            print("\n🎯 TimeMixer版本控制管理器")
            print("="*50)
            self.show_system_status()
            
            print("选择操作:")
            print("1. 快速设置版本控制")
            print("2. 创建项目备份")
            print("3. 扫描代码变更")
            print("4. 快速恢复")
            print("5. 日常维护")
            print("6. 紧急备份")
            print("7. 查看变更历史")
            print("8. Git操作")
            print("9. 退出")
            
            choice = input("请选择 (1-9): ").strip()
            
            if choice == "1":
                self.quick_setup()
                
            elif choice == "2":
                self.backup_tool.create_snapshot_backup()
                
            elif choice == "3":
                changes = self.change_tracker.scan_for_changes()
                self.change_tracker.show_change_summary(changes)
                
            elif choice == "4":
                self.restore_tool.interactive_restore()
                
            elif choice == "5":
                self.daily_maintenance()
                
            elif choice == "6":
                self.emergency_backup()
                
            elif choice == "7":
                self.change_tracker.show_change_history()
                
            elif choice == "8":
                self.git_operations_menu()
                
            elif choice == "9":
                print("再见!")
                break
                
            else:
                print("无效选择，请重试")
    
    def git_operations_menu(self):
        """Git操作菜单"""
        if not self.git_vc.is_git_repo():
            print("❌ 当前目录不是Git仓库")
            return
        
        print("\n🌿 Git操作")
        print("="*30)
        print("1. 查看状态")
        print("2. 查看提交历史")
        print("3. 创建提交")
        print("4. 切换分支")
        print("5. 返回主菜单")
        
        choice = input("请选择 (1-5): ").strip()
        
        if choice == "1":
            self.git_vc.show_git_status()
        elif choice == "2":
            import subprocess
            result = subprocess.run(['git', 'log', '--oneline', '-10'], 
                                  cwd=self.project_root, capture_output=True, text=True)
            if result.returncode == 0:
                print("最近10次提交:")
                print(result.stdout)
        elif choice == "3":
            import subprocess
            subprocess.run(['git', 'add', '.'], cwd=self.project_root)
            msg = input("输入提交信息: ").strip()
            if msg:
                subprocess.run(['git', 'commit', '-m', msg], cwd=self.project_root)
        elif choice == "4":
            import subprocess
            result = subprocess.run(['git', 'branch'], 
                                  cwd=self.project_root, capture_output=True, text=True)
            if result.returncode == 0:
                print("可用分支:")
                print(result.stdout)
                branch = input("输入分支名: ").strip()
                if branch:
                    subprocess.run(['git', 'checkout', branch], cwd=self.project_root)

def main():
    """主函数"""
    manager = VersionControlManager()
    
    print("🎯 TimeMixer项目版本控制管理器")
    print("="*50)
    print("统一管理备份、Git版本控制、代码追踪和快速恢复")
    print()
    
    # 检查是否首次运行
    if not (Path(".") / "backups").exists() and not (Path(".") / ".git").exists():
        print("🔍 检测到这是首次运行")
        setup = input("是否立即设置版本控制? (y/n): ").lower() == 'y'
        if setup:
            manager.quick_setup()
            print("\n按回车键继续...")
            input()
    
    manager.show_main_menu()

if __name__ == "__main__":
    main()
