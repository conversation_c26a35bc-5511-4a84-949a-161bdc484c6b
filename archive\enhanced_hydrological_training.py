"""
基于水文学特征的增强训练脚本
============================

使用水文学特征工程生成的增强数据集进行TimeMixer训练，
以提高径流预测的R²值。
"""

import numpy as np
import pandas as pd
import warnings
import csv
import os
from datetime import datetime
from hydrological_feature_engineering import HydrologicalFeatureEngineer

warnings.filterwarnings('ignore')

def load_enhanced_hydrological_data():
    """加载或生成增强的水文学数据"""
    print("🌊 加载增强水文学数据")
    print("="*40)
    
    enhanced_file = 'enhanced_hydrological_features.csv'
    
    # 检查是否已存在增强数据集
    if os.path.exists(enhanced_file):
        print(f"✅ 发现已存在的增强数据集: {enhanced_file}")
        df = pd.read_csv(enhanced_file)
        df['DATE'] = pd.to_datetime(df['DATE'])
        df = df.set_index('DATE')
    else:
        print("🔧 生成新的水文学特征...")
        engineer = HydrologicalFeatureEngineer()
        results = engineer.run_complete_feature_engineering()
        df = results['enhanced_data']
    
    print(f"📊 增强数据集形状: {df.shape}")
    print(f"📅 时间范围: {df.index.min()} 到 {df.index.max()}")
    
    return df

def select_optimal_features(df, target_feature='RUNOFF', top_k=10):
    """选择最优的水文学特征组合"""
    print(f"\n🎯 选择最优特征组合 (前{top_k}个)")
    print("="*40)
    
    # 计算与目标变量的相关性
    correlations = {}
    target = df[target_feature]
    
    # 排除目标变量和日期相关列
    exclude_cols = [target_feature, 'DATE'] if 'DATE' in df.columns else [target_feature]
    feature_cols = [col for col in df.columns if col not in exclude_cols]
    
    for feature in feature_cols:
        if df[feature].dtype in ['float64', 'int64', 'float32', 'int32']:
            corr = df[feature].corr(target)
            if not np.isnan(corr):
                correlations[feature] = abs(corr)
    
    # 排序并选择前k个特征
    sorted_features = sorted(correlations.items(), key=lambda x: x[1], reverse=True)
    selected_features = [feat[0] for feat in sorted_features[:top_k]]
    
    print(f"🔝 选择的前{top_k}个特征:")
    for i, (feature, corr) in enumerate(sorted_features[:top_k], 1):
        print(f"  {i:2d}. {feature:<25} 相关性: {corr:.4f}")
    
    # 确保包含原始的重要特征，但避免重复
    essential_features = ['VISIB', 'SLP']  # RUNOFF已经在相关性分析中了
    for feat in essential_features:
        if feat in df.columns and feat not in selected_features:
            selected_features.append(feat)

    # 移除目标变量本身（如果意外包含）
    if target_feature in selected_features:
        selected_features.remove(target_feature)
    
    print(f"\n✅ 最终特征列表 ({len(selected_features)}个):")
    for i, feat in enumerate(selected_features, 1):
        corr = correlations.get(feat, 0)
        print(f"  {i:2d}. {feat:<25} 相关性: {corr:.4f}")
    
    return selected_features

def prepare_enhanced_training_data(df, selected_features, target_feature='RUNOFF', 
                                 n_steps=60, n_pred_steps=7, max_samples=None):
    """准备增强的训练数据"""
    print(f"\n🔧 准备增强训练数据")
    print("="*40)
    
    # 移除缺失值
    df_clean = df[selected_features + [target_feature]].dropna()
    print(f"清理后数据长度: {len(df_clean)}")
    
    # 获取特征和目标数据
    input_data = df_clean[selected_features].values.astype(np.float32)
    target_data = df_clean[target_feature].values.astype(np.float32)

    print(f"选择的特征数量: {len(selected_features)}")
    print(f"输入特征维度: {input_data.shape}")
    print(f"目标变量维度: {target_data.shape}")

    # 确保特征数量一致
    n_features = input_data.shape[1]
    print(f"实际特征数量: {n_features}")
    
    # 数据标准化
    print("\n📊 数据标准化...")
    
    # 输入特征标准化
    input_means = np.mean(input_data, axis=0)
    input_stds = np.std(input_data, axis=0)
    input_data_normalized = (input_data - input_means) / (input_stds + 1e-8)
    
    # 目标变量标准化
    target_mean = np.mean(target_data)
    target_std = np.std(target_data)
    target_data_normalized = (target_data - target_mean) / target_std
    
    print(f"输入特征标准化统计:")
    for i, feature in enumerate(selected_features):
        print(f"  {feature}: 均值={input_means[i]:.2f}, 标准差={input_stds[i]:.2f}")
    
    print(f"目标变量标准化统计:")
    print(f"  {target_feature}: 均值={target_mean:.2f}, 标准差={target_std:.2f}")
    
    # 创建时间序列样本
    total_sample_len = n_steps + n_pred_steps
    n_samples = len(target_data_normalized) - total_sample_len + 1
    
    if n_samples <= 0:
        raise ValueError("数据太短，无法创建样本")
    
    print(f"\n📦 创建时间序列样本...")
    print(f"可创建的最大样本数: {n_samples}")
    
    # 限制样本数量
    if max_samples is not None:
        n_samples = min(n_samples, max_samples)
        print(f"限制样本数为: {n_samples}")
    
    # 创建数据集 - 使用实际的特征数量
    dataset_X = np.zeros((n_samples, n_steps, n_features), dtype=np.float32)
    dataset_y = np.zeros((n_samples, n_pred_steps, 1), dtype=np.float32)
    
    for i in range(n_samples):
        # 输入特征 (多变量)
        dataset_X[i, :, :] = input_data_normalized[i:i + n_steps, :]
        # 目标变量 (单变量)
        dataset_y[i, :, 0] = target_data_normalized[i + n_steps:i + total_sample_len]
    
    print(f"输入数据集形状: {dataset_X.shape} (样本数, 时间步, 特征数)")
    print(f"目标数据集形状: {dataset_y.shape} (样本数, 预测步, 1)")
    
    # 数据集划分
    train_end = int(n_samples * 0.7)
    val_end = int(n_samples * 0.85)
    
    train_data = {'X': dataset_X[:train_end], 'X_pred': dataset_y[:train_end]}
    val_data = {'X': dataset_X[train_end:val_end], 'X_pred': dataset_y[train_end:val_end]}
    test_data = {'X': dataset_X[val_end:], 'X_pred': dataset_y[val_end:]}
    
    print(f"训练集: {train_data['X'].shape}")
    print(f"验证集: {val_data['X'].shape}")
    print(f"测试集: {test_data['X'].shape}")
    
    # 保存标准化参数
    normalization_params = {
        'input_means': input_means,
        'input_stds': input_stds,
        'target_mean': target_mean,
        'target_std': target_std,
        'selected_features': selected_features,
        'target_feature': target_feature
    }
    
    return train_data, val_data, test_data, normalization_params

def run_enhanced_hydrological_training(config_name='PSO优化_高精度'):
    """运行基于水文学特征的增强训练"""
    print("🎯 基于水文学特征的增强TimeMixer训练")
    print("="*50)
    
    # 1. 加载增强数据
    df = load_enhanced_hydrological_data()
    
    # 2. 选择最优特征
    selected_features = select_optimal_features(df, top_k=15)  # 增加特征数量
    
    # 3. 从参数配置中获取最优配置
    from my_parameters import get_my_parameters
    configs = get_my_parameters()
    
    # 找到指定配置
    config = None
    for cfg in configs:
        if cfg['name'] == config_name:
            config = cfg
            break
    
    if config is None:
        print(f"❌ 未找到配置: {config_name}")
        print("可用配置:")
        for cfg in configs:
            print(f"  - {cfg['name']}")
        return None
    
    print(f"\n🔧 使用配置: {config['name']}")
    
    # 4. 准备训练数据
    train_data, val_data, test_data, norm_params = prepare_enhanced_training_data(
        df, selected_features,
        n_steps=config['n_steps'],
        n_pred_steps=config['n_pred_steps'],
        max_samples=config.get('max_samples', None)
    )
    
    # 5. 更新配置以适应新的特征数量
    enhanced_config = config.copy()
    enhanced_config['n_features'] = len(selected_features)
    enhanced_config['name'] = f"{config['name']}_水文学增强"
    
    print(f"\n🚀 开始增强训练...")
    print(f"特征数量: {len(selected_features)}")
    print(f"配置: {enhanced_config['name']}")
    
    # 6. 导入并运行训练
    try:
        from compatible_training_runner import run_timemixer_training_with_data
        
        # 使用增强数据运行训练
        results = run_timemixer_training_with_data(
            train_data, val_data, test_data, 
            norm_params, enhanced_config
        )
        
        if results:
            print(f"\n✅ 水文学增强训练完成!")
            print(f"📊 结果已保存到训练结果文件")
            
            # 保存特征信息
            feature_info = {
                'selected_features': selected_features,
                'feature_count': len(selected_features),
                'config_used': enhanced_config,
                'training_timestamp': datetime.now().isoformat()
            }
            
            import json
            with open('enhanced_training_features.json', 'w', encoding='utf-8') as f:
                json.dump(feature_info, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"📋 特征信息已保存到: enhanced_training_features.json")
            
        return results
        
    except ImportError as e:
        print(f"❌ 导入训练模块失败: {e}")
        print("请确保compatible_training_runner.py可用")
        return None

def main():
    """主函数"""
    print("🌊 水文学特征增强训练主程序")
    print("="*50)
    
    # 显示可用的配置
    from my_parameters import get_my_parameters
    configs = get_my_parameters()
    
    print("📋 可用的训练配置:")
    for i, config in enumerate(configs, 1):
        print(f"  {i:2d}. {config['name']}")
    
    # 推荐使用的配置
    recommended_configs = [
        'PSO优化_高精度',
        'PSO优化_平衡型', 
        '稳定防过拟合_TOP3_全数据'
    ]
    
    print(f"\n💡 推荐配置 (适合水文学特征):")
    for config_name in recommended_configs:
        print(f"  - {config_name}")
    
    # 运行训练
    print(f"\n🚀 开始运行增强训练...")
    
    # 使用最佳配置
    best_config = 'PSO优化_高精度'  # 可以根据需要修改
    results = run_enhanced_hydrological_training(best_config)
    
    if results:
        print(f"\n🎉 训练完成!")
        print(f"💡 建议:")
        print(f"1. 检查训练结果中的R²值是否有提升")
        print(f"2. 分析哪些水文学特征贡献最大")
        print(f"3. 考虑进一步调整特征选择策略")
        print(f"4. 尝试不同的配置组合")
    else:
        print(f"\n❌ 训练失败，请检查错误信息")

if __name__ == "__main__":
    main()
