"""
高级参数配置生成器
==================

提供更多可控参数的配置生成工具。
"""

import torch
import itertools
from typing import Dict, List, Any

class ParameterConfigGenerator:
    """参数配置生成器"""
    
    def __init__(self):
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        print(f"检测到设备: {self.device}")
        
    def get_all_parameters(self) -> Dict[str, Any]:
        """获取所有可配置参数及其说明"""
        return {
            # 数据相关参数
            'n_steps': {
                'description': '输入序列长度',
                'type': int,
                'default': 96,
                'range': [24, 48, 72, 96, 120, 144],
                'impact': '更长的序列可以捕获更多历史信息，但增加计算成本'
            },
            'n_pred_steps': {
                'description': '预测序列长度',
                'type': int,
                'default': 24,
                'range': [6, 12, 18, 24, 30, 36],
                'impact': '预测步长，通常为输入长度的1/4到1/2'
            },
            
            # 模型架构参数
            'n_layers': {
                'description': '模型层数',
                'type': int,
                'default': 2,
                'range': [1, 2, 3, 4],
                'impact': '更多层可以学习更复杂的模式，但可能过拟合'
            },
            'd_model': {
                'description': '模型维度',
                'type': int,
                'default': 256,
                'range': [32, 64, 96, 128, 192, 256, 384, 512],
                'impact': '更大的维度增加模型容量，但需要更多内存'
            },
            'd_ffn': {
                'description': '前馈网络维度',
                'type': int,
                'default': 512,
                'range': [64, 128, 192, 256, 384, 512, 768, 1024],
                'impact': '通常设为d_model的2-4倍'
            },
            'top_k': {
                'description': 'TimeMixer的top-k参数',
                'type': int,
                'default': 5,
                'range': [3, 5, 7, 10, 15],
                'impact': '控制注意力机制的稀疏性'
            },
            'moving_avg': {
                'description': '移动平均窗口大小',
                'type': int,
                'default': 25,
                'range': [15, 25, 35, 50, 75],
                'impact': '用于趋势分解的窗口大小'
            },
            'downsampling_window': {
                'description': '下采样窗口大小',
                'type': int,
                'default': 2,
                'range': [2, 3, 4, 5],
                'impact': '控制下采样的程度'
            },
            'downsampling_layers': {
                'description': '下采样层数',
                'type': int,
                'default': 1,
                'range': [1, 2, 3],
                'impact': '更多下采样层可以捕获多尺度特征'
            },
            
            # 正则化参数
            'dropout': {
                'description': 'Dropout率',
                'type': float,
                'default': 0.1,
                'range': [0.0, 0.1, 0.15, 0.2, 0.25, 0.3],
                'impact': '防止过拟合，但过高会影响学习能力'
            },
            'use_norm': {
                'description': '是否使用层归一化',
                'type': bool,
                'default': True,
                'range': [True, False],
                'impact': '通常建议开启以稳定训练'
            },
            
            # 训练参数
            'epochs': {
                'description': '训练轮次',
                'type': int,
                'default': 100,
                'range': [5, 10, 15, 20, 30, 50, 100],
                'impact': '更多轮次可能获得更好结果，但有过拟合风险'
            },
            'batch_size': {
                'description': '批次大小',
                'type': int,
                'default': 32,
                'range': [8, 16, 32, 64, 128],
                'impact': 'GPU可用时可以使用更大批次'
            },
            'learning_rate': {
                'description': '学习率',
                'type': float,
                'default': 1e-3,
                'range': [1e-4, 5e-4, 8e-4, 1e-3, 2e-3, 5e-3],
                'impact': '控制参数更新步长，需要仔细调节'
            },
            'patience': {
                'description': '早停耐心值',
                'type': int,
                'default': 10,
                'range': [3, 5, 8, 10, 15],
                'impact': '验证集性能不提升时等待的轮次'
            },
            
            # 系统参数
            'device': {
                'description': '计算设备',
                'type': str,
                'default': self.device,
                'range': ['cpu', 'cuda'],
                'impact': 'GPU可以显著加速训练'
            },
            'num_workers': {
                'description': '数据加载进程数',
                'type': int,
                'default': 0,
                'range': [0, 2, 4, 8],
                'impact': '多进程可以加速数据加载，但可能有兼容性问题'
            }
        }
    
    def create_small_model_configs(self) -> List[Dict[str, Any]]:
        """创建小模型配置（快速实验）"""
        base_config = {
            'device': self.device,
            'use_norm': True,
            'term': 'short',
            'num_workers': 0
        }
        
        configs = [
            {**base_config, 'name': '超小模型_快速', 'n_steps': 24, 'n_pred_steps': 6, 'n_layers': 1, 'd_model': 32, 'd_ffn': 64, 'top_k': 3, 'moving_avg': 15, 'dropout': 0.1, 'epochs': 5, 'learning_rate': 1e-3, 'batch_size': 64},
            {**base_config, 'name': '小模型_标准', 'n_steps': 48, 'n_pred_steps': 12, 'n_layers': 1, 'd_model': 64, 'd_ffn': 128, 'top_k': 5, 'moving_avg': 25, 'dropout': 0.1, 'epochs': 10, 'learning_rate': 1e-3, 'batch_size': 32},
            {**base_config, 'name': '小模型_深层', 'n_steps': 48, 'n_pred_steps': 12, 'n_layers': 2, 'd_model': 64, 'd_ffn': 128, 'top_k': 5, 'moving_avg': 25, 'dropout': 0.15, 'epochs': 10, 'learning_rate': 8e-4, 'batch_size': 32},
        ]
        return configs
    
    def create_medium_model_configs(self) -> List[Dict[str, Any]]:
        """创建中等模型配置（平衡性能）"""
        base_config = {
            'device': self.device,
            'use_norm': True,
            'term': 'short',
            'num_workers': 0
        }
        
        configs = [
            {**base_config, 'name': '中模型_标准', 'n_steps': 72, 'n_pred_steps': 18, 'n_layers': 2, 'd_model': 128, 'd_ffn': 256, 'top_k': 5, 'moving_avg': 25, 'dropout': 0.15, 'epochs': 15, 'learning_rate': 8e-4, 'batch_size': 32},
            {**base_config, 'name': '中模型_宽网络', 'n_steps': 72, 'n_pred_steps': 18, 'n_layers': 2, 'd_model': 192, 'd_ffn': 384, 'top_k': 7, 'moving_avg': 35, 'dropout': 0.15, 'epochs': 15, 'learning_rate': 5e-4, 'batch_size': 24},
            {**base_config, 'name': '中模型_深网络', 'n_steps': 72, 'n_pred_steps': 18, 'n_layers': 3, 'd_model': 128, 'd_ffn': 256, 'top_k': 5, 'moving_avg': 25, 'dropout': 0.2, 'epochs': 15, 'learning_rate': 5e-4, 'batch_size': 24},
        ]
        return configs
    
    def create_large_model_configs(self) -> List[Dict[str, Any]]:
        """创建大模型配置（最佳性能）"""
        base_config = {
            'device': self.device,
            'use_norm': True,
            'term': 'short',
            'num_workers': 0
        }
        
        batch_size = 64 if self.device == 'cuda' else 16
        
        configs = [
            {**base_config, 'name': '大模型_标准', 'n_steps': 96, 'n_pred_steps': 24, 'n_layers': 3, 'd_model': 256, 'd_ffn': 512, 'top_k': 7, 'moving_avg': 35, 'dropout': 0.2, 'epochs': 20, 'learning_rate': 5e-4, 'batch_size': batch_size//2},
            {**base_config, 'name': '大模型_超宽', 'n_steps': 96, 'n_pred_steps': 24, 'n_layers': 2, 'd_model': 384, 'd_ffn': 768, 'top_k': 10, 'moving_avg': 50, 'dropout': 0.2, 'epochs': 20, 'learning_rate': 3e-4, 'batch_size': batch_size//4},
            {**base_config, 'name': '大模型_超深', 'n_steps': 96, 'n_pred_steps': 24, 'n_layers': 4, 'd_model': 256, 'd_ffn': 512, 'top_k': 7, 'moving_avg': 35, 'dropout': 0.25, 'epochs': 20, 'learning_rate': 3e-4, 'batch_size': batch_size//4},
        ]
        return configs
    
    def create_learning_rate_experiments(self) -> List[Dict[str, Any]]:
        """创建学习率实验配置"""
        base_config = {
            'device': self.device,
            'n_steps': 48, 'n_pred_steps': 12, 'n_layers': 2, 'd_model': 64, 'd_ffn': 128,
            'top_k': 5, 'moving_avg': 25, 'dropout': 0.15, 'epochs': 10, 'batch_size': 32,
            'use_norm': True, 'term': 'short', 'num_workers': 0
        }
        
        learning_rates = [1e-4, 5e-4, 8e-4, 1e-3, 2e-3, 5e-3]
        configs = []
        
        for lr in learning_rates:
            config = base_config.copy()
            config['learning_rate'] = lr
            config['name'] = f'学习率实验_lr{lr:.0e}'
            configs.append(config)
        
        return configs
    
    def create_architecture_experiments(self) -> List[Dict[str, Any]]:
        """创建架构实验配置"""
        base_config = {
            'device': self.device,
            'n_steps': 48, 'n_pred_steps': 12, 'learning_rate': 1e-3,
            'dropout': 0.15, 'epochs': 10, 'batch_size': 32,
            'use_norm': True, 'term': 'short', 'num_workers': 0
        }
        
        configs = [
            # 层数实验
            {**base_config, 'name': '架构_1层', 'n_layers': 1, 'd_model': 64, 'd_ffn': 128, 'top_k': 5, 'moving_avg': 25},
            {**base_config, 'name': '架构_2层', 'n_layers': 2, 'd_model': 64, 'd_ffn': 128, 'top_k': 5, 'moving_avg': 25},
            {**base_config, 'name': '架构_3层', 'n_layers': 3, 'd_model': 64, 'd_ffn': 128, 'top_k': 5, 'moving_avg': 25},
            
            # 维度实验
            {**base_config, 'name': '架构_小维度', 'n_layers': 2, 'd_model': 32, 'd_ffn': 64, 'top_k': 3, 'moving_avg': 15},
            {**base_config, 'name': '架构_中维度', 'n_layers': 2, 'd_model': 96, 'd_ffn': 192, 'top_k': 7, 'moving_avg': 35},
            {**base_config, 'name': '架构_大维度', 'n_layers': 2, 'd_model': 128, 'd_ffn': 256, 'top_k': 10, 'moving_avg': 50},
            
            # top_k实验
            {**base_config, 'name': '架构_topk3', 'n_layers': 2, 'd_model': 64, 'd_ffn': 128, 'top_k': 3, 'moving_avg': 25},
            {**base_config, 'name': '架构_topk7', 'n_layers': 2, 'd_model': 64, 'd_ffn': 128, 'top_k': 7, 'moving_avg': 25},
            {**base_config, 'name': '架构_topk15', 'n_layers': 2, 'd_model': 64, 'd_ffn': 128, 'top_k': 15, 'moving_avg': 25},
        ]
        
        return configs
    
    def create_sequence_length_experiments(self) -> List[Dict[str, Any]]:
        """创建序列长度实验配置"""
        base_config = {
            'device': self.device,
            'n_layers': 2, 'd_model': 64, 'd_ffn': 128, 'top_k': 5,
            'dropout': 0.15, 'epochs': 10, 'learning_rate': 1e-3, 'batch_size': 32,
            'use_norm': True, 'term': 'short', 'num_workers': 0
        }
        
        sequence_configs = [
            (24, 6, 15),   # 短序列
            (48, 12, 25),  # 中序列
            (72, 18, 35),  # 长序列
            (96, 24, 50),  # 超长序列
            (120, 30, 60), # 极长序列
        ]
        
        configs = []
        for n_steps, n_pred_steps, moving_avg in sequence_configs:
            config = base_config.copy()
            config.update({
                'name': f'序列长度_{n_steps}to{n_pred_steps}',
                'n_steps': n_steps,
                'n_pred_steps': n_pred_steps,
                'moving_avg': moving_avg
            })
            configs.append(config)
        
        return configs
    
    def get_all_experiment_configs(self) -> Dict[str, List[Dict[str, Any]]]:
        """获取所有实验配置"""
        return {
            'small_models': self.create_small_model_configs(),
            'medium_models': self.create_medium_model_configs(),
            'large_models': self.create_large_model_configs(),
            'learning_rate_experiments': self.create_learning_rate_experiments(),
            'architecture_experiments': self.create_architecture_experiments(),
            'sequence_length_experiments': self.create_sequence_length_experiments(),
        }
    
    def print_parameter_guide(self):
        """打印参数调优指南"""
        params = self.get_all_parameters()
        
        print("TimeMixer++ 参数调优指南")
        print("="*60)
        
        categories = {
            '数据参数': ['n_steps', 'n_pred_steps'],
            '模型架构': ['n_layers', 'd_model', 'd_ffn', 'top_k', 'moving_avg', 'downsampling_window', 'downsampling_layers'],
            '正则化': ['dropout', 'use_norm'],
            '训练参数': ['epochs', 'batch_size', 'learning_rate', 'patience'],
            '系统参数': ['device', 'num_workers']
        }
        
        for category, param_names in categories.items():
            print(f"\n{category}:")
            print("-" * 30)
            for param_name in param_names:
                if param_name in params:
                    param = params[param_name]
                    print(f"{param_name}:")
                    print(f"  描述: {param['description']}")
                    print(f"  默认值: {param['default']}")
                    print(f"  建议范围: {param['range']}")
                    print(f"  影响: {param['impact']}")
                    print()

def main():
    """演示参数配置生成器"""
    generator = ParameterConfigGenerator()
    
    print("参数配置生成器演示")
    print("="*50)
    
    # 显示参数指南
    generator.print_parameter_guide()
    
    # 获取所有配置
    all_configs = generator.get_all_experiment_configs()
    
    print(f"\n可用的实验配置:")
    for category, configs in all_configs.items():
        print(f"{category}: {len(configs)} 个配置")
        for config in configs[:2]:  # 显示前2个配置
            print(f"  - {config['name']}")
        if len(configs) > 2:
            print(f"  ... 还有 {len(configs) - 2} 个配置")
        print()

if __name__ == "__main__":
    main()
