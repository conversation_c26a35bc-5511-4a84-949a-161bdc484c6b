"""
运行基于水文学特征的增强训练
============================

使用增强的水文学特征数据集，通过修改现有训练脚本来提高R²值。
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime

def create_enhanced_parameter_config():
    """创建基于水文学特征的增强参数配置"""
    print("🔧 创建水文学特征增强配置")
    print("="*40)
    
    # 基于最佳PSO配置，针对水文学特征优化
    enhanced_config = {
        'name': '水文学特征增强_最优配置',
        
        # 数据相关参数 - 针对水文学特征优化
        'n_steps': 90,                    # 增加输入序列长度以捕捉更多水文学模式
        'n_pred_steps': 5,                # 预测5天，平衡精度和实用性
        
        # 模型架构参数 - 适配更多特征
        'n_layers': 3,                    # 适中的层数
        'd_model': 256,                   # 增大模型维度以处理更多特征
        'd_ffn': 512,                     # 相应增大前馈网络
        'top_k': 10,                      # 适中的top-k
        'moving_avg': 7,                  # 7天移动平均，符合水文学周期
        'downsampling_window': 3,         # 适中的下采样
        'downsampling_layers': 2,         # 适中的下采样层数
        
        # 正则化参数 - 防止过拟合
        'dropout': 0.2,                   # 适中的dropout
        'use_norm': True,                 # 使用层归一化
        
        # 训练参数 - 充分训练
        'epochs': 200,                    # 充分的训练轮次
        'batch_size': 32,                 # 适中的批次大小
        'learning_rate': 3e-4,            # 较低的学习率，稳定训练
        'patience': 30,                   # 大耐心值
        
        # 系统参数
        'device': 'cuda',                 # GPU加速
        'num_workers': 0                  # 单进程
    }
    
    print(f"✅ 增强配置创建完成:")
    print(f"  序列长度: {enhanced_config['n_steps']} → {enhanced_config['n_pred_steps']}")
    print(f"  模型架构: {enhanced_config['n_layers']}层, d_model={enhanced_config['d_model']}")
    print(f"  训练参数: lr={enhanced_config['learning_rate']:.2e}, epochs={enhanced_config['epochs']}")
    
    return enhanced_config

def modify_training_runner_for_enhanced_features():
    """修改训练运行器以使用增强特征"""
    print("\n🔧 修改训练运行器以支持水文学特征")
    print("="*50)
    
    # 检查增强数据集是否存在
    enhanced_file = 'enhanced_hydrological_features.csv'
    if not os.path.exists(enhanced_file):
        print(f"❌ 增强数据集不存在: {enhanced_file}")
        print("请先运行: python simplified_hydrological_features.py")
        return False
    
    # 读取增强数据集
    df = pd.read_csv(enhanced_file)
    print(f"✅ 加载增强数据集: {df.shape}")
    
    # 分析特征
    numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
    if 'DATE' in numeric_cols:
        numeric_cols.remove('DATE')
    
    print(f"📊 数值特征数量: {len(numeric_cols)}")
    
    # 选择最重要的特征（基于相关性）
    target_col = 'RUNOFF'
    if target_col not in numeric_cols:
        print(f"❌ 目标变量 {target_col} 不存在")
        return False
    
    # 计算相关性并选择前15个特征
    correlations = {}
    for col in numeric_cols:
        if col != target_col:
            corr = df[col].corr(df[target_col])
            if not pd.isna(corr):
                correlations[col] = abs(corr)
    
    # 选择相关性最高的特征
    sorted_features = sorted(correlations.items(), key=lambda x: x[1], reverse=True)
    selected_features = [feat[0] for feat in sorted_features[:15]]
    
    # 确保包含原始重要特征
    essential_features = ['VISIB', 'SLP']
    for feat in essential_features:
        if feat in df.columns and feat not in selected_features:
            selected_features.append(feat)
    
    print(f"\n🎯 选择的特征 ({len(selected_features)}个):")
    for i, feat in enumerate(selected_features, 1):
        corr = correlations.get(feat, 0)
        print(f"  {i:2d}. {feat:<25} 相关性: {corr:.4f}")
    
    # 创建修改后的数据文件
    enhanced_data = df[['DATE'] + selected_features + [target_col]].copy()
    enhanced_data.to_csv('enhanced_training_data.csv', index=False)
    
    print(f"\n✅ 创建训练数据文件: enhanced_training_data.csv")
    print(f"📊 数据形状: {enhanced_data.shape}")
    
    return True, selected_features

def create_modified_training_runner():
    """创建修改版的训练运行器"""
    print("\n📝 创建修改版训练运行器")
    print("="*40)
    
    # 读取原始训练运行器
    with open('compatible_training_runner.py', 'r', encoding='utf-8') as f:
        original_code = f.read()
    
    # 修改数据加载部分
    modified_code = original_code.replace(
        "df = pd.read_csv('1971-2017all_cleaned_CHANGBEI_daily_weather_data.csv')",
        "df = pd.read_csv('enhanced_training_data.csv')"
    )

    # 修改日期解析格式
    modified_code = modified_code.replace(
        "df['DATE'] = pd.to_datetime(df['DATE'], format='%Y/%m/%d')",
        "df['DATE'] = pd.to_datetime(df['DATE'], format='%Y-%m-%d')"
    )
    
    # 修改特征选择部分
    modified_code = modified_code.replace(
        "input_features = ['RUNOFF', 'VISIB', 'SLP']",
        "# 动态获取特征列表\n        feature_cols = [col for col in df.columns if col not in ['DATE', 'RUNOFF']]\n        input_features = feature_cols"
    )
    
    # 修改注释
    modified_code = modified_code.replace(
        "# 基于LightGBM特征重要性分析选择的3个最重要特征",
        "# 基于水文学特征工程选择的最重要特征"
    )
    
    modified_code = modified_code.replace(
        "print(f\"特征选择依据: LightGBM分析，RUNOFF占98.6%重要性\")",
        "print(f\"特征选择依据: 水文学特征工程，包含径流记忆效应等关键特征\")"
    )
    
    # 保存修改后的文件
    with open('enhanced_compatible_training_runner.py', 'w', encoding='utf-8') as f:
        f.write(modified_code)
    
    print("✅ 修改版训练运行器已创建: enhanced_compatible_training_runner.py")
    
    return True

def run_enhanced_training():
    """运行增强训练"""
    print("\n🚀 运行水文学特征增强训练")
    print("="*50)
    
    # 1. 检查并生成增强特征
    if not os.path.exists('enhanced_hydrological_features.csv'):
        print("🔧 生成水文学特征...")
        os.system('python simplified_hydrological_features.py')
    
    # 2. 修改训练运行器
    success, selected_features = modify_training_runner_for_enhanced_features()
    if not success:
        return False
    
    # 3. 创建修改版训练运行器
    create_modified_training_runner()
    
    # 4. 创建增强配置
    enhanced_config = create_enhanced_parameter_config()
    
    # 5. 将增强配置添加到参数文件
    print("\n📝 添加增强配置到参数文件...")
    
    # 读取当前参数文件
    with open('my_parameters.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 创建新配置字符串
    config_str = f"""
        # 🌊 配置17: 水文学特征增强配置 (基于水文学原理)
        {{
            'name': '{enhanced_config['name']}',
            # 数据相关参数 (水文学特征优化)
            'n_steps': {enhanced_config['n_steps']},                    # 输入序列长度
            'n_pred_steps': {enhanced_config['n_pred_steps']},               # 预测序列长度

            # 模型架构参数 (适配水文学特征)
            'n_layers': {enhanced_config['n_layers']},                    # 模型层数
            'd_model': {enhanced_config['d_model']},                   # 模型维度
            'd_ffn': {enhanced_config['d_ffn']},                     # 前馈网络维度
            'top_k': {enhanced_config['top_k']},                       # TimeMixer的top-k参数
            'moving_avg': {enhanced_config['moving_avg']},                  # 移动平均窗口大小
            'downsampling_window': {enhanced_config['downsampling_window']},         # 下采样窗口大小
            'downsampling_layers': {enhanced_config['downsampling_layers']},         # 下采样层数

            # 正则化参数 (水文学特征优化)
            'dropout': {enhanced_config['dropout']:.3f},                   # Dropout率
            'use_norm': {enhanced_config['use_norm']},                 # 是否使用层归一化

            # 训练参数 (水文学特征优化)
            'epochs': {enhanced_config['epochs']},                     # 训练轮次
            'batch_size': {enhanced_config['batch_size']},                 # 批次大小
            'learning_rate': {enhanced_config['learning_rate']:.2e},            # 学习率
            'patience': {enhanced_config['patience']},                   # 早停耐心值

            # 系统参数
            'device': '{enhanced_config['device']}',                 # 计算设备
            'num_workers': {enhanced_config['num_workers']}                  # 数据加载进程数
        }}"""
    
    # 在最后一个配置后插入新配置
    last_brace_pos = content.rfind('}')
    bracket_pos = content.find(']', last_brace_pos)
    
    if last_brace_pos != -1 and bracket_pos != -1:
        new_content = (content[:last_brace_pos + 1] + 
                      "," + config_str + 
                      content[bracket_pos:])
        
        with open('my_parameters.py', 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print("✅ 增强配置已添加到参数文件")
    
    # 6. 运行训练
    print(f"\n🔥 开始运行增强训练...")
    print(f"使用特征数量: {len(selected_features)}")
    print(f"配置名称: {enhanced_config['name']}")
    
    # 导入并运行修改版训练器
    try:
        import enhanced_compatible_training_runner
        result = enhanced_compatible_training_runner.run_compatible_training(enhanced_config)
        
        if result:
            print(f"\n🎉 水文学特征增强训练完成!")
            print(f"📊 请查看训练结果文件了解R²改善情况")
            
            # 保存特征信息
            feature_info = {
                'enhanced_features': selected_features,
                'feature_count': len(selected_features),
                'config_used': enhanced_config,
                'training_timestamp': datetime.now().isoformat(),
                'data_file': 'enhanced_training_data.csv'
            }
            
            import json
            with open('enhanced_training_info.json', 'w', encoding='utf-8') as f:
                json.dump(feature_info, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"📋 训练信息已保存到: enhanced_training_info.json")
            
        return result
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        return False

def main():
    """主函数"""
    print("🌊 水文学特征增强训练主程序")
    print("="*50)
    
    print("💡 本程序将:")
    print("1. 🔧 生成基于水文学原理的特征")
    print("2. 📊 选择最相关的特征组合")
    print("3. 🚀 使用增强特征进行TimeMixer训练")
    print("4. 📈 期望显著提高R²值")
    
    # 运行增强训练
    success = run_enhanced_training()
    
    if success:
        print(f"\n🎉 水文学特征增强训练完成!")
        print(f"\n💡 建议:")
        print(f"1. 检查 timemixer_evaluation_results.csv 中的R²值")
        print(f"2. 对比增强前后的性能差异")
        print(f"3. 分析哪些水文学特征贡献最大")
        print(f"4. 考虑进一步优化特征选择")
    else:
        print(f"\n❌ 训练失败")
        print(f"💡 故障排除:")
        print(f"1. 确保数据文件存在")
        print(f"2. 检查Python环境和依赖")
        print(f"3. 查看错误信息进行调试")

if __name__ == "__main__":
    main()
