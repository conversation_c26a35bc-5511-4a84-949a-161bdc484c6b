"""
测试API修复
===========

验证TimeMixer API调用是否修复了参数问题
"""

from my_parameters import get_my_parameters
from compatible_training_runner import run_compatible_training

def test_api_fix():
    """测试API修复"""
    
    configs = get_my_parameters()
    
    # 使用全数据训练配置进行测试
    test_config = configs[-1].copy()  # 全数据训练配置
    
    # 修改为快速测试
    test_config.update({
        'name': 'API修复测试',
        'max_samples': 100,  # 限制样本数量
        'epochs': 2,         # 只训练2轮
        'batch_size': 16
    })
    
    print("🧪 测试TimeMixer API修复")
    print("="*50)
    print(f"配置名称: {test_config['name']}")
    print(f"预测步长: {test_config['n_pred_steps']}")
    print(f"训练轮次: {test_config['epochs']}")
    print(f"样本数量: {test_config['max_samples']}")
    print()
    
    print("开始测试...")
    result = run_compatible_training(test_config)
    
    if result:
        print(f"\n✅ API修复测试成功!")
        print(f"训练ID: {result['training_id']}")
        print(f"MAE: {result['mae']:.6f}")
        print(f"R²: {result['r2']:.6f}")
        print("\n🎉 现在可以正常使用PyPOTS TimeMixer了!")
    else:
        print(f"\n❌ API修复测试失败!")

if __name__ == "__main__":
    test_api_fix()
