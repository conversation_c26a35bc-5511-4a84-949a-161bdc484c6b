"""
基于水文学原理的径流特征工程
============================

根据水文学原理，从现有的RUNOFF数据构造新的特征，
以提高径流预测的准确性和R²值。
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.signal import find_peaks
import warnings
warnings.filterwarnings('ignore')

class HydrologicalFeatureEngineer:
    """基于水文学原理的特征工程器"""
    
    def __init__(self, data_file='1971-2017all_cleaned_CHANGBEI_daily_weather_data.csv'):
        self.data_file = data_file
        self.df = None
        self.enhanced_df = None
        
    def load_data(self):
        """加载数据"""
        print("🌊 加载径流数据进行水文学特征工程")
        print("="*50)
        
        try:
            self.df = pd.read_csv(self.data_file)
            self.df['DATE'] = pd.to_datetime(self.df['DATE'], format='%Y/%m/%d')
            self.df = self.df.set_index('DATE').sort_index()
            
            print(f"✅ 数据加载成功: {self.df.shape}")
            print(f"📅 时间范围: {self.df.index.min()} 到 {self.df.index.max()}")
            print(f"💧 径流范围: {self.df['RUNOFF'].min():.1f} - {self.df['RUNOFF'].max():.1f} m³/s")
            
            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def create_hydrological_features(self):
        """基于水文学原理创建新特征"""
        print("\n🔧 基于水文学原理构造新特征")
        print("="*40)
        
        # 复制原始数据
        self.enhanced_df = self.df.copy()
        runoff = self.df['RUNOFF'].values
        
        # 1. 基流分离特征 (Baseflow Separation)
        print("1️⃣ 基流分离特征...")
        self.enhanced_df['baseflow'] = self._calculate_baseflow(runoff)
        self.enhanced_df['quickflow'] = self.enhanced_df['RUNOFF'] - self.enhanced_df['baseflow']
        self.enhanced_df['baseflow_index'] = self.enhanced_df['baseflow'] / self.enhanced_df['RUNOFF']
        
        # 2. 径流递减特征 (Recession Analysis)
        print("2️⃣ 径流递减特征...")
        self.enhanced_df['recession_constant'] = self._calculate_recession_constant(runoff)
        self.enhanced_df['recession_rate'] = self._calculate_recession_rate(runoff)
        
        # 3. 流量持续曲线特征 (Flow Duration Curve)
        print("3️⃣ 流量持续曲线特征...")
        self.enhanced_df['flow_percentile'] = self._calculate_flow_percentiles(runoff)
        self.enhanced_df['flow_exceedance'] = self._calculate_flow_exceedance(runoff)
        
        # 4. 洪峰特征 (Peak Flow Characteristics)
        print("4️⃣ 洪峰特征...")
        self.enhanced_df['peak_indicator'] = self._identify_peaks(runoff)
        self.enhanced_df['days_since_peak'] = self._days_since_peak(runoff)
        self.enhanced_df['peak_magnitude'] = self._peak_magnitude(runoff)
        
        # 5. 季节性水文特征 (Seasonal Hydrological Features)
        print("5️⃣ 季节性水文特征...")
        self.enhanced_df['seasonal_index'] = self._calculate_seasonal_index()
        self.enhanced_df['monthly_anomaly'] = self._calculate_monthly_anomaly()
        
        # 6. 径流变异性特征 (Runoff Variability)
        print("6️⃣ 径流变异性特征...")
        self.enhanced_df['cv_7day'] = self._rolling_cv(runoff, 7)
        self.enhanced_df['cv_30day'] = self._rolling_cv(runoff, 30)
        self.enhanced_df['variability_index'] = self._variability_index(runoff)
        
        # 7. 水文连通性特征 (Hydrological Connectivity)
        print("7️⃣ 水文连通性特征...")
        self.enhanced_df['antecedent_index_5'] = self._antecedent_precipitation_index(5)
        self.enhanced_df['antecedent_index_30'] = self._antecedent_precipitation_index(30)
        
        # 8. 径流记忆效应 (Runoff Memory Effect)
        print("8️⃣ 径流记忆效应特征...")
        self.enhanced_df['memory_1day'] = self.enhanced_df['RUNOFF'].shift(1)
        self.enhanced_df['memory_7day'] = self.enhanced_df['RUNOFF'].rolling(7).mean().shift(1)
        self.enhanced_df['memory_30day'] = self.enhanced_df['RUNOFF'].rolling(30).mean().shift(1)
        
        # 9. 径流趋势特征 (Runoff Trend Features)
        print("9️⃣ 径流趋势特征...")
        self.enhanced_df['trend_7day'] = self._calculate_trend(runoff, 7)
        self.enhanced_df['trend_30day'] = self._calculate_trend(runoff, 30)
        self.enhanced_df['acceleration'] = self._calculate_acceleration(runoff)
        
        # 10. 极值特征 (Extreme Value Features)
        print("🔟 极值特征...")
        self.enhanced_df['extreme_high'] = self._extreme_indicator(runoff, 'high')
        self.enhanced_df['extreme_low'] = self._extreme_indicator(runoff, 'low')
        self.enhanced_df['return_period'] = self._estimate_return_period(runoff)
        
        print(f"✅ 特征工程完成! 新增 {len(self.enhanced_df.columns) - len(self.df.columns)} 个水文学特征")
        
    def _calculate_baseflow(self, runoff, alpha=0.925):
        """计算基流 - 使用数字滤波法"""
        baseflow = np.zeros_like(runoff)
        baseflow[0] = runoff[0]
        
        for i in range(1, len(runoff)):
            baseflow[i] = alpha * baseflow[i-1] + (1-alpha) * runoff[i]
            if baseflow[i] > runoff[i]:
                baseflow[i] = runoff[i]
        
        return baseflow
    
    def _calculate_recession_constant(self, runoff, window=30):
        """计算径流递减常数"""
        recession_const = np.full_like(runoff, np.nan)

        for i in range(window, len(runoff)):
            window_data = runoff[i-window:i]
            if len(window_data) > 1:
                # 寻找递减段
                decreasing = np.diff(window_data) < 0
                if np.sum(decreasing) > window // 2:
                    # 计算递减常数 - 修复索引问题
                    decreasing_values = window_data[1:][decreasing]  # 对应diff的长度
                    if len(decreasing_values) > 1 and np.all(decreasing_values > 0):
                        log_q = np.log(decreasing_values)
                        if len(log_q) > 1:
                            slope, _, _, _, _ = stats.linregress(range(len(log_q)), log_q)
                            recession_const[i] = -slope

        return recession_const
    
    def _calculate_recession_rate(self, runoff, window=7):
        """计算径流递减率"""
        gradient = np.gradient(runoff)
        # 避免除零错误
        rate = np.where(runoff != 0, -gradient / runoff, 0)
        return rate
    
    def _calculate_flow_percentiles(self, runoff):
        """计算流量百分位数"""
        percentiles = np.zeros_like(runoff)
        sorted_flow = np.sort(runoff)
        
        for i, flow in enumerate(runoff):
            percentile = (np.searchsorted(sorted_flow, flow) / len(sorted_flow)) * 100
            percentiles[i] = percentile
        
        return percentiles
    
    def _calculate_flow_exceedance(self, runoff):
        """计算流量超越概率"""
        return 100 - self._calculate_flow_percentiles(runoff)
    
    def _identify_peaks(self, runoff, prominence=None):
        """识别洪峰"""
        if prominence is None:
            prominence = np.std(runoff) * 0.5
        
        peaks, _ = find_peaks(runoff, prominence=prominence)
        peak_indicator = np.zeros_like(runoff)
        peak_indicator[peaks] = 1
        
        return peak_indicator
    
    def _days_since_peak(self, runoff):
        """距离上次洪峰的天数"""
        peaks = self._identify_peaks(runoff)
        days_since = np.zeros_like(runoff)
        last_peak = -1
        
        for i in range(len(runoff)):
            if peaks[i] == 1:
                last_peak = i
            if last_peak >= 0:
                days_since[i] = i - last_peak
            else:
                days_since[i] = i
        
        return days_since
    
    def _peak_magnitude(self, runoff, window=7):
        """洪峰量级"""
        rolling_max = pd.Series(runoff).rolling(window, center=True).max()
        return rolling_max.fillna(method='bfill').fillna(method='ffill').values
    
    def _calculate_seasonal_index(self):
        """计算季节性指数"""
        month = self.enhanced_df.index.month
        seasonal_mean = self.enhanced_df.groupby(month)['RUNOFF'].transform('mean')
        annual_mean = self.enhanced_df['RUNOFF'].mean()
        return seasonal_mean / annual_mean
    
    def _calculate_monthly_anomaly(self):
        """计算月度异常值"""
        month = self.enhanced_df.index.month
        monthly_mean = self.enhanced_df.groupby(month)['RUNOFF'].transform('mean')
        return self.enhanced_df['RUNOFF'] - monthly_mean
    
    def _rolling_cv(self, runoff, window):
        """滚动变异系数"""
        series = pd.Series(runoff)
        rolling_mean = series.rolling(window).mean()
        rolling_std = series.rolling(window).std()
        return (rolling_std / rolling_mean).fillna(0).values
    
    def _variability_index(self, runoff, window=30):
        """径流变异性指数"""
        series = pd.Series(runoff)
        rolling_range = series.rolling(window).max() - series.rolling(window).min()
        rolling_mean = series.rolling(window).mean()
        return (rolling_range / rolling_mean).fillna(0).values
    
    def _antecedent_precipitation_index(self, days):
        """前期降水指数 (使用RUNOFF作为代理)"""
        weights = np.exp(-np.arange(days) / (days/3))  # 指数衰减权重
        api = np.zeros(len(self.enhanced_df))
        
        for i in range(days, len(self.enhanced_df)):
            api[i] = np.sum(self.enhanced_df['RUNOFF'].iloc[i-days:i].values * weights)
        
        return api
    
    def _calculate_trend(self, runoff, window):
        """计算趋势"""
        trends = np.zeros_like(runoff)
        
        for i in range(window, len(runoff)):
            y = runoff[i-window:i]
            x = np.arange(len(y))
            if len(y) > 1:
                slope, _, _, _, _ = stats.linregress(x, y)
                trends[i] = slope
        
        return trends
    
    def _calculate_acceleration(self, runoff):
        """计算径流加速度"""
        velocity = np.gradient(runoff)
        acceleration = np.gradient(velocity)
        return acceleration
    
    def _extreme_indicator(self, runoff, extreme_type='high'):
        """极值指示器"""
        if extreme_type == 'high':
            threshold = np.percentile(runoff, 95)
            return (runoff > threshold).astype(int)
        else:
            threshold = np.percentile(runoff, 5)
            return (runoff < threshold).astype(int)
    
    def _estimate_return_period(self, runoff):
        """估算重现期"""
        sorted_flow = np.sort(runoff)[::-1]  # 降序排列
        ranks = np.arange(1, len(sorted_flow) + 1)
        return_periods = (len(sorted_flow) + 1) / ranks
        
        # 为每个流量值分配重现期
        result = np.zeros_like(runoff)
        for i, flow in enumerate(runoff):
            idx = np.searchsorted(-sorted_flow, -flow)  # 在降序数组中搜索
            if idx < len(return_periods):
                result[i] = return_periods[idx]
            else:
                result[i] = return_periods[-1]
        
        return result

    def analyze_feature_importance(self):
        """分析新特征的重要性"""
        print("\n📊 分析水文学特征重要性")
        print("="*40)

        # 移除缺失值
        analysis_df = self.enhanced_df.dropna()

        # 获取所有新特征
        original_features = self.df.columns.tolist()
        new_features = [col for col in self.enhanced_df.columns if col not in original_features]

        print(f"原始特征数量: {len(original_features)}")
        print(f"新增特征数量: {len(new_features)}")

        # 计算与目标变量的相关性
        correlations = {}
        target = analysis_df['RUNOFF']

        for feature in new_features:
            if feature in analysis_df.columns:
                corr = analysis_df[feature].corr(target)
                if not np.isnan(corr):
                    correlations[feature] = abs(corr)

        # 排序并显示
        sorted_corr = sorted(correlations.items(), key=lambda x: x[1], reverse=True)

        print(f"\n🔝 前10个最相关的新特征:")
        for i, (feature, corr) in enumerate(sorted_corr[:10], 1):
            print(f"  {i:2d}. {feature:<25} 相关性: {corr:.4f}")

        return sorted_corr

    def save_enhanced_dataset(self, output_file=None):
        """保存增强后的数据集"""
        if output_file is None:
            output_file = 'enhanced_hydrological_features.csv'

        print(f"\n💾 保存增强数据集到: {output_file}")

        # 重置索引以保存日期
        save_df = self.enhanced_df.reset_index()
        save_df.to_csv(output_file, index=False)

        print(f"✅ 数据集已保存")
        print(f"📊 最终数据形状: {save_df.shape}")
        print(f"📅 时间范围: {save_df['DATE'].min()} 到 {save_df['DATE'].max()}")

        return output_file

    def create_feature_summary(self):
        """创建特征摘要报告"""
        print("\n📋 生成特征摘要报告")
        print("="*40)

        # 获取新特征
        original_features = self.df.columns.tolist()
        new_features = [col for col in self.enhanced_df.columns if col not in original_features]

        # 特征分类
        feature_categories = {
            '基流分离': ['baseflow', 'quickflow', 'baseflow_index'],
            '径流递减': ['recession_constant', 'recession_rate'],
            '流量持续曲线': ['flow_percentile', 'flow_exceedance'],
            '洪峰特征': ['peak_indicator', 'days_since_peak', 'peak_magnitude'],
            '季节性特征': ['seasonal_index', 'monthly_anomaly'],
            '变异性特征': ['cv_7day', 'cv_30day', 'variability_index'],
            '水文连通性': ['antecedent_index_5', 'antecedent_index_30'],
            '记忆效应': ['memory_1day', 'memory_7day', 'memory_30day'],
            '趋势特征': ['trend_7day', 'trend_30day', 'acceleration'],
            '极值特征': ['extreme_high', 'extreme_low', 'return_period']
        }

        summary = {
            '数据概览': {
                '原始特征数': len(original_features),
                '新增特征数': len(new_features),
                '总特征数': len(self.enhanced_df.columns),
                '数据点数': len(self.enhanced_df),
                '时间跨度': f"{self.enhanced_df.index.min()} 到 {self.enhanced_df.index.max()}"
            },
            '特征分类': feature_categories,
            '水文学原理': {
                '基流分离': '分离基流和快流成分，反映流域蓄水能力',
                '径流递减': '分析洪水退水过程，反映流域调蓄特性',
                '流量持续曲线': '描述流量分布特征，反映水资源可利用性',
                '洪峰特征': '识别和量化洪水事件，反映极端水文过程',
                '季节性特征': '捕捉季节性变化模式，反映气候影响',
                '变异性特征': '量化径流变异程度，反映系统稳定性',
                '水文连通性': '反映前期条件对当前径流的影响',
                '记忆效应': '捕捉径流的时间依赖性和惯性',
                '趋势特征': '识别径流变化趋势和加速度',
                '极值特征': '识别和量化极端水文事件'
            }
        }

        # 保存摘要
        import json
        with open('hydrological_features_summary.json', 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2, default=str)

        print("✅ 特征摘要已保存到: hydrological_features_summary.json")

        return summary

    def visualize_key_features(self, save_plots=True):
        """可视化关键特征"""
        print("\n📈 可视化关键水文学特征")
        print("="*40)

        # 设置图形样式
        plt.style.use('default')
        fig, axes = plt.subplots(3, 2, figsize=(15, 12))
        fig.suptitle('水文学特征可视化', fontsize=16, fontweight='bold')

        # 1. 基流分离
        axes[0,0].plot(self.enhanced_df.index[:365], self.enhanced_df['RUNOFF'][:365],
                      label='总径流', alpha=0.7)
        axes[0,0].plot(self.enhanced_df.index[:365], self.enhanced_df['baseflow'][:365],
                      label='基流', alpha=0.8)
        axes[0,0].fill_between(self.enhanced_df.index[:365],
                              self.enhanced_df['baseflow'][:365],
                              self.enhanced_df['RUNOFF'][:365],
                              alpha=0.3, label='快流')
        axes[0,0].set_title('基流分离 (前365天)')
        axes[0,0].legend()
        axes[0,0].set_ylabel('径流 (m³/s)')

        # 2. 流量持续曲线
        sorted_flow = np.sort(self.enhanced_df['RUNOFF'])[::-1]
        exceedance = np.arange(1, len(sorted_flow)+1) / len(sorted_flow) * 100
        axes[0,1].semilogy(exceedance, sorted_flow)
        axes[0,1].set_title('流量持续曲线')
        axes[0,1].set_xlabel('超越概率 (%)')
        axes[0,1].set_ylabel('径流 (m³/s)')
        axes[0,1].grid(True, alpha=0.3)

        # 3. 季节性模式
        monthly_avg = self.enhanced_df.groupby(self.enhanced_df.index.month)['RUNOFF'].mean()
        axes[1,0].bar(range(1, 13), monthly_avg.values)
        axes[1,0].set_title('月平均径流')
        axes[1,0].set_xlabel('月份')
        axes[1,0].set_ylabel('径流 (m³/s)')
        axes[1,0].set_xticks(range(1, 13))

        # 4. 径流变异性
        axes[1,1].plot(self.enhanced_df.index[:365], self.enhanced_df['cv_30day'][:365])
        axes[1,1].set_title('30天滚动变异系数')
        axes[1,1].set_ylabel('变异系数')

        # 5. 洪峰识别
        peak_days = self.enhanced_df[self.enhanced_df['peak_indicator'] == 1].index[:50]
        axes[2,0].plot(self.enhanced_df.index[:365], self.enhanced_df['RUNOFF'][:365], alpha=0.7)
        peak_values = self.enhanced_df.loc[peak_days, 'RUNOFF'][:50]
        axes[2,0].scatter(peak_days, peak_values, color='red', s=30, label='洪峰')
        axes[2,0].set_title('洪峰识别 (前365天)')
        axes[2,0].legend()
        axes[2,0].set_ylabel('径流 (m³/s)')

        # 6. 记忆效应
        axes[2,1].scatter(self.enhanced_df['memory_1day'], self.enhanced_df['RUNOFF'],
                         alpha=0.5, s=1)
        axes[2,1].set_title('1天记忆效应')
        axes[2,1].set_xlabel('前1天径流 (m³/s)')
        axes[2,1].set_ylabel('当前径流 (m³/s)')

        plt.tight_layout()

        if save_plots:
            plt.savefig('hydrological_features_visualization.png', dpi=300, bbox_inches='tight')
            print("✅ 可视化图表已保存到: hydrological_features_visualization.png")

        plt.show()

    def run_complete_feature_engineering(self):
        """运行完整的特征工程流程"""
        print("🎯 水文学特征工程完整流程")
        print("="*50)

        # 1. 加载数据
        if not self.load_data():
            return None

        # 2. 创建水文学特征
        self.create_hydrological_features()

        # 3. 分析特征重要性
        feature_importance = self.analyze_feature_importance()

        # 4. 保存增强数据集
        output_file = self.save_enhanced_dataset()

        # 5. 创建特征摘要
        summary = self.create_feature_summary()

        # 6. 可视化关键特征
        self.visualize_key_features()

        print(f"\n🎉 水文学特征工程完成!")
        print(f"📊 增强数据集: {output_file}")
        print(f"📋 特征摘要: hydrological_features_summary.json")
        print(f"📈 可视化图表: hydrological_features_visualization.png")

        return {
            'enhanced_data': self.enhanced_df,
            'feature_importance': feature_importance,
            'summary': summary,
            'output_file': output_file
        }


def main():
    """主函数"""
    print("🌊 基于水文学原理的径流特征工程")
    print("="*50)

    # 创建特征工程器
    engineer = HydrologicalFeatureEngineer()

    # 运行完整流程
    results = engineer.run_complete_feature_engineering()

    if results:
        print(f"\n💡 使用建议:")
        print(f"1. 使用增强数据集进行TimeMixer训练")
        print(f"2. 重点关注相关性最高的前10个新特征")
        print(f"3. 根据水文学原理解释模型预测结果")
        print(f"4. 考虑季节性和极值特征的影响")

        # 显示推荐的特征组合
        top_features = results['feature_importance'][:5]
        print(f"\n🎯 推荐的前5个新特征:")
        for i, (feature, corr) in enumerate(top_features, 1):
            print(f"  {i}. {feature} (相关性: {corr:.4f})")


if __name__ == "__main__":
    main()
