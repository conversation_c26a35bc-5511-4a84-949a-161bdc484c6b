"""
Runoff Forecasting Demo Script
=============================

This script demonstrates the complete forecasting pipeline using your runoff data.
Based on the data analysis, we know:
- Shape: (24836, 2) - about 68 years of daily data
- Columns: ['DATA', 'runoff']
- Date format: YYYY-MM-DD
- Runoff range: reasonable values for water flow
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def load_runoff_data():
    """Load the runoff data."""
    print("🌊 Loading Runoff Data (1950-2017)")
    print("=" * 50)
    
    file_path = r'C:\Users\<USER>\Desktop\timemix\1964-2017dailyRunoff.csv'
    
    try:
        # Load data (we know it's Excel format)
        data = pd.read_excel(file_path)
        print(f"✅ Successfully loaded data")
        print(f"   📊 Shape: {data.shape}")
        print(f"   📅 Columns: {list(data.columns)}")
        
        # Parse dates
        data['DATA'] = pd.to_datetime(data['DATA'])
        data = data.sort_values('DATA').reset_index(drop=True)
        
        # Basic statistics
        print(f"\n📈 Data Overview:")
        print(f"   📅 Date range: {data['DATA'].min().date()} to {data['DATA'].max().date()}")
        print(f"   ⏱️  Duration: {(data['DATA'].max() - data['DATA'].min()).days:,} days")
        print(f"   💧 Runoff range: {data['runoff'].min():.1f} to {data['runoff'].max():.1f}")
        print(f"   📊 Average runoff: {data['runoff'].mean():.1f}")
        print(f"   🔍 Missing values: {data['runoff'].isnull().sum()}")
        
        return data
        
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        return None

def create_sequences_for_forecasting(data, lookback_days=30, forecast_days=7):
    """Create sequences for time series forecasting."""
    print(f"\n🔧 Preparing Data for Forecasting")
    print(f"   📥 Input: {lookback_days} days")
    print(f"   📤 Output: {forecast_days} days")
    
    # Extract runoff values
    values = data['runoff'].values
    
    # Simple normalization (Min-Max scaling)
    min_val = np.min(values)
    max_val = np.max(values)
    normalized = (values - min_val) / (max_val - min_val)
    
    print(f"   🔄 Normalized data: {min_val:.1f} - {max_val:.1f} → 0.0 - 1.0")
    
    # Create sequences
    X, y = [], []
    dates = []
    
    for i in range(len(normalized) - lookback_days - forecast_days + 1):
        # Input sequence (lookback_days)
        X.append(normalized[i:(i + lookback_days)])
        # Target sequence (forecast_days)
        y.append(normalized[(i + lookback_days):(i + lookback_days + forecast_days)])
        # Store date for reference
        dates.append(data['DATA'].iloc[i + lookback_days])
    
    X = np.array(X).reshape(-1, lookback_days, 1)  # Shape: (samples, timesteps, features)
    y = np.array(y).reshape(-1, forecast_days, 1)
    
    print(f"   ✅ Created {len(X):,} sequences")
    print(f"   📊 Input shape: {X.shape}")
    print(f"   📊 Target shape: {y.shape}")
    
    return X, y, dates, min_val, max_val

def split_data_chronologically(X, y, dates, train_ratio=0.7, val_ratio=0.2):
    """Split data chronologically (important for time series)."""
    print(f"\n📊 Splitting Data Chronologically")
    
    n_samples = len(X)
    train_end = int(n_samples * train_ratio)
    val_end = int(n_samples * (train_ratio + val_ratio))
    
    # Split data
    train_X, train_y = X[:train_end], y[:train_end]
    val_X, val_y = X[train_end:val_end], y[train_end:val_end]
    test_X, test_y = X[val_end:], y[val_end:]
    
    # Split dates
    train_dates = dates[:train_end]
    val_dates = dates[train_end:val_end]
    test_dates = dates[val_end:]
    
    print(f"   🚂 Training: {len(train_X):,} samples ({train_dates[0].date()} to {train_dates[-1].date()})")
    print(f"   🔍 Validation: {len(val_X):,} samples ({val_dates[0].date()} to {val_dates[-1].date()})")
    print(f"   🧪 Testing: {len(test_X):,} samples ({test_dates[0].date()} to {test_dates[-1].date()})")
    
    return (train_X, train_y, train_dates), (val_X, val_y, val_dates), (test_X, test_y, test_dates)

def create_timemixer_model(lookback_days, forecast_days):
    """Create TimeMixer model for forecasting."""
    print(f"\n🤖 Creating TimeMixer Model")
    
    try:
        # Try to import PyPOTS
        from pypots.forecasting.timemixer import TimeMixer
        print("   ✅ PyPOTS TimeMixer imported successfully")
        
        # Model configuration optimized for your data
        config = {
            'n_steps': lookback_days,
            'n_features': 1,  # Univariate (only runoff)
            'n_pred_steps': forecast_days,
            'n_pred_features': 1,
            'n_layers': 2,      # Moderate complexity
            'd_model': 128,     # Model dimension
            'd_ffn': 256,       # Feed-forward dimension
            'top_k': 3,         # Top-k for TimeMixer
            'dropout': 0.1,
            'batch_size': 32,
            'epochs': 30,       # Reasonable for demo
            'patience': 10,     # Early stopping
            'verbose': True
        }
        
        print(f"   ⚙️  Configuration:")
        print(f"      📥 Input: {config['n_steps']} days → 📤 Output: {config['n_pred_steps']} days")
        print(f"      🧠 Model: {config['d_model']}D, {config['n_layers']} layers")
        print(f"      🎯 Training: {config['epochs']} epochs, batch size {config['batch_size']}")
        
        # Create model
        model = TimeMixer(**config)
        print("   ✅ TimeMixer model created successfully")
        
        return model, config
        
    except ImportError:
        print("   ❌ PyPOTS not installed")
        print("   💡 Please install: pip install pypots")
        return None, None
    except Exception as e:
        print(f"   ❌ Error creating model: {e}")
        return None, None

def train_model(model, train_data, val_data):
    """Train the TimeMixer model."""
    print(f"\n🚀 Training TimeMixer Model")
    
    train_X, train_y, _ = train_data
    val_X, val_y, _ = val_data
    
    # Prepare data in PyPOTS format
    train_dict = {'X': train_X}
    val_dict = {'X': val_X}
    
    try:
        print("   🔄 Starting training process...")
        model.fit(train_dict, val_dict)
        print("   ✅ Training completed successfully!")
        return True
    except Exception as e:
        print(f"   ❌ Training failed: {e}")
        return False

def evaluate_and_visualize(model, test_data, min_val, max_val):
    """Evaluate model and create visualizations."""
    print(f"\n📊 Evaluating Model Performance")
    
    test_X, test_y, test_dates = test_data
    
    try:
        # Make predictions
        test_dict = {'X': test_X}
        results = model.predict(test_dict)
        predictions = results['forecasting']
        
        # Denormalize data
        test_y_denorm = test_y * (max_val - min_val) + min_val
        pred_denorm = predictions * (max_val - min_val) + min_val
        
        # Calculate metrics
        mse = np.mean((test_y_denorm - pred_denorm) ** 2)
        rmse = np.sqrt(mse)
        mae = np.mean(np.abs(test_y_denorm - pred_denorm))
        
        print(f"   📈 Performance Metrics:")
        print(f"      🎯 RMSE: {rmse:.2f}")
        print(f"      📏 MAE: {mae:.2f}")
        print(f"      📊 MSE: {mse:.2f}")
        
        # Create visualization
        create_prediction_plots(test_y_denorm, pred_denorm, test_dates)
        
        return rmse, mae
        
    except Exception as e:
        print(f"   ❌ Evaluation failed: {e}")
        return None, None

def create_prediction_plots(actual, predicted, dates):
    """Create prediction visualization plots."""
    print(f"   📊 Creating prediction plots...")
    
    try:
        # Plot first 5 test samples
        fig, axes = plt.subplots(5, 1, figsize=(15, 20))
        
        for i in range(min(5, len(actual))):
            ax = axes[i]
            
            # Plot actual vs predicted
            days = range(len(actual[i]))
            ax.plot(days, actual[i].flatten(), 'b-', label='Actual', linewidth=2, marker='o')
            ax.plot(days, predicted[i].flatten(), 'r--', label='Predicted', linewidth=2, marker='s')
            
            ax.set_title(f'Sample {i+1}: Runoff Forecast (Starting {dates[i].date()})')
            ax.set_xlabel('Days')
            ax.set_ylabel('Runoff')
            ax.legend()
            ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('runoff_forecasting_results.png', dpi=300, bbox_inches='tight')
        print(f"   ✅ Plot saved as 'runoff_forecasting_results.png'")
        
        # Show plot
        plt.show()
        
    except Exception as e:
        print(f"   ⚠️  Plotting failed: {e}")

def main():
    """Main execution function."""
    print("🌊 RUNOFF FORECASTING WITH TIMEMIXER++")
    print("=" * 60)
    print("📅 Processing 1950-2017 Daily Runoff Data")
    print("🎯 Goal: Predict 7-day runoff from 30-day history")
    print()
    
    # Step 1: Load data
    data = load_runoff_data()
    if data is None:
        return
    
    # Step 2: Create sequences
    X, y, dates, min_val, max_val = create_sequences_for_forecasting(data, 30, 7)
    
    # Step 3: Split data
    train_data, val_data, test_data = split_data_chronologically(X, y, dates)
    
    # Step 4: Create model
    model, config = create_timemixer_model(30, 7)
    if model is None:
        print("\n❌ Cannot proceed without TimeMixer model")
        print("💡 Please install PyPOTS: pip install pypots")
        return
    
    # Step 5: Train model
    success = train_model(model, train_data, val_data)
    if not success:
        return
    
    # Step 6: Evaluate and visualize
    rmse, mae = evaluate_and_visualize(model, test_data, min_val, max_val)
    
    # Step 7: Save model
    try:
        model.save("runoff_timemixer_model.pypots")
        print(f"\n💾 Model saved as 'runoff_timemixer_model.pypots'")
    except:
        print(f"\n⚠️  Could not save model")
    
    # Final summary
    print(f"\n🎉 FORECASTING COMPLETED!")
    print("=" * 60)
    if rmse and mae:
        print(f"📊 Final Performance: RMSE={rmse:.2f}, MAE={mae:.2f}")
    print(f"📁 Generated files:")
    print(f"   - runoff_forecasting_results.png (visualization)")
    print(f"   - runoff_timemixer_model.pypots (trained model)")
    print(f"\n✨ Your runoff forecasting system is ready!")

if __name__ == "__main__":
    main()
