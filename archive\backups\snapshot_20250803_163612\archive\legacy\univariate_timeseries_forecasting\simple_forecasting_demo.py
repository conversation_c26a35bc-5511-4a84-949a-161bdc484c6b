"""
Simple Runoff Forecasting Demo (No PyPOTS Required)
===================================================

This script provides a working forecasting solution using basic ML methods
while PyPOTS dependency issues are being resolved.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error, mean_absolute_error
import warnings
warnings.filterwarnings('ignore')

def load_runoff_data():
    """Load the runoff data."""
    print("🌊 Loading Runoff Data (1950-2017)")
    print("=" * 50)
    
    file_path = r'C:\Users\<USER>\Desktop\timemix\1964-2017dailyRunoff.csv'
    
    try:
        # Load data
        data = pd.read_excel(file_path)
        print(f"✅ Successfully loaded data")
        print(f"   📊 Shape: {data.shape}")
        print(f"   📅 Columns: {list(data.columns)}")
        
        # Parse dates
        data['DATA'] = pd.to_datetime(data['DATA'])
        data = data.sort_values('DATA').reset_index(drop=True)
        
        # Basic statistics
        print(f"\n📈 Data Overview:")
        print(f"   📅 Date range: {data['DATA'].min().date()} to {data['DATA'].max().date()}")
        print(f"   ⏱️  Duration: {(data['DATA'].max() - data['DATA'].min()).days:,} days")
        print(f"   💧 Runoff range: {data['runoff'].min():.1f} to {data['runoff'].max():.1f}")
        print(f"   📊 Average runoff: {data['runoff'].mean():.1f}")
        print(f"   🔍 Missing values: {data['runoff'].isnull().sum()}")
        
        return data
        
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        return None

def create_features(data, lookback_days=30, forecast_days=7):
    """Create features for machine learning models."""
    print(f"\n🔧 Creating Features for ML Models")
    print(f"   📥 Lookback window: {lookback_days} days")
    print(f"   📤 Forecast horizon: {forecast_days} days")
    
    values = data['runoff'].values
    dates = data['DATA'].values
    
    # Create feature matrix and targets
    X, y, sample_dates = [], [], []
    
    for i in range(len(values) - lookback_days - forecast_days + 1):
        # Features: past lookback_days values
        features = values[i:(i + lookback_days)]
        
        # Add statistical features
        feature_vector = list(features) + [
            np.mean(features),      # Mean of lookback period
            np.std(features),       # Std of lookback period
            np.min(features),       # Min of lookback period
            np.max(features),       # Max of lookback period
            features[-1] - features[0],  # Trend (last - first)
            np.mean(features[-7:]) if len(features) >= 7 else np.mean(features)  # Recent week average
        ]
        
        X.append(feature_vector)
        
        # Target: next forecast_days values
        targets = values[(i + lookback_days):(i + lookback_days + forecast_days)]
        y.append(targets)
        
        # Store date for reference
        sample_dates.append(dates[i + lookback_days])
    
    X = np.array(X)
    y = np.array(y)
    
    print(f"   ✅ Created {len(X):,} samples")
    print(f"   📊 Feature shape: {X.shape}")
    print(f"   📊 Target shape: {y.shape}")
    print(f"   🔢 Features per sample: {X.shape[1]} (including statistical features)")
    
    return X, y, sample_dates

def split_data_chronologically(X, y, dates, train_ratio=0.7, val_ratio=0.2):
    """Split data chronologically."""
    print(f"\n📊 Splitting Data Chronologically")
    
    n_samples = len(X)
    train_end = int(n_samples * train_ratio)
    val_end = int(n_samples * (train_ratio + val_ratio))
    
    # Split data
    train_X, train_y = X[:train_end], y[:train_end]
    val_X, val_y = X[train_end:val_end], y[train_end:val_end]
    test_X, test_y = X[val_end:], y[val_end:]
    
    # Split dates
    train_dates = dates[:train_end]
    val_dates = dates[train_end:val_end]
    test_dates = dates[val_end:]
    
    print(f"   🚂 Training: {len(train_X):,} samples")
    print(f"   🔍 Validation: {len(val_X):,} samples")
    print(f"   🧪 Testing: {len(test_X):,} samples")
    
    return (train_X, train_y, train_dates), (val_X, val_y, val_dates), (test_X, test_y, test_dates)

def train_models(train_data, val_data):
    """Train multiple forecasting models."""
    print(f"\n🤖 Training Forecasting Models")
    
    train_X, train_y, _ = train_data
    val_X, val_y, _ = val_data
    
    models = {}
    
    # Model 1: Random Forest (for each forecast day)
    print("   🌲 Training Random Forest models...")
    rf_models = []
    for day in range(train_y.shape[1]):  # For each forecast day
        rf = RandomForestRegressor(
            n_estimators=100,
            max_depth=10,
            random_state=42,
            n_jobs=-1
        )
        rf.fit(train_X, train_y[:, day])
        rf_models.append(rf)
    models['RandomForest'] = rf_models
    print("   ✅ Random Forest models trained")
    
    # Model 2: Linear Regression (for each forecast day)
    print("   📈 Training Linear Regression models...")
    lr_models = []
    for day in range(train_y.shape[1]):
        lr = LinearRegression()
        lr.fit(train_X, train_y[:, day])
        lr_models.append(lr)
    models['LinearRegression'] = lr_models
    print("   ✅ Linear Regression models trained")
    
    # Evaluate on validation set
    print("   📊 Evaluating models on validation set...")
    for model_name, model_list in models.items():
        val_pred = np.array([model.predict(val_X) for model in model_list]).T
        val_rmse = np.sqrt(mean_squared_error(val_y, val_pred))
        val_mae = mean_absolute_error(val_y, val_pred)
        print(f"      {model_name}: RMSE={val_rmse:.2f}, MAE={val_mae:.2f}")
    
    return models

def evaluate_models(models, test_data):
    """Evaluate models on test set."""
    print(f"\n📊 Evaluating Models on Test Set")
    
    test_X, test_y, test_dates = test_data
    results = {}
    
    for model_name, model_list in models.items():
        print(f"   🔍 Evaluating {model_name}...")
        
        # Make predictions
        predictions = np.array([model.predict(test_X) for model in model_list]).T
        
        # Calculate metrics
        rmse = np.sqrt(mean_squared_error(test_y, predictions))
        mae = mean_absolute_error(test_y, predictions)
        
        results[model_name] = {
            'predictions': predictions,
            'rmse': rmse,
            'mae': mae
        }
        
        print(f"      📈 RMSE: {rmse:.2f}")
        print(f"      📏 MAE: {mae:.2f}")
    
    return results, test_y, test_dates

def create_visualizations(results, actual, dates):
    """Create prediction visualizations."""
    print(f"\n📊 Creating Visualizations")
    
    # Find best model
    best_model = min(results.keys(), key=lambda x: results[x]['rmse'])
    print(f"   🏆 Best model: {best_model} (RMSE: {results[best_model]['rmse']:.2f})")
    
    best_predictions = results[best_model]['predictions']
    
    # Create plots
    fig, axes = plt.subplots(3, 2, figsize=(15, 18))
    axes = axes.flatten()
    
    # Plot first 6 test samples
    for i in range(min(6, len(actual))):
        ax = axes[i]
        
        days = range(len(actual[i]))
        ax.plot(days, actual[i], 'b-', label='Actual', linewidth=2, marker='o')
        ax.plot(days, best_predictions[i], 'r--', label='Predicted', linewidth=2, marker='s')
        
        ax.set_title(f'Sample {i+1}: Runoff Forecast\n(Starting {dates[i].date()})')
        ax.set_xlabel('Days')
        ax.set_ylabel('Runoff')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('simple_runoff_forecasting_results.png', dpi=300, bbox_inches='tight')
    print(f"   ✅ Visualization saved as 'simple_runoff_forecasting_results.png'")
    
    # Show plot
    plt.show()
    
    return best_model

def main():
    """Main execution function."""
    print("🌊 SIMPLE RUNOFF FORECASTING DEMO")
    print("=" * 60)
    print("📅 Processing 1950-2017 Daily Runoff Data")
    print("🎯 Goal: Predict 7-day runoff using ML models")
    print("💡 No PyPOTS required - using scikit-learn")
    print()
    
    # Step 1: Load data
    data = load_runoff_data()
    if data is None:
        return
    
    # Step 2: Create features
    X, y, dates = create_features(data, lookback_days=30, forecast_days=7)
    
    # Step 3: Split data
    train_data, val_data, test_data = split_data_chronologically(X, y, dates)
    
    # Step 4: Train models
    models = train_models(train_data, val_data)
    
    # Step 5: Evaluate models
    results, actual, test_dates = evaluate_models(models, test_data)
    
    # Step 6: Create visualizations
    best_model = create_visualizations(results, actual, test_dates)
    
    # Step 7: Final summary
    print(f"\n🎉 SIMPLE FORECASTING COMPLETED!")
    print("=" * 60)
    print(f"🏆 Best Model: {best_model}")
    print(f"📊 Performance: RMSE={results[best_model]['rmse']:.2f}, MAE={results[best_model]['mae']:.2f}")
    print(f"📁 Generated files:")
    print(f"   - simple_runoff_forecasting_results.png")
    print(f"\n💡 This demo shows that forecasting works!")
    print(f"   Once PyPOTS is fixed, you can use the advanced TimeMixer++ model")
    print(f"   for potentially better performance.")
    print(f"\n✨ Your runoff forecasting system is working!")

if __name__ == "__main__":
    main()
