"""
运行R²>0.8冲刺训练
==================

基于配置17的成功，测试优化配置18-22，争取突破R²=0.8
"""

import os
import sys
from datetime import datetime

def show_available_configs():
    """显示可用的R²>0.8冲刺配置"""
    print("🎯 R²>0.8冲刺配置列表")
    print("="*50)
    
    configs_info = [
        {
            'id': 17,
            'name': '水文学特征增强_最优配置',
            'r2': 0.7301,
            'status': '✅ 当前最佳',
            'strategy': '基线配置'
        },
        {
            'id': 18,
            'name': '深度模型_R2冲刺0.8_v1',
            'r2': '目标>0.8',
            'status': '🚀 首选',
            'strategy': '深度模型+长训练'
        },
        {
            'id': 19,
            'name': '长序列记忆_R2冲刺0.8_v2',
            'r2': '目标>0.8',
            'status': '📈 记忆优化',
            'strategy': '长序列+大模型'
        },
        {
            'id': 20,
            'name': '精细调优_R2冲刺0.8_v3',
            'r2': '目标>0.8',
            'status': '⚡ 稳妥选择',
            'strategy': '精细参数调优'
        },
        {
            'id': 21,
            'name': '高容量模型_R2冲刺0.8_v4',
            'r2': '目标>0.8',
            'status': '💪 高容量',
            'strategy': '深网络+大FFN'
        },
        {
            'id': 22,
            'name': '集成启发_R2冲刺0.8_v5',
            'r2': '目标>0.8',
            'status': '🔬 创新尝试',
            'strategy': '短预测+超长训练'
        }
    ]
    
    for config in configs_info:
        print(f"配置{config['id']:2d}: {config['name']}")
        print(f"        R²: {config['r2']:<12} {config['status']}")
        print(f"        策略: {config['strategy']}")
        print()
    
    return configs_info

def run_single_config(config_id):
    """运行单个配置"""
    print(f"\n🚀 运行配置{config_id}")
    print("="*40)
    
    # 检查必要文件
    required_files = [
        'enhanced_training_data.csv',
        'enhanced_compatible_training_runner.py',
        'my_parameters.py'
    ]
    
    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ 缺少必要文件: {file}")
            return False
    
    print("✅ 必要文件检查通过")
    
    # 导入训练模块
    try:
        import enhanced_compatible_training_runner
        
        # 获取配置
        from my_parameters import get_my_parameters
        configs = get_my_parameters()
        
        if config_id > len(configs):
            print(f"❌ 配置{config_id}不存在，总共有{len(configs)}个配置")
            return False
        
        selected_config = configs[config_id - 1]  # 索引从0开始
        
        print(f"📋 使用配置: {selected_config['name']}")
        print(f"🔧 关键参数:")
        print(f"  序列长度: {selected_config['n_steps']} → {selected_config['n_pred_steps']}")
        print(f"  模型架构: {selected_config['n_layers']}层, d_model={selected_config['d_model']}")
        print(f"  训练参数: lr={selected_config['learning_rate']:.2e}, epochs={selected_config['epochs']}")
        
        # 开始训练
        print(f"\n🔥 开始训练...")
        start_time = datetime.now()
        
        result = enhanced_compatible_training_runner.run_compatible_training(selected_config)
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        if result:
            print(f"\n✅ 配置{config_id}训练完成!")
            print(f"⏱️ 训练时长: {duration:.1f}秒")
            print(f"📊 请查看 timemixer_evaluation_results.csv 了解R²结果")
            
            # 尝试读取最新结果
            try:
                import pandas as pd
                df = pd.read_csv('timemixer_evaluation_results.csv')
                latest_result = df.iloc[-1]
                r2_value = latest_result['R2']
                
                print(f"\n🎯 训练结果:")
                print(f"  R² Score: {r2_value:.4f}")
                print(f"  MAE: {latest_result['MAE']:.2f}")
                print(f"  RMSE: {latest_result['RMSE']:.2f}")
                
                if r2_value > 0.8:
                    print(f"🎉 恭喜！成功突破R²=0.8！")
                elif r2_value > 0.75:
                    print(f"👍 很好！R²>0.75，接近目标")
                elif r2_value > 0.73:
                    print(f"📈 有进步！超过了基线配置")
                else:
                    print(f"📊 结果记录，继续尝试其他配置")
                
            except Exception as e:
                print(f"📊 结果文件读取失败: {e}")
            
            return True
        else:
            print(f"❌ 配置{config_id}训练失败")
            return False
            
    except Exception as e:
        print(f"❌ 训练过程出错: {e}")
        return False

def run_batch_training(config_ids):
    """批量运行多个配置"""
    print(f"\n🔄 批量训练配置: {config_ids}")
    print("="*50)
    
    results = {}
    
    for config_id in config_ids:
        print(f"\n{'='*20} 配置{config_id} {'='*20}")
        success = run_single_config(config_id)
        results[config_id] = success
        
        if not success:
            print(f"⚠️ 配置{config_id}失败，继续下一个...")
        
        print(f"{'='*50}")
    
    # 总结结果
    print(f"\n📊 批量训练总结:")
    successful = [cid for cid, success in results.items() if success]
    failed = [cid for cid, success in results.items() if not success]
    
    print(f"✅ 成功: {len(successful)}个配置 {successful}")
    print(f"❌ 失败: {len(failed)}个配置 {failed}")
    
    if successful:
        print(f"\n💡 建议查看 timemixer_evaluation_results.csv 对比各配置的R²值")

def interactive_training():
    """交互式训练"""
    print("🎯 R²>0.8冲刺交互式训练")
    print("="*50)
    
    configs_info = show_available_configs()
    
    while True:
        print("\n🔧 训练选项:")
        print("1. 运行单个配置")
        print("2. 批量运行推荐配置 (18, 20, 19)")
        print("3. 批量运行所有冲刺配置 (18-22)")
        print("4. 查看配置详情")
        print("5. 退出")
        
        choice = input("\n请选择 (1-5): ").strip()
        
        if choice == '1':
            config_id = input("请输入配置ID (17-22): ").strip()
            try:
                config_id = int(config_id)
                if 17 <= config_id <= 22:
                    run_single_config(config_id)
                else:
                    print("❌ 请输入17-22之间的配置ID")
            except ValueError:
                print("❌ 请输入有效的数字")
                
        elif choice == '2':
            print("🚀 运行推荐配置组合...")
            run_batch_training([18, 20, 19])
            
        elif choice == '3':
            print("🚀 运行所有冲刺配置...")
            run_batch_training([18, 19, 20, 21, 22])
            
        elif choice == '4':
            show_available_configs()
            
        elif choice == '5':
            print("👋 退出训练程序")
            break
            
        else:
            print("❌ 无效选择，请重试")

def main():
    """主函数"""
    print("🎯 R²>0.8冲刺训练程序")
    print("="*50)
    
    print("📊 目标: 基于配置17 (R²=0.7301) 突破R²=0.8")
    print("🔧 策略: 测试5个优化配置 (配置18-22)")
    print("💡 重点: 利用水文学特征的强记忆效应")
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] == '--config':
            if len(sys.argv) > 2:
                try:
                    config_id = int(sys.argv[2])
                    run_single_config(config_id)
                except ValueError:
                    print("❌ 无效的配置ID")
            else:
                print("❌ 请指定配置ID")
        elif sys.argv[1] == '--batch':
            if len(sys.argv) > 2:
                if sys.argv[2] == 'recommended':
                    run_batch_training([18, 20, 19])
                elif sys.argv[2] == 'all':
                    run_batch_training([18, 19, 20, 21, 22])
                else:
                    print("❌ 无效的批量选项")
            else:
                run_batch_training([18, 20, 19])  # 默认推荐配置
        else:
            print("❌ 无效的命令行参数")
    else:
        # 交互式模式
        interactive_training()

if __name__ == "__main__":
    main()
