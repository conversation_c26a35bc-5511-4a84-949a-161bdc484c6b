@echo off
echo Starting Runoff Forecasting Demo...
echo.

echo Step 1: Installing required packages...
pip install pandas numpy matplotlib openpyxl

echo.
echo Step 2: Running basic demo...
python basic_demo.py

echo.
echo Step 3: If PyPOTS is available, install it...
pip install pypots

echo.
echo Step 4: Running full forecasting demo...
python run_forecasting_demo.py

echo.
echo Demo completed!
pause
