"""
Simple data analysis script for runoff data
"""

import sys
import os
import pandas as pd
import numpy as np

# Add current directory to path
sys.path.append('.')

from data_loader import UnivariateTimeSeriesLoader

def main():
    # File path
    file_path = r'C:\Users\<USER>\Desktop\timemix\1964-2017dailyRunoff.csv'
    
    print("Runoff Data Analysis")
    print("=" * 40)
    
    try:
        # Create loader
        loader = UnivariateTimeSeriesLoader(
            date_column='DATA', 
            target_column='runoff'
        )
        
        # Load data
        print("Loading data...")
        data = loader.load_csv(file_path)
        
        # Basic info
        print(f"\nBasic Information:")
        print(f"  - Data shape: {data.shape}")
        print(f"  - Date range: {data['DATA'].min().date()} to {data['DATA'].max().date()}")
        print(f"  - Duration: {(data['DATA'].max() - data['DATA'].min()).days} days")
        
        # Runoff statistics
        runoff_stats = data['runoff'].describe()
        print(f"\nRunoff Statistics:")
        print(f"  - Mean: {runoff_stats['mean']:.2f}")
        print(f"  - Median: {runoff_stats['50%']:.2f}")
        print(f"  - Std Dev: {runoff_stats['std']:.2f}")
        print(f"  - Min: {runoff_stats['min']:.2f}")
        print(f"  - Max: {runoff_stats['max']:.2f}")
        
        # Check missing values
        missing_info = loader.check_missing_values()
        
        # Seasonal analysis
        data['month'] = data['DATA'].dt.month
        monthly_avg = data.groupby('month')['runoff'].mean()
        
        print(f"\nMonthly Averages:")
        months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 
                  'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
        for i, month in enumerate(months, 1):
            print(f"  - {month}: {monthly_avg[i]:.2f}")
        
        # Recommendation
        total_samples = len(data)
        print(f"\nRecommendation for {total_samples:,} samples:")
        if total_samples > 20000:
            print("  ✓ Large dataset - Use long_term or custom configuration")
            print("  ✓ Suggested: 90 days input → 30 days forecast")
        elif total_samples > 5000:
            print("  ✓ Medium-large dataset - Use long_term configuration")
            print("  ✓ Suggested: 90 days input → 30 days forecast")
        else:
            print("  ✓ Medium dataset - Use medium_term configuration")
            print("  ✓ Suggested: 30 days input → 7 days forecast")
        
        print(f"\n✅ Data analysis completed successfully!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    return True

if __name__ == "__main__":
    main()
