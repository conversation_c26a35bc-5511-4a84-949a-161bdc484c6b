"""
简化的水文学特征工程
==================

基于水文学原理构造关键的径流特征，专注于最有效的特征。
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

class SimplifiedHydrologicalFeatures:
    """简化的水文学特征工程器"""
    
    def __init__(self, data_file='1971-2017all_cleaned_CHANGBEI_daily_weather_data.csv'):
        self.data_file = data_file
        self.df = None
        self.enhanced_df = None
        
    def load_data(self):
        """加载数据"""
        print("🌊 加载径流数据")
        print("="*30)
        
        try:
            self.df = pd.read_csv(self.data_file)
            self.df['DATE'] = pd.to_datetime(self.df['DATE'], format='%Y/%m/%d')
            self.df = self.df.set_index('DATE').sort_index()
            
            print(f"✅ 数据加载成功: {self.df.shape}")
            print(f"📅 时间范围: {self.df.index.min()} 到 {self.df.index.max()}")
            print(f"💧 径流范围: {self.df['RUNOFF'].min():.1f} - {self.df['RUNOFF'].max():.1f} m³/s")
            
            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def create_hydrological_features(self):
        """创建关键的水文学特征"""
        print("\n🔧 构造水文学特征")
        print("="*30)
        
        # 复制原始数据
        self.enhanced_df = self.df.copy()
        runoff = self.df['RUNOFF'].values
        
        print("1️⃣ 径流记忆效应特征...")
        # 1. 径流记忆效应 (最重要的水文学特征)
        self.enhanced_df['runoff_lag1'] = self.enhanced_df['RUNOFF'].shift(1)
        self.enhanced_df['runoff_lag3'] = self.enhanced_df['RUNOFF'].shift(3)
        self.enhanced_df['runoff_lag7'] = self.enhanced_df['RUNOFF'].shift(7)
        self.enhanced_df['runoff_lag15'] = self.enhanced_df['RUNOFF'].shift(15)
        self.enhanced_df['runoff_lag30'] = self.enhanced_df['RUNOFF'].shift(30)
        
        print("2️⃣ 滚动统计特征...")
        # 2. 滚动统计特征
        self.enhanced_df['runoff_mean_7d'] = self.enhanced_df['RUNOFF'].rolling(7).mean()
        self.enhanced_df['runoff_mean_15d'] = self.enhanced_df['RUNOFF'].rolling(15).mean()
        self.enhanced_df['runoff_mean_30d'] = self.enhanced_df['RUNOFF'].rolling(30).mean()
        
        self.enhanced_df['runoff_std_7d'] = self.enhanced_df['RUNOFF'].rolling(7).std()
        self.enhanced_df['runoff_std_15d'] = self.enhanced_df['RUNOFF'].rolling(15).std()
        self.enhanced_df['runoff_std_30d'] = self.enhanced_df['RUNOFF'].rolling(30).std()
        
        self.enhanced_df['runoff_max_7d'] = self.enhanced_df['RUNOFF'].rolling(7).max()
        self.enhanced_df['runoff_min_7d'] = self.enhanced_df['RUNOFF'].rolling(7).min()
        
        print("3️⃣ 变化率特征...")
        # 3. 变化率特征
        self.enhanced_df['runoff_change_1d'] = self.enhanced_df['RUNOFF'].diff(1)
        self.enhanced_df['runoff_change_3d'] = self.enhanced_df['RUNOFF'].diff(3)
        self.enhanced_df['runoff_change_7d'] = self.enhanced_df['RUNOFF'].diff(7)
        
        self.enhanced_df['runoff_pct_change_1d'] = self.enhanced_df['RUNOFF'].pct_change(1)
        self.enhanced_df['runoff_pct_change_7d'] = self.enhanced_df['RUNOFF'].pct_change(7)
        
        print("4️⃣ 季节性特征...")
        # 4. 季节性特征
        self.enhanced_df['month'] = self.enhanced_df.index.month
        self.enhanced_df['day_of_year'] = self.enhanced_df.index.dayofyear
        self.enhanced_df['season'] = self.enhanced_df['month'].map({
            12: 1, 1: 1, 2: 1,  # 冬季
            3: 2, 4: 2, 5: 2,   # 春季
            6: 3, 7: 3, 8: 3,   # 夏季
            9: 4, 10: 4, 11: 4  # 秋季
        })
        
        # 月度异常值
        monthly_mean = self.enhanced_df.groupby('month')['RUNOFF'].transform('mean')
        self.enhanced_df['runoff_monthly_anomaly'] = self.enhanced_df['RUNOFF'] - monthly_mean
        
        print("5️⃣ 基流分离特征...")
        # 5. 简化的基流分离
        self.enhanced_df['baseflow'] = self._simple_baseflow_separation(runoff)
        self.enhanced_df['quickflow'] = self.enhanced_df['RUNOFF'] - self.enhanced_df['baseflow']
        self.enhanced_df['baseflow_ratio'] = self.enhanced_df['baseflow'] / (self.enhanced_df['RUNOFF'] + 1e-8)
        
        print("6️⃣ 极值特征...")
        # 6. 极值特征
        self.enhanced_df['is_high_flow'] = (self.enhanced_df['RUNOFF'] > 
                                          self.enhanced_df['RUNOFF'].quantile(0.9)).astype(int)
        self.enhanced_df['is_low_flow'] = (self.enhanced_df['RUNOFF'] < 
                                         self.enhanced_df['RUNOFF'].quantile(0.1)).astype(int)
        
        print("7️⃣ 趋势特征...")
        # 7. 趋势特征
        self.enhanced_df['runoff_trend_7d'] = self._calculate_trend(runoff, 7)
        self.enhanced_df['runoff_trend_30d'] = self._calculate_trend(runoff, 30)
        
        print("8️⃣ 变异性特征...")
        # 8. 变异性特征
        self.enhanced_df['runoff_cv_7d'] = (self.enhanced_df['runoff_std_7d'] / 
                                          (self.enhanced_df['runoff_mean_7d'] + 1e-8))
        self.enhanced_df['runoff_cv_30d'] = (self.enhanced_df['runoff_std_30d'] / 
                                           (self.enhanced_df['runoff_mean_30d'] + 1e-8))
        
        print("9️⃣ 前期影响特征...")
        # 9. 前期影响指数
        self.enhanced_df['antecedent_5d'] = self._antecedent_index(runoff, 5)
        self.enhanced_df['antecedent_15d'] = self._antecedent_index(runoff, 15)
        self.enhanced_df['antecedent_30d'] = self._antecedent_index(runoff, 30)
        
        print("🔟 相对位置特征...")
        # 10. 相对位置特征
        self.enhanced_df['runoff_percentile'] = self._calculate_percentile_rank(runoff)
        
        # 移除临时列
        self.enhanced_df = self.enhanced_df.drop(['month'], axis=1)
        
        print(f"✅ 特征工程完成! 新增 {len(self.enhanced_df.columns) - len(self.df.columns)} 个特征")
        
    def _simple_baseflow_separation(self, runoff, alpha=0.925):
        """简化的基流分离"""
        baseflow = np.zeros_like(runoff)
        baseflow[0] = runoff[0]
        
        for i in range(1, len(runoff)):
            baseflow[i] = alpha * baseflow[i-1] + (1-alpha) * runoff[i]
            if baseflow[i] > runoff[i]:
                baseflow[i] = runoff[i]
        
        return baseflow
    
    def _calculate_trend(self, runoff, window):
        """计算趋势"""
        trends = np.zeros_like(runoff)
        
        for i in range(window, len(runoff)):
            y = runoff[i-window:i]
            x = np.arange(len(y))
            if len(y) > 1:
                # 简单线性回归
                slope = np.polyfit(x, y, 1)[0]
                trends[i] = slope
        
        return trends
    
    def _antecedent_index(self, runoff, days):
        """前期影响指数"""
        weights = np.exp(-np.arange(days) / (days/3))  # 指数衰减权重
        api = np.zeros(len(runoff))
        
        for i in range(days, len(runoff)):
            api[i] = np.sum(runoff[i-days:i] * weights)
        
        return api
    
    def _calculate_percentile_rank(self, runoff):
        """计算百分位排名"""
        percentiles = np.zeros_like(runoff)
        sorted_flow = np.sort(runoff)
        
        for i, flow in enumerate(runoff):
            percentile = (np.searchsorted(sorted_flow, flow) / len(sorted_flow)) * 100
            percentiles[i] = percentile
        
        return percentiles
    
    def analyze_feature_importance(self):
        """分析特征重要性"""
        print("\n📊 分析特征重要性")
        print("="*30)
        
        # 移除缺失值
        analysis_df = self.enhanced_df.dropna()
        
        # 获取新特征
        original_features = self.df.columns.tolist()
        new_features = [col for col in self.enhanced_df.columns if col not in original_features]
        
        print(f"原始特征数量: {len(original_features)}")
        print(f"新增特征数量: {len(new_features)}")
        
        # 计算相关性
        correlations = {}
        target = analysis_df['RUNOFF']
        
        for feature in new_features:
            if feature in analysis_df.columns:
                corr = analysis_df[feature].corr(target)
                if not np.isnan(corr):
                    correlations[feature] = abs(corr)
        
        # 排序
        sorted_corr = sorted(correlations.items(), key=lambda x: x[1], reverse=True)
        
        print(f"\n🔝 前15个最相关的新特征:")
        for i, (feature, corr) in enumerate(sorted_corr[:15], 1):
            print(f"  {i:2d}. {feature:<25} 相关性: {corr:.4f}")
        
        return sorted_corr
    
    def save_enhanced_dataset(self, output_file='enhanced_hydrological_features.csv'):
        """保存增强数据集"""
        print(f"\n💾 保存增强数据集")
        print("="*30)
        
        # 重置索引以保存日期
        save_df = self.enhanced_df.reset_index()
        save_df.to_csv(output_file, index=False)
        
        print(f"✅ 数据集已保存到: {output_file}")
        print(f"📊 最终数据形状: {save_df.shape}")
        
        return output_file
    
    def run_complete_feature_engineering(self):
        """运行完整特征工程"""
        print("🎯 简化水文学特征工程")
        print("="*50)
        
        # 1. 加载数据
        if not self.load_data():
            return None
        
        # 2. 创建特征
        self.create_hydrological_features()
        
        # 3. 分析重要性
        feature_importance = self.analyze_feature_importance()
        
        # 4. 保存数据集
        output_file = self.save_enhanced_dataset()
        
        print(f"\n🎉 特征工程完成!")
        print(f"📊 增强数据集: {output_file}")
        
        return {
            'enhanced_data': self.enhanced_df,
            'feature_importance': feature_importance,
            'output_file': output_file
        }

def main():
    """主函数"""
    print("🌊 简化水文学特征工程")
    print("="*50)
    
    # 创建特征工程器
    engineer = SimplifiedHydrologicalFeatures()
    
    # 运行特征工程
    results = engineer.run_complete_feature_engineering()
    
    if results:
        print(f"\n💡 使用建议:")
        print(f"1. 使用增强数据集进行TimeMixer训练")
        print(f"2. 重点关注相关性最高的前10个新特征")
        print(f"3. 径流记忆效应特征通常最重要")
        
        # 显示推荐特征
        top_features = results['feature_importance'][:10]
        print(f"\n🎯 推荐的前10个新特征:")
        for i, (feature, corr) in enumerate(top_features, 1):
            print(f"  {i:2d}. {feature:<25} 相关性: {corr:.4f}")

if __name__ == "__main__":
    main()
