"""
高级批量训练运行器
==================

使用高级参数配置进行批量训练实验。
"""

from single_training_runner import run_timemixer_training
from advanced_parameter_configs import ParameterConfigGenerator
import time
import pandas as pd

def run_experiment_category(category_name: str, configs: list, max_configs: int = None):
    """运行特定类别的实验"""
    
    print(f"\n{'='*60}")
    print(f"开始 {category_name} 实验")
    print(f"{'='*60}")
    
    if max_configs and len(configs) > max_configs:
        configs = configs[:max_configs]
        print(f"限制为前 {max_configs} 个配置")
    
    print(f"将运行 {len(configs)} 个配置:")
    for i, config in enumerate(configs, 1):
        print(f"  {i}. {config['name']}")
    
    input(f"\n按回车键开始 {category_name} 实验...")
    
    results = []
    start_time = time.time()
    
    for i, config in enumerate(configs, 1):
        print(f"\n{'-'*50}")
        print(f"{category_name} 进度: {i}/{len(configs)}")
        print(f"配置: {config['name']}")
        print(f"{'-'*50}")
        
        # 显示关键参数
        key_params = ['n_steps', 'n_pred_steps', 'n_layers', 'd_model', 'learning_rate', 'epochs', 'device']
        print("关键参数:")
        for key in key_params:
            if key in config:
                print(f"  {key}: {config[key]}")
        
        config_start_time = time.time()
        
        try:
            result = run_timemixer_training(config)
            config_end_time = time.time()
            config_time = config_end_time - config_start_time
            
            if result:
                result['category'] = category_name
                result['config_name'] = config['name']
                result['config_time'] = config_time
                results.append(result)
                
                print(f"\n✓ {config['name']} 完成 (耗时: {config_time:.1f}s)")
                print(f"  MAE: {result['mae']:.4f}")
                print(f"  RMSE: {result['rmse']:.4f}")
                print(f"  NSE: {result['nse']:.4f}")
                print(f"  R²: {result['r2']:.4f}")
            else:
                print(f"\n✗ {config['name']} 失败")
                
        except Exception as e:
            print(f"\n✗ {config['name']} 异常: {e}")
        
        # 显示总体进度
        elapsed_time = time.time() - start_time
        avg_time = elapsed_time / i
        remaining_time = avg_time * (len(configs) - i)
        
        print(f"\n进度统计:")
        print(f"  已完成: {i}/{len(configs)}")
        print(f"  已用时间: {elapsed_time/60:.1f}分钟")
        print(f"  预计剩余: {remaining_time/60:.1f}分钟")
        
        # 间隔时间
        if i < len(configs):
            print("等待 3 秒...")
            time.sleep(3)
    
    return results

def analyze_category_results(category_name: str, results: list):
    """分析类别实验结果"""
    
    if not results:
        print(f"\n{category_name} 没有成功的结果")
        return
    
    print(f"\n{'='*60}")
    print(f"{category_name} 结果分析")
    print(f"{'='*60}")
    
    # 结果表格
    print(f"\n详细结果:")
    print(f"{'配置名称':<25} {'MAE':<10} {'RMSE':<10} {'NSE':<8} {'R²':<8} {'时间(s)':<8}")
    print("-" * 75)
    
    for result in results:
        print(f"{result['config_name']:<25} {result['mae']:<10.4f} {result['rmse']:<10.4f} "
              f"{result['nse']:<8.4f} {result['r2']:<8.4f} {result['config_time']:<8.1f}")
    
    # 最佳结果
    best_mae = min(results, key=lambda x: x['mae'])
    best_r2 = max(results, key=lambda x: x['r2'])
    best_nse = max(results, key=lambda x: x['nse'])
    fastest = min(results, key=lambda x: x['config_time'])
    
    print(f"\n{category_name} 最佳结果:")
    print(f"最低 MAE: {best_mae['config_name']} (MAE: {best_mae['mae']:.4f})")
    print(f"最高 R²:  {best_r2['config_name']} (R²: {best_r2['r2']:.4f})")
    print(f"最高 NSE: {best_nse['config_name']} (NSE: {best_nse['nse']:.4f})")
    print(f"最快训练: {fastest['config_name']} (时间: {fastest['config_time']:.1f}s)")

def run_comprehensive_experiments():
    """运行综合实验"""
    
    print("TimeMixer++ 高级批量训练系统")
    print("="*60)
    
    generator = ParameterConfigGenerator()
    all_configs = generator.get_all_experiment_configs()
    
    print(f"\n可用实验类别:")
    for i, (category, configs) in enumerate(all_configs.items(), 1):
        print(f"{i}. {category}: {len(configs)} 个配置")
    
    print(f"\n选择要运行的实验:")
    print("1. 快速实验 (小模型)")
    print("2. 标准实验 (中等模型)")
    print("3. 高性能实验 (大模型)")
    print("4. 学习率调优实验")
    print("5. 架构对比实验")
    print("6. 序列长度实验")
    print("7. 全部实验 (谨慎选择!)")
    print("0. 自定义选择")
    
    choice = input("\n请输入选择 (1-7, 0): ").strip()
    
    all_results = []
    
    if choice == "1":
        results = run_experiment_category("小模型实验", all_configs['small_models'])
        all_results.extend(results)
        analyze_category_results("小模型实验", results)
        
    elif choice == "2":
        results = run_experiment_category("中等模型实验", all_configs['medium_models'])
        all_results.extend(results)
        analyze_category_results("中等模型实验", results)
        
    elif choice == "3":
        results = run_experiment_category("大模型实验", all_configs['large_models'])
        all_results.extend(results)
        analyze_category_results("大模型实验", results)
        
    elif choice == "4":
        results = run_experiment_category("学习率实验", all_configs['learning_rate_experiments'])
        all_results.extend(results)
        analyze_category_results("学习率实验", results)
        
    elif choice == "5":
        results = run_experiment_category("架构实验", all_configs['architecture_experiments'])
        all_results.extend(results)
        analyze_category_results("架构实验", results)
        
    elif choice == "6":
        results = run_experiment_category("序列长度实验", all_configs['sequence_length_experiments'])
        all_results.extend(results)
        analyze_category_results("序列长度实验", results)
        
    elif choice == "7":
        print("\n⚠ 警告: 全部实验将运行大量配置，可能需要数小时!")
        confirm = input("确认运行全部实验? (yes/no): ").strip().lower()
        if confirm == 'yes':
            for category, configs in all_configs.items():
                results = run_experiment_category(category, configs, max_configs=5)  # 限制每类最多5个
                all_results.extend(results)
                analyze_category_results(category, results)
        else:
            print("已取消")
            return
            
    elif choice == "0":
        print("\n自定义实验选择:")
        selected_categories = []
        for i, (category, configs) in enumerate(all_configs.items(), 1):
            select = input(f"运行 {category} ({len(configs)} 个配置)? (y/n): ").strip().lower()
            if select == 'y':
                max_configs = input(f"最多运行几个配置? (默认全部 {len(configs)}): ").strip()
                max_configs = int(max_configs) if max_configs.isdigit() else None
                selected_categories.append((category, configs, max_configs))
        
        for category, configs, max_configs in selected_categories:
            results = run_experiment_category(category, configs, max_configs)
            all_results.extend(results)
            analyze_category_results(category, results)
    
    else:
        print("无效选择")
        return
    
    # 总体分析
    if all_results:
        print(f"\n{'='*60}")
        print("总体实验结果分析")
        print(f"{'='*60}")
        
        print(f"总实验数: {len(all_results)}")
        
        # 按类别统计
        categories = {}
        for result in all_results:
            cat = result.get('category', 'Unknown')
            if cat not in categories:
                categories[cat] = []
            categories[cat].append(result)
        
        print(f"\n按类别统计:")
        for cat, results in categories.items():
            avg_mae = sum(r['mae'] for r in results) / len(results)
            avg_r2 = sum(r['r2'] for r in results) / len(results)
            print(f"  {cat}: {len(results)} 个实验, 平均MAE: {avg_mae:.4f}, 平均R²: {avg_r2:.4f}")
        
        # 全局最佳
        global_best_mae = min(all_results, key=lambda x: x['mae'])
        global_best_r2 = max(all_results, key=lambda x: x['r2'])
        
        print(f"\n全局最佳结果:")
        print(f"最佳 MAE: {global_best_mae['config_name']} (MAE: {global_best_mae['mae']:.4f})")
        print(f"最佳 R²: {global_best_r2['config_name']} (R²: {global_best_r2['r2']:.4f})")
        
        # 保存详细结果
        results_df = pd.DataFrame([
            {
                'Category': r.get('category', 'Unknown'),
                'Config_Name': r['config_name'],
                'Training_ID': r['training_id'],
                'MAE': r['mae'],
                'RMSE': r['rmse'],
                'NSE': r['nse'],
                'R2': r['r2'],
                'Training_Time': r['config_time']
            }
            for r in all_results
        ])
        
        results_file = f"advanced_experiment_results_{int(time.time())}.csv"
        results_df.to_csv(results_file, index=False)
        print(f"\n详细结果已保存到: {results_file}")

def main():
    """主函数"""
    run_comprehensive_experiments()

if __name__ == "__main__":
    main()
