"""
测试全数据加载
==============

验证数据集是否正确使用全部24,836天的数据
"""

from my_parameters import get_my_parameters
from compatible_training_runner import run_compatible_training

def test_data_loading():
    """测试不同的数据加载配置"""
    
    configs = get_my_parameters()
    
    print("🧪 测试数据加载配置")
    print("="*60)
    
    # 测试配置1: 限制样本数量
    print("1. 测试限制样本数量的配置:")
    config1 = configs[0]  # 快速验证配置
    print(f"   配置: {config1['name']}")
    print(f"   max_samples: {config1.get('max_samples', '无限制')}")
    print(f"   n_steps: {config1['n_steps']}, n_pred_steps: {config1['n_pred_steps']}")
    
    # 计算理论样本数
    total_days = 24836
    total_sample_len = config1['n_steps'] + config1['n_pred_steps']
    max_possible_samples = total_days - total_sample_len + 1
    expected_samples = min(max_possible_samples, config1.get('max_samples', max_possible_samples))
    
    print(f"   理论最大样本数: {max_possible_samples}")
    print(f"   预期使用样本数: {expected_samples}")
    print()
    
    # 测试配置2: 全数据配置
    print("2. 测试全数据配置:")
    config2 = configs[-1]  # 全数据训练配置
    print(f"   配置: {config2['name']}")
    print(f"   max_samples: {config2.get('max_samples', '无限制')}")
    print(f"   n_steps: {config2['n_steps']}, n_pred_steps: {config2['n_pred_steps']}")
    
    total_sample_len2 = config2['n_steps'] + config2['n_pred_steps']
    max_possible_samples2 = total_days - total_sample_len2 + 1
    expected_samples2 = config2.get('max_samples', max_possible_samples2)
    
    print(f"   理论最大样本数: {max_possible_samples2}")
    print(f"   预期使用样本数: {expected_samples2}")
    print()
    
    print("💡 建议:")
    print("- 使用配置1进行快速测试")
    print("- 使用配置7进行全数据训练")
    print(f"- 全数据训练将使用约 {max_possible_samples2:,} 个样本")
    print(f"- 预估内存需求: ~{max_possible_samples2 * total_sample_len2 * 4 / 1024 / 1024:.1f} MB")

def test_quick_run():
    """快速测试数据加载"""
    configs = get_my_parameters()
    
    # 创建一个超快速测试配置
    test_config = configs[0].copy()
    test_config.update({
        'name': '数据加载测试',
        'n_steps': 24,
        'n_pred_steps': 6,
        'max_samples': 100,  # 只用100个样本测试
        'epochs': 1,         # 只训练1轮
        'batch_size': 16
    })
    
    print(f"\n🚀 快速测试数据加载...")
    print(f"配置: {test_config['name']}")
    print(f"预期样本数: {test_config['max_samples']}")
    print()
    
    result = run_compatible_training(test_config)
    
    if result:
        print(f"\n✅ 数据加载测试成功!")
        print(f"训练ID: {result['training_id']}")
    else:
        print(f"\n❌ 数据加载测试失败!")

if __name__ == "__main__":
    test_data_loading()
    
    # 询问是否运行快速测试
    choice = input("\n是否运行快速数据加载测试? (y/n): ").lower()
    if choice == 'y':
        test_quick_run()
