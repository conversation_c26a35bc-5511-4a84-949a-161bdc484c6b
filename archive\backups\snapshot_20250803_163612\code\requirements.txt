# TimeMixer++ PyPOTS Integration Requirements
# ==========================================

# Core dependencies
pypots>=0.6.0
torch>=1.10.0
numpy>=1.20.0
pandas>=1.3.0

# Optional dependencies for enhanced functionality
matplotlib>=3.5.0
scikit-learn>=1.0.0
scipy>=1.7.0
seaborn>=0.11.0

# Development and testing dependencies (optional)
jupyter>=1.0.0
notebook>=6.4.0
pytest>=6.0.0
pytest-cov>=2.12.0

# Data handling and visualization (optional)
plotly>=5.0.0
h5py>=3.1.0
openpyxl>=3.0.0

# Performance optimization (optional)
numba>=0.56.0
