"""
Simple Installation Test for TimeMixer++ PyPOTS Integration
===========================================================

This script provides a simplified test to check if the core functionality works.
"""

import sys
import os

# Set environment variables to avoid threadpoolctl issues
os.environ["OMP_NUM_THREADS"] = "1"
os.environ["MKL_NUM_THREADS"] = "1" 
os.environ["NUMEXPR_NUM_THREADS"] = "1"
os.environ["OPENBLAS_NUM_THREADS"] = "1"

def test_basic_imports():
    """Test basic imports without triggering sklearn issues."""
    print("Testing basic imports...")
    
    try:
        import numpy as np
        print("✓ NumPy imported successfully")
    except Exception as e:
        print(f"✗ NumPy import failed: {e}")
        return False
        
    try:
        import pandas as pd
        print("✓ Pandas imported successfully")
    except Exception as e:
        print(f"✗ Pandas import failed: {e}")
        return False
        
    try:
        import torch
        print("✓ PyTorch imported successfully")
        if torch.cuda.is_available():
            print(f"✓ CUDA available with {torch.cuda.device_count()} device(s)")
        else:
            print("⚠ CUDA not available, will use CPU")
    except Exception as e:
        print(f"✗ PyTorch import failed: {e}")
        return False
        
    return True

def test_configuration():
    """Test the TimeMixer++ configuration."""
    print("\nTesting TimeMixer++ configuration...")
    
    try:
        from timemixer_plus_plus_config import (
            TimeMixerPPConfig, 
            TimeMixerPPModelFactory, 
            get_default_configs
        )
        
        # Test configuration creation
        config = TimeMixerPPConfig()
        configs = get_default_configs()
        factory = TimeMixerPPModelFactory()
        
        print("✓ TimeMixer++ configuration working correctly")
        print(f"✓ Available configurations: {list(configs.keys())}")
        return True
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False

def test_pypots_basic():
    """Test basic PyPOTS functionality without importing problematic modules."""
    print("\nTesting PyPOTS basic functionality...")
    
    try:
        # Try to import pypots core without triggering sklearn issues
        import pypots
        print("✓ PyPOTS core imported successfully")
        
        # Test if we can access version info
        if hasattr(pypots, '__version__'):
            print(f"✓ PyPOTS version: {pypots.__version__}")
        
        return True
        
    except Exception as e:
        if "threadpoolctl" in str(e) or "split" in str(e):
            print("⚠ PyPOTS imported with threadpoolctl warnings (this is expected)")
            return True
        else:
            print(f"✗ PyPOTS import failed: {e}")
            return False

def run_simple_demo():
    """Run a simple demonstration."""
    print("\n" + "="*50)
    print("Simple Configuration Demo")
    print("="*50)
    
    try:
        from timemixer_plus_plus_config import get_default_configs, create_custom_config
        
        # Show default configurations
        configs = get_default_configs()
        print(f"\nAvailable configurations: {list(configs.keys())}")
        
        # Show small configuration details
        small_config = configs['small']
        print(f"\nSmall configuration details:")
        print(f"  - n_steps: {small_config.n_steps}")
        print(f"  - n_features: {small_config.n_features}")
        print(f"  - d_model: {small_config.d_model}")
        print(f"  - n_layers: {small_config.n_layers}")
        print(f"  - epochs: {small_config.epochs}")
        
        # Create custom configuration
        custom_config = create_custom_config(
            n_steps=60,
            n_features=5,
            d_model=128,
            epochs=10
        )
        print(f"\nCustom configuration created:")
        print(f"  - n_steps: {custom_config.n_steps}")
        print(f"  - n_features: {custom_config.n_features}")
        print(f"  - d_model: {custom_config.d_model}")
        print(f"  - epochs: {custom_config.epochs}")
        
        print("\n✓ Configuration demo completed successfully!")
        return True
        
    except Exception as e:
        print(f"\n✗ Configuration demo failed: {e}")
        return False

def main():
    """Main function."""
    print("TimeMixer++ Simple Installation Test")
    print("="*50)
    print(f"Python version: {sys.version}")
    print(f"Python executable: {sys.executable}")
    print()
    
    # Run tests
    basic_ok = test_basic_imports()
    config_ok = test_configuration()
    pypots_ok = test_pypots_basic()
    
    # Summary
    print("\n" + "="*50)
    print("Test Summary:")
    print(f"Basic imports: {'✓ PASS' if basic_ok else '✗ FAIL'}")
    print(f"Configuration: {'✓ PASS' if config_ok else '✗ FAIL'}")
    print(f"PyPOTS basic: {'✓ PASS' if pypots_ok else '✗ FAIL'}")
    
    all_passed = basic_ok and config_ok and pypots_ok
    
    if all_passed:
        print("\n✓ Core functionality appears to be working!")
        print("\nNext steps:")
        print("1. Run: python timemixer_plus_plus_example.py")
        print("2. Try the configuration demo below...")
        
        # Run demo
        run_simple_demo()
    else:
        print("\n✗ Some tests failed. Check the errors above.")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
