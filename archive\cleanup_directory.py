"""
目录清理脚本
============

整理项目目录，保留核心训练文件和结果文件
"""

import os
import shutil
from datetime import datetime

def cleanup_directory():
    """清理目录，保留核心文件"""
    print("🧹 开始目录清理")
    print("="*40)
    
    # 要保留的核心文件
    keep_files = {
        # 核心训练脚本
        'enhanced_compatible_training_runner.py',
        'my_parameters.py',
        'show_configs.py',
        
        # 数据文件
        '1971-2017all_cleaned_CHANGBEI_daily_weather_data.csv',
        'enhanced_hydrological_features.csv',
        'enhanced_training_data.csv',
        
        # 结果文件
        'timemixer_evaluation_results.csv',
        'enhanced_training_info.json',
        'hydrological_enhancement_summary.json',
        
        # 说明文档
        'README.md',
        'PSO_优化说明.md',
        'requirements.txt'
    }
    
    # 要保留的目录
    keep_dirs = {
        'results',
        'docs',
        '__pycache__'  # Python缓存，自动生成
    }
    
    # 要移动到archive的文件（不删除，只是归档）
    archive_files = []
    
    # 扫描当前目录
    current_files = []
    current_dirs = []
    
    for item in os.listdir('.'):
        if os.path.isfile(item):
            current_files.append(item)
        elif os.path.isdir(item):
            current_dirs.append(item)
    
    print(f"📊 当前状态:")
    print(f"  文件数量: {len(current_files)}")
    print(f"  目录数量: {len(current_dirs)}")
    
    # 创建archive目录（如果不存在）
    if not os.path.exists('archive'):
        os.makedirs('archive')
    
    # 处理文件
    moved_files = 0
    for file in current_files:
        if file not in keep_files:
            # 移动到archive
            try:
                shutil.move(file, f'archive/{file}')
                archive_files.append(file)
                moved_files += 1
            except Exception as e:
                print(f"⚠️ 移动文件失败 {file}: {e}")
    
    # 处理目录
    moved_dirs = 0
    for dir_name in current_dirs:
        if dir_name not in keep_dirs and dir_name != 'archive':
            # 移动到archive
            try:
                if os.path.exists(f'archive/{dir_name}'):
                    # 如果archive中已存在，合并
                    shutil.copytree(dir_name, f'archive/{dir_name}_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}')
                else:
                    shutil.move(dir_name, f'archive/{dir_name}')
                moved_dirs += 1
            except Exception as e:
                print(f"⚠️ 移动目录失败 {dir_name}: {e}")
    
    print(f"\n✅ 清理完成:")
    print(f"  移动文件: {moved_files} 个")
    print(f"  移动目录: {moved_dirs} 个")
    print(f"  保留核心文件: {len(keep_files)} 个")
    
    # 显示保留的文件
    print(f"\n📋 保留的核心文件:")
    for file in sorted(keep_files):
        if os.path.exists(file):
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file} (不存在)")
    
    return moved_files, moved_dirs

def create_clean_structure():
    """创建清洁的目录结构"""
    print(f"\n📁 创建清洁目录结构")
    print("="*30)
    
    # 创建必要的目录
    dirs_to_create = [
        'results',
        'docs',
        'configs'
    ]
    
    for dir_name in dirs_to_create:
        if not os.path.exists(dir_name):
            os.makedirs(dir_name)
            print(f"  📁 创建目录: {dir_name}")
        else:
            print(f"  ✅ 目录已存在: {dir_name}")

def main():
    """主函数"""
    print("🧹 TimeMixer项目目录清理")
    print("="*50)
    
    print("💡 清理策略:")
    print("  • 保留核心训练脚本和数据文件")
    print("  • 保留重要结果和配置文件")
    print("  • 将其他文件移动到archive目录")
    print("  • 不删除任何文件，只是重新组织")
    
    # 确认清理
    confirm = input("\n是否开始清理？(y/n): ").strip().lower()
    if confirm != 'y':
        print("❌ 取消清理")
        return
    
    # 执行清理
    moved_files, moved_dirs = cleanup_directory()
    
    # 创建清洁结构
    create_clean_structure()
    
    print(f"\n🎉 目录清理完成!")
    print(f"📊 清理统计:")
    print(f"  • 移动文件: {moved_files} 个")
    print(f"  • 移动目录: {moved_dirs} 个")
    print(f"  • 所有文件都保存在archive中，可随时恢复")
    
    print(f"\n📋 当前目录结构:")
    for item in sorted(os.listdir('.')):
        if os.path.isfile(item):
            print(f"  📄 {item}")
        else:
            print(f"  📁 {item}/")

if __name__ == "__main__":
    main()
