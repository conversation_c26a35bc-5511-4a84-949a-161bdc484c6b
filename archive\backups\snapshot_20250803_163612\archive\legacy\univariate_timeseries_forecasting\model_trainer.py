"""
Univariate TimeMixer++ Model Trainer
====================================

This module handles training and evaluation of TimeMixer++ models
for univariate time series forecasting.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import time
from typing import Dict, Tuple, Optional, List
from pathlib import Path
import warnings

from data_loader import UnivariateTimeSeriesLoader
from model_config import UnivariateTimeMixerConfig, UnivariateModelFactory

warnings.filterwarnings('ignore')


class UnivariateModelTrainer:
    """
    Trainer class for univariate time series forecasting models.
    """
    
    def __init__(self, config: UnivariateTimeMixerConfig):
        """
        Initialize the trainer.
        
        Args:
            config: Model configuration
        """
        self.config = config
        self.model = None
        self.data_loader = None
        self.training_history = {}
        self.scaler_params = None
        
    def prepare_data(self, 
                    csv_file_path: str,
                    date_column: str = 'DATA',
                    target_column: str = 'runoff',
                    date_format: str = '%Y/%m/%d',
                    normalization: str = 'minmax',
                    train_ratio: float = 0.7,
                    val_ratio: float = 0.2) -> Tuple[Dict, Dict, Dict]:
        """
        Prepare data for training.
        
        Args:
            csv_file_path: Path to CSV file
            date_column: Name of date column
            target_column: Name of target column
            date_format: Date format string
            normalization: Normalization method ('minmax' or 'zscore')
            train_ratio: Training data ratio
            val_ratio: Validation data ratio
            
        Returns:
            Tuple of (train_data, val_data, test_data)
        """
        print("Preparing data for training...")
        
        # Initialize data loader
        self.data_loader = UnivariateTimeSeriesLoader(
            date_column=date_column,
            target_column=target_column,
            date_format=date_format
        )
        
        # Load and process data
        raw_data = self.data_loader.load_csv(csv_file_path)
        missing_info = self.data_loader.check_missing_values()
        
        # Handle missing values if any
        if missing_info['missing_values'] > 0:
            print(f"⚠ Found {missing_info['missing_values']} missing values")
            # Simple forward fill for missing values
            self.data_loader.data[target_column] = self.data_loader.data[target_column].fillna(method='ffill')
            print("✓ Missing values filled using forward fill")
        
        # Normalize data
        normalized_data = self.data_loader.normalize_data(normalization)
        self.scaler_params = self.data_loader.scaler_params
        
        # Create sequences
        X, y = self.data_loader.create_sequences(
            normalized_data, 
            sequence_length=self.config.n_steps,
            prediction_length=self.config.n_pred_steps
        )
        
        # Split data
        train_data, val_data, test_data = self.data_loader.split_data(
            X, y, train_ratio=train_ratio, val_ratio=val_ratio
        )
        
        print("✓ Data preparation completed")
        return train_data, val_data, test_data
    
    def create_model(self) -> None:
        """Create the TimeMixer++ model."""
        print("Creating TimeMixer++ model...")
        
        try:
            self.model = UnivariateModelFactory.create_forecasting_model(self.config)
            print("✓ Model created successfully")
            
            # Print model configuration
            print(f"Model Configuration:")
            print(f"  - Input sequence length: {self.config.n_steps}")
            print(f"  - Prediction horizon: {self.config.n_pred_steps}")
            print(f"  - Model dimension: {self.config.d_model}")
            print(f"  - Number of layers: {self.config.n_layers}")
            
        except Exception as e:
            raise RuntimeError(f"Failed to create model: {e}")
    
    def train_model(self, 
                   train_data: Dict, 
                   val_data: Dict,
                   save_model: bool = True,
                   model_save_path: str = "trained_model.pypots") -> Dict:
        """
        Train the model.
        
        Args:
            train_data: Training data dictionary
            val_data: Validation data dictionary
            save_model: Whether to save the trained model
            model_save_path: Path to save the model
            
        Returns:
            Training history dictionary
        """
        if self.model is None:
            raise ValueError("Model not created. Call create_model() first.")
        
        print("Starting model training...")
        start_time = time.time()
        
        try:
            # Prepare data for PyPOTS format
            train_pypots = {'X': train_data['X']}
            val_pypots = {'X': val_data['X']}
            
            # Train the model
            self.model.fit(train_pypots, val_pypots)
            
            training_time = time.time() - start_time
            print(f"✓ Training completed in {training_time:.2f} seconds")
            
            # Save model if requested
            if save_model:
                self.model.save(model_save_path)
                print(f"✓ Model saved to {model_save_path}")
            
            # Store training history
            self.training_history = {
                'training_time': training_time,
                'config': self.config.to_dict(),
                'data_info': {
                    'train_samples': train_data['X'].shape[0],
                    'val_samples': val_data['X'].shape[0],
                    'sequence_length': self.config.n_steps,
                    'prediction_length': self.config.n_pred_steps
                }
            }
            
            return self.training_history
            
        except Exception as e:
            raise RuntimeError(f"Training failed: {e}")
    
    def evaluate_model(self, test_data: Dict) -> Dict:
        """
        Evaluate the trained model.
        
        Args:
            test_data: Test data dictionary
            
        Returns:
            Evaluation metrics dictionary
        """
        if self.model is None:
            raise ValueError("Model not trained. Call train_model() first.")
        
        print("Evaluating model...")
        
        try:
            # Prepare test data for PyPOTS format
            test_pypots = {'X': test_data['X']}
            
            # Make predictions
            results = self.model.predict(test_pypots)
            predictions = results['forecasting']
            
            # Get actual values
            actual = test_data['y']
            
            # Calculate metrics
            metrics = self._calculate_metrics(actual, predictions)
            
            print("✓ Model evaluation completed")
            print(f"Evaluation Metrics:")
            for metric_name, value in metrics.items():
                if isinstance(value, float):
                    print(f"  - {metric_name}: {value:.4f}")
            
            return {
                'metrics': metrics,
                'predictions': predictions,
                'actual': actual
            }
            
        except Exception as e:
            raise RuntimeError(f"Evaluation failed: {e}")
    
    def _calculate_metrics(self, actual: np.ndarray, predicted: np.ndarray) -> Dict:
        """
        Calculate evaluation metrics.
        
        Args:
            actual: Actual values
            predicted: Predicted values
            
        Returns:
            Dictionary of metrics
        """
        # Ensure same shape
        min_samples = min(actual.shape[0], predicted.shape[0])
        actual = actual[:min_samples]
        predicted = predicted[:min_samples]
        
        # Flatten for metric calculation
        actual_flat = actual.flatten()
        predicted_flat = predicted.flatten()
        
        # Calculate metrics
        mse = np.mean((actual_flat - predicted_flat) ** 2)
        rmse = np.sqrt(mse)
        mae = np.mean(np.abs(actual_flat - predicted_flat))
        
        # Calculate MAPE (avoid division by zero)
        mask = actual_flat != 0
        mape = np.mean(np.abs((actual_flat[mask] - predicted_flat[mask]) / actual_flat[mask])) * 100
        
        # Calculate R²
        ss_res = np.sum((actual_flat - predicted_flat) ** 2)
        ss_tot = np.sum((actual_flat - np.mean(actual_flat)) ** 2)
        r2 = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0
        
        return {
            'MSE': mse,
            'RMSE': rmse,
            'MAE': mae,
            'MAPE': mape,
            'R2': r2
        }
    
    def plot_predictions(self, 
                        evaluation_results: Dict,
                        num_samples: int = 5,
                        save_path: Optional[str] = None) -> None:
        """
        Plot prediction results.
        
        Args:
            evaluation_results: Results from evaluate_model()
            num_samples: Number of samples to plot
            save_path: Path to save the plot
        """
        predictions = evaluation_results['predictions']
        actual = evaluation_results['actual']
        
        # Denormalize data for plotting
        if self.scaler_params is not None:
            predictions_denorm = self.data_loader.denormalize_data(predictions.reshape(-1, 1)).reshape(predictions.shape)
            actual_denorm = self.data_loader.denormalize_data(actual.reshape(-1, 1)).reshape(actual.shape)
        else:
            predictions_denorm = predictions
            actual_denorm = actual
        
        # Create subplots
        fig, axes = plt.subplots(num_samples, 1, figsize=(12, 3*num_samples))
        if num_samples == 1:
            axes = [axes]
        
        for i in range(min(num_samples, len(predictions_denorm))):
            ax = axes[i]
            
            # Plot actual vs predicted
            time_steps = range(len(actual_denorm[i]))
            ax.plot(time_steps, actual_denorm[i].flatten(), 'b-', label='Actual', linewidth=2)
            ax.plot(time_steps, predictions_denorm[i].flatten(), 'r--', label='Predicted', linewidth=2)
            
            ax.set_title(f'Sample {i+1}: Actual vs Predicted')
            ax.set_xlabel('Time Steps')
            ax.set_ylabel('Value')
            ax.legend()
            ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"✓ Plot saved to {save_path}")
        
        plt.show()
    
    def save_results(self, 
                    evaluation_results: Dict,
                    save_dir: str = "results") -> None:
        """
        Save training and evaluation results.
        
        Args:
            evaluation_results: Results from evaluate_model()
            save_dir: Directory to save results
        """
        save_path = Path(save_dir)
        save_path.mkdir(exist_ok=True)
        
        # Save metrics
        metrics_df = pd.DataFrame([evaluation_results['metrics']])
        metrics_df.to_csv(save_path / "evaluation_metrics.csv", index=False)
        
        # Save training history
        if self.training_history:
            history_df = pd.DataFrame([self.training_history['config']])
            history_df.to_csv(save_path / "training_config.csv", index=False)
        
        # Save predictions (sample)
        predictions = evaluation_results['predictions']
        actual = evaluation_results['actual']
        
        # Denormalize for saving
        if self.scaler_params is not None:
            predictions_denorm = self.data_loader.denormalize_data(predictions.reshape(-1, 1)).reshape(predictions.shape)
            actual_denorm = self.data_loader.denormalize_data(actual.reshape(-1, 1)).reshape(actual.shape)
        else:
            predictions_denorm = predictions
            actual_denorm = actual
        
        # Save first few samples
        sample_results = []
        for i in range(min(10, len(predictions_denorm))):
            for j in range(len(predictions_denorm[i])):
                sample_results.append({
                    'sample': i,
                    'time_step': j,
                    'actual': actual_denorm[i][j][0],
                    'predicted': predictions_denorm[i][j][0]
                })
        
        results_df = pd.DataFrame(sample_results)
        results_df.to_csv(save_path / "sample_predictions.csv", index=False)
        
        print(f"✓ Results saved to {save_dir}/")


def run_complete_training_pipeline(csv_file_path: str,
                                 config_name: str = 'medium_term',
                                 custom_config: Optional[Dict] = None) -> Dict:
    """
    Run the complete training pipeline.
    
    Args:
        csv_file_path: Path to CSV data file
        config_name: Name of predefined configuration
        custom_config: Custom configuration parameters
        
    Returns:
        Complete results dictionary
    """
    print("Starting Complete Training Pipeline")
    print("=" * 50)
    
    # Import here to avoid circular imports
    from model_config import get_univariate_configs, create_custom_univariate_config
    
    # Get configuration
    if custom_config:
        config = create_custom_univariate_config(**custom_config)
        print(f"✓ Using custom configuration")
    else:
        configs = get_univariate_configs()
        config = configs[config_name]
        print(f"✓ Using '{config_name}' configuration")
    
    # Initialize trainer
    trainer = UnivariateModelTrainer(config)
    
    # Prepare data
    train_data, val_data, test_data = trainer.prepare_data(csv_file_path)
    
    # Create and train model
    trainer.create_model()
    training_history = trainer.train_model(train_data, val_data)
    
    # Evaluate model
    evaluation_results = trainer.evaluate_model(test_data)
    
    # Plot results
    trainer.plot_predictions(evaluation_results, num_samples=3)
    
    # Save results
    trainer.save_results(evaluation_results)
    
    print("\n" + "=" * 50)
    print("✓ Complete training pipeline finished successfully!")
    
    return {
        'trainer': trainer,
        'training_history': training_history,
        'evaluation_results': evaluation_results,
        'config': config
    }


if __name__ == "__main__":
    # This will be used for testing
    print("Model Trainer Module - Ready for use!")
    print("Use run_complete_training_pipeline() to train a model.")
