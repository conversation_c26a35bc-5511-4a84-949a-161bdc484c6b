"""
演示多个训练配置
================

运行几个不同的训练配置来演示自动化训练系统。
"""

from single_training_runner import run_timemixer_training
import time
import pandas as pd

def run_demo_trainings():
    """运行演示训练"""
    
    print("TimeMixer++ 多配置演示训练")
    print("="*60)
    
    # 定义3个不同的配置进行演示
    demo_configs = [
        {
            'name': '小模型快速训练',
            'params': {
                'n_steps': 24,
                'n_pred_steps': 6,
                'n_layers': 1,
                'd_model': 32,
                'd_ffn': 64,
                'epochs': 3,
                'learning_rate': 1e-3,
                'dropout': 0.1
            }
        },
        {
            'name': '中等模型标准训练',
            'params': {
                'n_steps': 48,
                'n_pred_steps': 12,
                'n_layers': 2,
                'd_model': 64,
                'd_ffn': 128,
                'epochs': 5,
                'learning_rate': 8e-4,
                'dropout': 0.15
            }
        },
        {
            'name': '大模型深度训练',
            'params': {
                'n_steps': 72,
                'n_pred_steps': 18,
                'n_layers': 3,
                'd_model': 96,
                'd_ffn': 192,
                'epochs': 8,
                'learning_rate': 5e-4,
                'dropout': 0.2
            }
        }
    ]
    
    results = []
    
    for i, config in enumerate(demo_configs, 1):
        print(f"\n{'='*60}")
        print(f"演示训练 {i}/3: {config['name']}")
        print(f"{'='*60}")
        
        print("配置参数:")
        for key, value in config['params'].items():
            print(f"  {key}: {value}")
        
        print(f"\n开始训练...")
        start_time = time.time()
        
        try:
            result = run_timemixer_training(config['params'])
            end_time = time.time()
            training_time = end_time - start_time
            
            if result:
                result['config_name'] = config['name']
                result['training_time'] = training_time
                results.append(result)
                
                print(f"\n✓ {config['name']} 训练成功完成!")
                print(f"训练时间: {training_time:.1f} 秒")
                print(f"结果预览:")
                print(f"  MAE: {result['mae']:.4f}")
                print(f"  RMSE: {result['rmse']:.4f}")
                print(f"  NSE: {result['nse']:.4f}")
                print(f"  R²: {result['r2']:.4f}")
            else:
                print(f"\n✗ {config['name']} 训练失败")
                
        except Exception as e:
            print(f"\n✗ {config['name']} 训练出现异常: {e}")
        
        # 训练间隔
        if i < len(demo_configs):
            print(f"\n等待 5 秒后开始下一个训练...")
            time.sleep(5)
    
    return results

def analyze_results(results):
    """分析训练结果"""
    
    if not results:
        print("没有成功的训练结果可供分析")
        return
    
    print(f"\n{'='*60}")
    print("训练结果分析")
    print(f"{'='*60}")
    
    # 创建结果表格
    print(f"\n结果汇总:")
    print(f"{'配置名称':<20} {'训练ID':<12} {'MAE':<10} {'RMSE':<10} {'NSE':<8} {'R²':<8} {'时间(s)':<8}")
    print("-" * 85)
    
    for result in results:
        print(f"{result['config_name']:<20} {result['training_id']:<12} "
              f"{result['mae']:<10.4f} {result['rmse']:<10.4f} "
              f"{result['nse']:<8.4f} {result['r2']:<8.4f} {result['training_time']:<8.1f}")
    
    # 找出最佳结果
    best_mae = min(results, key=lambda x: x['mae'])
    best_r2 = max(results, key=lambda x: x['r2'])
    best_nse = max(results, key=lambda x: x['nse'])
    fastest = min(results, key=lambda x: x['training_time'])
    
    print(f"\n最佳结果:")
    print(f"最低 MAE: {best_mae['config_name']} ({best_mae['training_id']}) - MAE: {best_mae['mae']:.4f}")
    print(f"最高 R²:  {best_r2['config_name']} ({best_r2['training_id']}) - R²: {best_r2['r2']:.4f}")
    print(f"最高 NSE: {best_nse['config_name']} ({best_nse['training_id']}) - NSE: {best_nse['nse']:.4f}")
    print(f"最快训练: {fastest['config_name']} ({fastest['training_id']}) - 时间: {fastest['training_time']:.1f}s")
    
    # 性能vs效率分析
    print(f"\n性能 vs 效率分析:")
    for result in results:
        efficiency_score = result['r2'] / (result['training_time'] / 60)  # R²每分钟
        print(f"{result['config_name']:<20}: 效率分数 = {efficiency_score:.4f} (R²/分钟)")

def view_csv_results():
    """查看CSV文件中的所有结果"""
    
    try:
        df = pd.read_csv('timemixer_evaluation_results.csv')
        
        print(f"\n{'='*60}")
        print("CSV文件中的所有训练结果")
        print(f"{'='*60}")
        
        print(f"\n总训练次数: {len(df)}")
        
        if len(df) > 0:
            print(f"\n最新5次训练结果:")
            latest_results = df.tail(5)
            
            for _, row in latest_results.iterrows():
                print(f"\n{row['Training_ID']}:")
                print(f"  时间: {row['Timestamp']}")
                print(f"  MAE: {row['MAE']:.4f}")
                print(f"  RMSE: {row['RMSE']:.4f}")
                print(f"  NSE: {row['NSE']:.4f}")
                print(f"  R²: {row['R2']:.4f}")
                
                # 显示主要参数
                params = row['Parameters']
                if 'n_layers' in params:
                    n_layers = params.split('n_layers=')[1].split('|')[0]
                    d_model = params.split('d_model=')[1].split('|')[0]
                    epochs = params.split('epochs=')[1].split('|')[0]
                    print(f"  主要参数: layers={n_layers}, d_model={d_model}, epochs={epochs}")
            
            # 统计信息
            print(f"\n统计信息:")
            print(f"  平均 MAE: {df['MAE'].mean():.4f}")
            print(f"  最佳 MAE: {df['MAE'].min():.4f} ({df.loc[df['MAE'].idxmin(), 'Training_ID']})")
            print(f"  平均 R²: {df['R2'].mean():.4f}")
            print(f"  最佳 R²: {df['R2'].max():.4f} ({df.loc[df['R2'].idxmax(), 'Training_ID']})")
        
    except FileNotFoundError:
        print("CSV结果文件不存在")
    except Exception as e:
        print(f"读取CSV文件时出错: {e}")

def main():
    """主函数"""
    
    print("TimeMixer++ 自动化训练系统演示")
    print("="*60)
    print("这个演示将运行3个不同配置的训练，展示系统功能")
    
    # 运行演示训练
    results = run_demo_trainings()
    
    # 分析结果
    analyze_results(results)
    
    # 查看CSV中的所有结果
    view_csv_results()
    
    print(f"\n{'='*60}")
    print("演示完成!")
    print(f"{'='*60}")
    
    if results:
        print(f"✓ 成功完成 {len(results)} 个训练配置")
        print(f"✓ 所有结果已保存到 timemixer_evaluation_results.csv")
        print(f"✓ 模型文件已保存到对应的训练文件夹")
        
        print(f"\n您现在可以:")
        print(f"1. 查看 timemixer_evaluation_results.csv 了解详细结果")
        print(f"2. 运行 python batch_training_runner.py 进行更多实验")
        print(f"3. 修改参数配置进行自定义训练")
        print(f"4. 使用最佳配置进行生产训练")
    else:
        print(f"✗ 没有成功的训练，请检查系统配置")

if __name__ == "__main__":
    main()
