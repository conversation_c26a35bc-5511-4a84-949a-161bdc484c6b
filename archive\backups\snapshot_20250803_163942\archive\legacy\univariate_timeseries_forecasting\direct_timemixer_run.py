"""
Direct TimeMixer++ Run
=====================

This script attempts to directly run TimeMixer++ with fallback options.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

def load_data():
    """Load the runoff data."""
    print("🌊 Loading Runoff Data")
    print("=" * 30)
    
    file_path = r'C:\Users\<USER>\Desktop\timemix\1964-2017dailyRunoff.csv'
    
    try:
        data = pd.read_excel(file_path)
        data['DATA'] = pd.to_datetime(data['DATA'])
        data = data.sort_values('DATA').reset_index(drop=True)
        
        print(f"✅ Data loaded: {data.shape}")
        print(f"📅 Range: {data['DATA'].min().date()} to {data['DATA'].max().date()}")
        print(f"💧 Runoff: {data['runoff'].min():.1f} - {data['runoff'].max():.1f}")
        
        return data
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def prepare_data(data, n_steps=30, n_pred_steps=7):
    """Prepare data for TimeMixer++."""
    print(f"\n🔧 Preparing Data ({n_steps}→{n_pred_steps})")
    
    values = data['runoff'].values
    mean_val, std_val = np.mean(values), np.std(values)
    normalized = (values - mean_val) / std_val
    
    # Create sequences
    X = []
    for i in range(len(normalized) - n_steps - n_pred_steps + 1):
        X.append(normalized[i:(i + n_steps)])
    
    X = np.array(X).reshape(-1, n_steps, 1)
    
    # Split data
    n_train = int(0.7 * len(X))
    n_val = int(0.2 * len(X))
    
    train_X = X[:n_train]
    val_X = X[n_train:n_train+n_val]
    test_X = X[n_train+n_val:]
    
    print(f"✅ Prepared: Train={len(train_X)}, Val={len(val_X)}, Test={len(test_X)}")
    
    return train_X, val_X, test_X, mean_val, std_val

def try_timemixer_plus_plus(train_X, val_X, test_X, n_steps, n_pred_steps):
    """Try to use TimeMixer++ with multiple fallback options."""
    print(f"\n🤖 Attempting TimeMixer++ Import")
    
    # Try different import methods
    model = None
    model_name = None
    
    # Method 1: Try TimeMixer++
    try:
        from pypots.forecasting.timemixerpp import TimeMixerPP
        print("✅ Found TimeMixer++")
        
        model = TimeMixerPP(
            n_steps=n_steps,
            n_features=1,
            n_pred_steps=n_pred_steps,
            n_pred_features=1,
            n_layers=2,
            d_model=128,
            d_ffn=256,
            top_k=3,
            dropout=0.1,
            batch_size=32,
            epochs=20,  # Reduced for testing
            patience=10,
            device='cpu',
            verbose=True
        )
        model_name = "TimeMixer++"
        
    except ImportError:
        print("⚠️ TimeMixer++ not found, trying TimeMixer...")
        
        # Method 2: Try regular TimeMixer
        try:
            from pypots.forecasting.timemixer import TimeMixer
            print("✅ Found TimeMixer")
            
            model = TimeMixer(
                n_steps=n_steps,
                n_features=1,
                n_pred_steps=n_pred_steps,
                n_pred_features=1,
                n_layers=2,
                d_model=128,
                d_ffn=256,
                top_k=3,
                dropout=0.1,
                batch_size=32,
                epochs=20,
                patience=10,
                device='cpu',
                verbose=True
            )
            model_name = "TimeMixer"
            
        except ImportError:
            print("❌ Neither TimeMixer++ nor TimeMixer found")
            return None, None
    
    except Exception as e:
        print(f"❌ Error creating model: {e}")
        return None, None
    
    # Try to train the model
    print(f"\n🚀 Training {model_name}")
    try:
        train_data = {'X': train_X}
        val_data = {'X': val_X}
        
        print("   Starting training...")
        model.fit(train_data, val_data)
        print("   ✅ Training completed!")
        
        # Make predictions
        print("   Making predictions...")
        test_data = {'X': test_X}
        results = model.predict(test_data)
        predictions = results['forecasting']
        
        print(f"   ✅ Predictions completed: {predictions.shape}")
        
        return model, predictions
        
    except Exception as e:
        print(f"   ❌ Training/prediction failed: {e}")
        return None, None

def visualize_results(predictions, mean_val, std_val):
    """Create visualizations."""
    print(f"\n📊 Creating Visualizations")
    
    try:
        # Denormalize predictions
        pred_denorm = predictions * std_val + mean_val
        
        # Plot first 3 samples
        fig, axes = plt.subplots(3, 1, figsize=(12, 10))
        
        for i in range(min(3, len(pred_denorm))):
            ax = axes[i]
            days = range(len(pred_denorm[i]))
            ax.plot(days, pred_denorm[i].flatten(), 'r-', linewidth=2, marker='o')
            ax.set_title(f'Sample {i+1}: Runoff Forecast')
            ax.set_xlabel('Days')
            ax.set_ylabel('Runoff')
            ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('direct_timemixer_results.png', dpi=300, bbox_inches='tight')
        print("✅ Saved: direct_timemixer_results.png")
        plt.show()
        
    except Exception as e:
        print(f"⚠️ Visualization failed: {e}")

def main():
    """Main function."""
    print("🌊 DIRECT TIMEMIXER++ RUN")
    print("=" * 40)
    
    # Load data
    data = load_data()
    if data is None:
        return
    
    # Prepare data
    train_X, val_X, test_X, mean_val, std_val = prepare_data(data)
    
    # Try TimeMixer++
    model, predictions = try_timemixer_plus_plus(
        train_X, val_X, test_X, 
        n_steps=30, n_pred_steps=7
    )
    
    if model is None:
        print("\n❌ TimeMixer++ not available")
        print("\n💡 Solutions:")
        print("1. Create new conda environment:")
        print("   conda create -n pypots python=3.9")
        print("   conda activate pypots")
        print("   pip install torch pandas numpy matplotlib pypots")
        print("\n2. Or try fixing current environment:")
        print("   conda update --all")
        print("   pip install --force-reinstall pypots")
        return
    
    # Visualize results
    if predictions is not None:
        visualize_results(predictions, mean_val, std_val)
        
        # Save model
        try:
            model.save("direct_timemixer_model.pypots")
            print("✅ Model saved: direct_timemixer_model.pypots")
        except:
            print("⚠️ Could not save model")
    
    print(f"\n🎉 TimeMixer++ Run Completed!")

if __name__ == "__main__":
    main()
