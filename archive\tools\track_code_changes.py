"""
代码变更追踪系统
================

自动监控和记录重要代码文件的变更，生成变更日志，
帮助开发者了解代码演进历史。
"""

import os
import json
import hashlib
import datetime
from pathlib import Path
import difflib

class CodeChangeTracker:
    def __init__(self, project_root="."):
        self.project_root = Path(project_root)
        self.tracking_dir = self.project_root / ".code_tracking"
        self.tracking_dir.mkdir(exist_ok=True)
        
        self.snapshots_dir = self.tracking_dir / "snapshots"
        self.snapshots_dir.mkdir(exist_ok=True)
        
        self.changes_log = self.tracking_dir / "changes.json"
        
        # 需要追踪的核心文件
        self.tracked_files = [
            "compatible_training_runner.py",
            "my_parameters.py",
            "run_my_training.py", 
            "run_advanced_training.py",
            "debug_timemixer.py",
            "test_normalization.py",
            "validate_parameters.py"
        ]
    
    def get_file_hash(self, file_path):
        """计算文件的MD5哈希值"""
        try:
            with open(file_path, 'rb') as f:
                return hashlib.md5(f.read()).hexdigest()
        except:
            return None
    
    def get_file_content(self, file_path):
        """读取文件内容"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except:
            return None
    
    def load_changes_log(self):
        """加载变更日志"""
        if self.changes_log.exists():
            try:
                with open(self.changes_log, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                pass
        return {"files": {}, "changes": []}
    
    def save_changes_log(self, log_data):
        """保存变更日志"""
        with open(self.changes_log, 'w', encoding='utf-8') as f:
            json.dump(log_data, f, ensure_ascii=False, indent=2)
    
    def create_file_snapshot(self, file_path, timestamp):
        """创建文件快照"""
        content = self.get_file_content(file_path)
        if content is None:
            return None
        
        snapshot_name = f"{file_path.stem}_{timestamp}.py"
        snapshot_path = self.snapshots_dir / snapshot_name
        
        with open(snapshot_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return snapshot_path
    
    def generate_diff(self, old_content, new_content, file_name):
        """生成文件差异"""
        if old_content is None or new_content is None:
            return None
        
        old_lines = old_content.splitlines(keepends=True)
        new_lines = new_content.splitlines(keepends=True)
        
        diff = list(difflib.unified_diff(
            old_lines, new_lines,
            fromfile=f"{file_name} (旧版本)",
            tofile=f"{file_name} (新版本)",
            lineterm=""
        ))
        
        return ''.join(diff) if diff else None
    
    def scan_for_changes(self):
        """扫描文件变更"""
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        log_data = self.load_changes_log()
        
        changes_detected = []
        
        print("🔍 扫描代码文件变更...")
        print("="*40)
        
        for file_name in self.tracked_files:
            file_path = self.project_root / file_name
            
            if not file_path.exists():
                print(f"⚠ {file_name} - 文件不存在")
                continue
            
            current_hash = self.get_file_hash(file_path)
            current_content = self.get_file_content(file_path)
            
            if current_hash is None:
                print(f"❌ {file_name} - 无法读取文件")
                continue
            
            # 检查是否是新文件或已变更
            file_info = log_data["files"].get(file_name, {})
            last_hash = file_info.get("last_hash")
            
            if last_hash != current_hash:
                # 文件已变更或是新文件
                change_type = "新文件" if last_hash is None else "已修改"
                
                # 创建快照
                snapshot_path = self.create_file_snapshot(file_path, timestamp)
                
                # 生成差异（如果不是新文件）
                diff_content = None
                if last_hash is not None and "last_content" in file_info:
                    diff_content = self.generate_diff(
                        file_info["last_content"], 
                        current_content, 
                        file_name
                    )
                
                # 记录变更
                change_record = {
                    "timestamp": timestamp,
                    "file": file_name,
                    "type": change_type,
                    "old_hash": last_hash,
                    "new_hash": current_hash,
                    "snapshot": str(snapshot_path.name) if snapshot_path else None,
                    "has_diff": diff_content is not None
                }
                
                if diff_content:
                    # 保存差异文件
                    diff_file = self.tracking_dir / f"{file_path.stem}_diff_{timestamp}.txt"
                    with open(diff_file, 'w', encoding='utf-8') as f:
                        f.write(diff_content)
                    change_record["diff_file"] = str(diff_file.name)
                
                changes_detected.append(change_record)
                log_data["changes"].append(change_record)
                
                # 更新文件信息
                log_data["files"][file_name] = {
                    "last_hash": current_hash,
                    "last_content": current_content,
                    "last_modified": timestamp,
                    "size": file_path.stat().st_size
                }
                
                print(f"🔄 {file_name} - {change_type}")
                if diff_content:
                    lines_changed = len([line for line in diff_content.split('\n') 
                                       if line.startswith('+') or line.startswith('-')])
                    print(f"   变更行数: ~{lines_changed}")
            else:
                print(f"✓ {file_name} - 无变更")
        
        # 保存日志
        self.save_changes_log(log_data)
        
        return changes_detected
    
    def show_change_summary(self, changes):
        """显示变更摘要"""
        if not changes:
            print("\n✅ 没有检测到文件变更")
            return
        
        print(f"\n📊 检测到 {len(changes)} 个文件变更:")
        print("="*50)
        
        for change in changes:
            print(f"📁 {change['file']}")
            print(f"   类型: {change['type']}")
            print(f"   时间: {change['timestamp']}")
            if change.get('has_diff'):
                print(f"   差异文件: {change.get('diff_file')}")
            print()
    
    def show_change_history(self, file_name=None, limit=10):
        """显示变更历史"""
        log_data = self.load_changes_log()
        changes = log_data.get("changes", [])
        
        if file_name:
            changes = [c for c in changes if c["file"] == file_name]
            print(f"📜 {file_name} 的变更历史:")
        else:
            print("📜 所有文件的变更历史:")
        
        print("="*60)
        
        # 按时间倒序排列
        changes.sort(key=lambda x: x["timestamp"], reverse=True)
        
        for i, change in enumerate(changes[:limit]):
            print(f"{i+1}. {change['file']} - {change['type']}")
            print(f"   时间: {change['timestamp']}")
            if change.get('diff_file'):
                print(f"   差异: {change['diff_file']}")
            print()
        
        if len(changes) > limit:
            print(f"... 还有 {len(changes) - limit} 条记录")
    
    def view_diff(self, change_timestamp, file_name):
        """查看具体的文件差异"""
        diff_file = self.tracking_dir / f"{Path(file_name).stem}_diff_{change_timestamp}.txt"
        
        if diff_file.exists():
            print(f"📄 {file_name} 在 {change_timestamp} 的变更:")
            print("="*60)
            with open(diff_file, 'r', encoding='utf-8') as f:
                print(f.read())
        else:
            print(f"❌ 差异文件不存在: {diff_file}")

def main():
    """主函数"""
    tracker = CodeChangeTracker()
    
    print("🎯 TimeMixer代码变更追踪系统")
    print("="*50)
    
    while True:
        print("\n选择操作:")
        print("1. 扫描文件变更")
        print("2. 查看变更历史")
        print("3. 查看特定文件历史")
        print("4. 查看具体差异")
        print("5. 退出")
        
        choice = input("请选择 (1-5): ").strip()
        
        if choice == "1":
            changes = tracker.scan_for_changes()
            tracker.show_change_summary(changes)
            
        elif choice == "2":
            limit = input("显示最近几条记录 (默认10): ").strip()
            limit = int(limit) if limit.isdigit() else 10
            tracker.show_change_history(limit=limit)
            
        elif choice == "3":
            print("\n可追踪的文件:")
            for i, file_name in enumerate(tracker.tracked_files, 1):
                print(f"{i}. {file_name}")
            
            try:
                idx = int(input("选择文件编号: ")) - 1
                if 0 <= idx < len(tracker.tracked_files):
                    file_name = tracker.tracked_files[idx]
                    tracker.show_change_history(file_name)
                else:
                    print("无效选择")
            except ValueError:
                print("请输入数字")
                
        elif choice == "4":
            timestamp = input("输入变更时间戳 (格式: YYYYMMDD_HHMMSS): ").strip()
            file_name = input("输入文件名: ").strip()
            if timestamp and file_name:
                tracker.view_diff(timestamp, file_name)
            else:
                print("请输入有效的时间戳和文件名")
                
        elif choice == "5":
            print("再见!")
            break
            
        else:
            print("无效选择，请重试")

if __name__ == "__main__":
    main()
