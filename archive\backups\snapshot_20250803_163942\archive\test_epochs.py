"""
测试epochs参数传递
==================

验证epochs参数是否正确传递到训练过程
"""

from my_parameters import get_my_parameters
from compatible_training_runner import run_compatible_training

def test_epochs_parameter():
    """测试epochs参数"""
    
    # 获取第一个配置
    configs = get_my_parameters()
    test_config = configs[0].copy()
    
    print("🧪 测试epochs参数传递")
    print("="*50)
    print(f"配置名称: {test_config['name']}")
    print(f"配置中的epochs: {test_config['epochs']}")
    print()
    
    # 创建一个快速测试配置
    test_config.update({
        'name': 'epochs测试',
        'n_steps': 24,
        'n_pred_steps': 6,
        'epochs': 5,  # 设置为5轮进行快速测试
        'batch_size': 16
    })
    
    print(f"测试配置epochs: {test_config['epochs']}")
    print("开始测试训练...")
    print()
    
    # 运行训练
    result = run_compatible_training(test_config)
    
    if result:
        print(f"\n✅ 测试完成!")
        print(f"训练ID: {result['training_id']}")
    else:
        print(f"\n❌ 测试失败!")

if __name__ == "__main__":
    test_epochs_parameter()
