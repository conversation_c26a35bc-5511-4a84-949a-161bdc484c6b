# 工具目录

本目录包含各种辅助工具和脚本：

## 版本控制工具
- `backup_project.py` - 项目备份工具
- `version_control_manager.py` - 版本控制管理器
- `init_git_version_control.py` - Git初始化工具
- `quick_restore.py` - 快速恢复工具
- `track_code_changes.py` - 代码变更追踪

## 训练工具
- `run_advanced_training.py` - 高级训练界面
- `start_version_control.py` - 版本控制启动器

## 使用方法
```bash
# 启动版本控制管理
python tools/version_control_manager.py

# 创建项目备份
python tools/backup_project.py
```
