"""
Quick Run Script for Runoff Forecasting
=======================================

This script provides a simplified way to run the forecasting pipeline
on your runoff data.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

def load_and_analyze_data():
    """Load and analyze the runoff data."""
    print("🔍 Loading and analyzing runoff data...")
    
    # Load data
    file_path = r'C:\Users\<USER>\Desktop\timemix\1964-2017dailyRunoff.csv'
    
    try:
        # Try to load as Excel first, then CSV
        try:
            data = pd.read_excel(file_path)
            print("✓ Loaded as Excel file")
        except:
            data = pd.read_csv(file_path)
            print("✓ Loaded as CSV file")
        
        # Parse dates
        data['DATA'] = pd.to_datetime(data['DATA'])
        data = data.sort_values('DATA').reset_index(drop=True)
        
        print(f"📊 Data Overview:")
        print(f"   - Shape: {data.shape}")
        print(f"   - Date range: {data['DATA'].min().date()} to {data['DATA'].max().date()}")
        print(f"   - Duration: {(data['DATA'].max() - data['DATA'].min()).days:,} days")
        
        # Statistics
        stats = data['runoff'].describe()
        print(f"📈 Runoff Statistics:")
        print(f"   - Mean: {stats['mean']:.2f}")
        print(f"   - Std: {stats['std']:.2f}")
        print(f"   - Min: {stats['min']:.2f}")
        print(f"   - Max: {stats['max']:.2f}")
        print(f"   - Missing values: {data['runoff'].isnull().sum()}")
        
        return data
        
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        return None

def prepare_sequences(data, lookback=30, forecast=7):
    """Prepare sequences for training."""
    print(f"🔧 Preparing sequences ({lookback}→{forecast} days)...")
    
    # Normalize data
    values = data['runoff'].values
    mean_val = np.mean(values)
    std_val = np.std(values)
    normalized = (values - mean_val) / std_val
    
    # Create sequences
    X, y = [], []
    for i in range(len(normalized) - lookback - forecast + 1):
        X.append(normalized[i:(i + lookback)])
        y.append(normalized[(i + lookback):(i + lookback + forecast)])
    
    X = np.array(X).reshape(-1, lookback, 1)
    y = np.array(y).reshape(-1, forecast, 1)
    
    print(f"✓ Created {len(X)} sequences")
    print(f"   - Input shape: {X.shape}")
    print(f"   - Target shape: {y.shape}")
    
    return X, y, mean_val, std_val

def split_data(X, y, train_ratio=0.7, val_ratio=0.2):
    """Split data into train/val/test sets."""
    n_samples = len(X)
    train_end = int(n_samples * train_ratio)
    val_end = int(n_samples * (train_ratio + val_ratio))
    
    train_X, train_y = X[:train_end], y[:train_end]
    val_X, val_y = X[train_end:val_end], y[train_end:val_end]
    test_X, test_y = X[val_end:], y[val_end:]
    
    print(f"📊 Data split:")
    print(f"   - Train: {len(train_X)} samples")
    print(f"   - Validation: {len(val_X)} samples")
    print(f"   - Test: {len(test_X)} samples")
    
    return (train_X, train_y), (val_X, val_y), (test_X, test_y)

def create_and_train_model(train_data, val_data, config):
    """Create and train the TimeMixer++ model."""
    print("🤖 Creating TimeMixer++ model...")
    
    try:
        # Import PyPOTS
        from pypots.forecasting.timemixer import TimeMixer
        print("✓ Using TimeMixer from PyPOTS")
        
        # Create model
        model = TimeMixer(
            n_steps=config['n_steps'],
            n_features=1,
            n_pred_steps=config['n_pred_steps'],
            n_pred_features=1,
            n_layers=config['n_layers'],
            d_model=config['d_model'],
            d_ffn=config['d_ffn'],
            top_k=config['top_k'],
            dropout=config['dropout'],
            batch_size=config['batch_size'],
            epochs=config['epochs'],
            patience=config['patience'],
            verbose=True
        )
        
        print("🚀 Starting training...")
        
        # Prepare data for PyPOTS
        train_X, train_y = train_data
        val_X, val_y = val_data
        
        train_dict = {'X': train_X}
        val_dict = {'X': val_X}
        
        # Train model
        model.fit(train_dict, val_dict)
        
        print("✅ Training completed!")
        return model
        
    except ImportError:
        print("❌ PyPOTS not installed. Please run: pip install pypots")
        return None
    except Exception as e:
        print(f"❌ Training error: {e}")
        return None

def evaluate_model(model, test_data, mean_val, std_val):
    """Evaluate the trained model."""
    print("📊 Evaluating model...")
    
    test_X, test_y = test_data
    
    # Make predictions
    test_dict = {'X': test_X}
    results = model.predict(test_dict)
    predictions = results['forecasting']
    
    # Denormalize
    test_y_denorm = test_y * std_val + mean_val
    pred_denorm = predictions * std_val + mean_val
    
    # Calculate metrics
    mse = np.mean((test_y_denorm - pred_denorm) ** 2)
    rmse = np.sqrt(mse)
    mae = np.mean(np.abs(test_y_denorm - pred_denorm))
    
    print(f"📈 Model Performance:")
    print(f"   - RMSE: {rmse:.4f}")
    print(f"   - MAE: {mae:.4f}")
    print(f"   - MSE: {mse:.4f}")
    
    return predictions, test_y, rmse, mae

def plot_results(predictions, actual, mean_val, std_val, save_path="predictions.png"):
    """Plot prediction results."""
    print("📊 Creating prediction plots...")
    
    # Denormalize
    actual_denorm = actual * std_val + mean_val
    pred_denorm = predictions * std_val + mean_val
    
    # Plot first 5 samples
    fig, axes = plt.subplots(5, 1, figsize=(12, 15))
    
    for i in range(5):
        if i < len(actual_denorm):
            axes[i].plot(actual_denorm[i].flatten(), 'b-', label='Actual', linewidth=2)
            axes[i].plot(pred_denorm[i].flatten(), 'r--', label='Predicted', linewidth=2)
            axes[i].set_title(f'Sample {i+1}: Runoff Prediction')
            axes[i].set_ylabel('Runoff')
            axes[i].legend()
            axes[i].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"✓ Plot saved as {save_path}")
    plt.show()

def main():
    """Main execution function."""
    print("🌊 Runoff Forecasting with TimeMixer++")
    print("=" * 50)
    
    # Configuration
    config = {
        'n_steps': 30,      # 30 days input
        'n_pred_steps': 7,  # 7 days forecast
        'n_layers': 2,
        'd_model': 128,
        'd_ffn': 256,
        'top_k': 3,
        'dropout': 0.1,
        'batch_size': 32,
        'epochs': 20,       # Reduced for quick testing
        'patience': 10
    }
    
    print(f"📋 Configuration: {config['n_steps']}→{config['n_pred_steps']} days, {config['epochs']} epochs")
    
    # Step 1: Load and analyze data
    data = load_and_analyze_data()
    if data is None:
        return
    
    # Step 2: Prepare sequences
    X, y, mean_val, std_val = prepare_sequences(
        data, 
        lookback=config['n_steps'], 
        forecast=config['n_pred_steps']
    )
    
    # Step 3: Split data
    train_data, val_data, test_data = split_data(X, y)
    
    # Step 4: Create and train model
    model = create_and_train_model(train_data, val_data, config)
    if model is None:
        print("❌ Model training failed. Please install PyPOTS and try again.")
        return
    
    # Step 5: Evaluate model
    predictions, actual, rmse, mae = evaluate_model(model, test_data, mean_val, std_val)
    
    # Step 6: Plot results
    plot_results(predictions, actual, mean_val, std_val)
    
    # Step 7: Save model
    try:
        model.save("runoff_model.pypots")
        print("✅ Model saved as runoff_model.pypots")
    except:
        print("⚠ Could not save model")
    
    print("\n🎉 Runoff forecasting completed!")
    print(f"📊 Final Performance: RMSE={rmse:.4f}, MAE={mae:.4f}")

if __name__ == "__main__":
    main()
