"""
自定义参数训练脚本
==================

在这里添加您设计的参数配置进行训练。
"""

from compatible_training_runner import run_compatible_training
import time

def get_custom_parameter_sets():
    """
    在这里定义您的自定义参数配置
    您可以添加任意数量的参数组合
    """
    
    # 🎯 在这里添加您设计的参数配置
    custom_configs = [
        # 配置1: 您的第一个参数组合
        {
            'name': '我的配置1',
            'n_steps': 48,           # 输入序列长度
            'n_pred_steps': 12,      # 预测序列长度
            'n_layers': 2,           # 模型层数
            'd_model': 64,           # 模型维度
            'd_ffn': 128,            # 前馈网络维度
            'epochs': 10,            # 训练轮次
            'learning_rate': 1e-3,   # 学习率
            'dropout': 0.1,          # Dropout率
            'device': 'cpu'          # 使用设备
        },
        
        # 配置2: 您的第二个参数组合
        {
            'name': '我的配置2',
            'n_steps': 72,
            'n_pred_steps': 18,
            'n_layers': 3,
            'd_model': 96,
            'd_ffn': 192,
            'epochs': 15,
            'learning_rate': 8e-4,
            'dropout': 0.15,
            'device': 'cpu'
        },
        
        # 配置3: 您的第三个参数组合
        {
            'name': '我的配置3',
            'n_steps': 96,
            'n_pred_steps': 24,
            'n_layers': 2,
            'd_model': 128,
            'd_ffn': 256,
            'epochs': 20,
            'learning_rate': 5e-4,
            'dropout': 0.2,
            'device': 'cpu'
        },
        
        # 🔧 添加更多配置...
        # 复制上面的格式，修改参数值即可
        
    ]
    
    return custom_configs

def add_gpu_configs():
    """
    如果您有GPU，可以添加GPU优化的配置
    """
    
    gpu_configs = [
        # GPU配置1: 利用GPU的大批次训练
        {
            'name': 'GPU大批次配置',
            'n_steps': 96,
            'n_pred_steps': 24,
            'n_layers': 3,
            'd_model': 256,
            'd_ffn': 512,
            'epochs': 25,
            'learning_rate': 3e-4,
            'dropout': 0.2,
            'device': 'cuda',        # 使用GPU
            'batch_size': 64         # GPU可以用更大的批次
        },
        
        # GPU配置2: 深层网络
        {
            'name': 'GPU深层网络',
            'n_steps': 144,
            'n_pred_steps': 36,
            'n_layers': 4,
            'd_model': 192,
            'd_ffn': 384,
            'epochs': 30,
            'learning_rate': 2e-4,
            'dropout': 0.25,
            'device': 'cuda',
            'batch_size': 32
        }
    ]
    
    return gpu_configs

def run_custom_training():
    """运行自定义参数训练"""
    
    print("🎯 自定义参数训练系统")
    print("="*50)
    
    # 获取自定义配置
    custom_configs = get_custom_parameter_sets()
    
    # 检查是否有GPU
    try:
        import torch
        gpu_available = torch.cuda.is_available()
        if gpu_available:
            print(f"✓ 检测到GPU: {torch.cuda.get_device_name(0)}")
            gpu_configs = add_gpu_configs()
            custom_configs.extend(gpu_configs)
        else:
            print("⚠ 未检测到GPU，使用CPU配置")
    except:
        print("⚠ 无法检测GPU，使用CPU配置")
        gpu_available = False
    
    print(f"\n📋 可用的自定义配置 (共{len(custom_configs)}个):")
    for i, config in enumerate(custom_configs, 1):
        print(f"{i}. {config['name']}")
        print(f"   序列: {config['n_steps']}→{config['n_pred_steps']}")
        print(f"   模型: {config['n_layers']}层, {config['d_model']}维度")
        print(f"   训练: {config['epochs']}轮, 学习率={config['learning_rate']}")
        print(f"   设备: {config['device']}")
        print()
    
    # 选择配置
    print("选择训练模式:")
    print("1. 运行单个配置")
    print("2. 运行所有配置")
    print("3. 运行CPU配置")
    if gpu_available:
        print("4. 运行GPU配置")
    
    choice = input(f"\n请选择 (1-{4 if gpu_available else 3}): ").strip()
    
    if choice == "1":
        # 运行单个配置
        while True:
            try:
                config_idx = int(input(f"选择配置编号 (1-{len(custom_configs)}): ")) - 1
                if 0 <= config_idx < len(custom_configs):
                    selected_config = custom_configs[config_idx]
                    break
                else:
                    print("无效编号，请重试")
            except ValueError:
                print("请输入数字")
        
        print(f"\n🚀 开始训练: {selected_config['name']}")
        result = run_compatible_training(selected_config)
        
        if result:
            print(f"\n✅ 训练完成!")
            print(f"训练ID: {result['training_id']}")
            print(f"MAE: {result['mae']:.6f}")
            print(f"R²: {result['r2']:.6f}")
    
    elif choice == "2":
        # 运行所有配置
        print(f"\n🚀 开始批量训练 (共{len(custom_configs)}个配置)")
        
        results = []
        for i, config in enumerate(custom_configs, 1):
            print(f"\n进度: {i}/{len(custom_configs)} - {config['name']}")
            
            try:
                result = run_compatible_training(config)
                if result:
                    results.append(result)
                    print(f"✓ {config['name']} 完成 - MAE: {result['mae']:.4f}")
                else:
                    print(f"✗ {config['name']} 失败")
            except Exception as e:
                print(f"✗ {config['name']} 异常: {e}")
            
            # 间隔时间
            if i < len(custom_configs):
                time.sleep(2)
        
        # 显示结果汇总
        if results:
            print(f"\n📊 批量训练结果汇总:")
            print(f"{'配置名称':<20} {'MAE':<12} {'R²':<12}")
            print("-" * 50)
            for result in results:
                config_name = next(c['name'] for c in custom_configs 
                                 if c.get('name', '') in result.get('training_id', ''))
                print(f"{config_name:<20} {result['mae']:<12.6f} {result['r2']:<12.6f}")
            
            best_result = min(results, key=lambda x: x['mae'])
            print(f"\n🏆 最佳结果: MAE = {best_result['mae']:.6f}")
    
    elif choice == "3":
        # 运行CPU配置
        cpu_configs = [c for c in custom_configs if c.get('device', 'cpu') == 'cpu']
        print(f"\n🚀 运行CPU配置 (共{len(cpu_configs)}个)")
        
        for config in cpu_configs:
            print(f"\n训练: {config['name']}")
            result = run_compatible_training(config)
            if result:
                print(f"✓ 完成 - MAE: {result['mae']:.4f}")
            time.sleep(1)
    
    elif choice == "4" and gpu_available:
        # 运行GPU配置
        gpu_configs = [c for c in custom_configs if c.get('device', 'cpu') == 'cuda']
        print(f"\n🎮 运行GPU配置 (共{len(gpu_configs)}个)")
        
        for config in gpu_configs:
            print(f"\n训练: {config['name']}")
            result = run_compatible_training(config)
            if result:
                print(f"✓ 完成 - MAE: {result['mae']:.4f}")
            time.sleep(1)
    
    else:
        print("无效选择")

def main():
    """主函数"""
    
    print("🎯 TimeMixer++ 自定义参数训练")
    print("="*50)
    print("在这个脚本中，您可以:")
    print("1. 在 get_custom_parameter_sets() 函数中添加您的参数配置")
    print("2. 运行单个或批量训练实验")
    print("3. 比较不同参数配置的效果")
    print()
    
    # 显示参数说明
    print("📖 可调参数说明:")
    print("- n_steps: 输入序列长度 (24, 48, 72, 96, 144...)")
    print("- n_pred_steps: 预测序列长度 (通常为n_steps的1/4到1/2)")
    print("- n_layers: 模型层数 (1, 2, 3, 4...)")
    print("- d_model: 模型维度 (32, 64, 96, 128, 256...)")
    print("- d_ffn: 前馈网络维度 (通常为d_model的2倍)")
    print("- epochs: 训练轮次 (5, 10, 15, 20, 30...)")
    print("- learning_rate: 学习率 (1e-4, 5e-4, 1e-3, 2e-3...)")
    print("- dropout: Dropout率 (0.1, 0.15, 0.2, 0.25...)")
    print("- device: 设备 ('cpu' 或 'cuda')")
    print()
    
    run_custom_training()

if __name__ == "__main__":
    main()
