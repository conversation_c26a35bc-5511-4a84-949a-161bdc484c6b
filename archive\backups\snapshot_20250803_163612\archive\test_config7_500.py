"""
测试配置7的500轮训练
==================

验证500轮训练参数是否正确传递
"""

from my_parameters import get_my_parameters
from compatible_training_runner import run_compatible_training

def test_config7_500():
    """测试配置7的500轮设置"""
    
    configs = get_my_parameters()
    config7 = configs[-1].copy()
    
    # 修改为快速测试
    config7.update({
        'epochs': 3,           # 测试用，只训练3轮
        'max_samples': 100,    # 限制样本数量
        'patience': 2          # 小耐心值
    })
    
    print("🧪 测试配置7的500轮参数传递")
    print("="*50)
    print(f"原始epochs设置: 500")
    print(f"测试epochs设置: {config7['epochs']}")
    print(f"原始patience设置: 30")
    print(f"测试patience设置: {config7['patience']}")
    print(f"n_pred_steps: {config7['n_pred_steps']}")
    print()
    
    # 运行训练
    result = run_compatible_training(config7)
    
    if result:
        print(f"\n✅ 配置7参数传递测试成功!")
        print(f"训练ID: {result['training_id']}")
        print("\n🎉 现在您可以运行完整的500轮训练了!")
        print("运行命令: python run_my_training.py")
        print("选择第7个配置")
        return True
    else:
        print(f"\n❌ 配置7参数传递测试失败!")
        return False

if __name__ == "__main__":
    test_config7_500()
