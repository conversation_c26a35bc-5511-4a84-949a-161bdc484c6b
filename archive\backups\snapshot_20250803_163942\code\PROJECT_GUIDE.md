# TimeMixer 径流预测项目

## 🎯 项目简介
基于TimeMixer模型的日径流量预测系统，使用68年历史数据(1950-2017)进行训练。

## 📁 核心文件说明

### 主要文件
- `run_my_training.py` - 主训练运行器，启动训练的入口
- `my_parameters.py` - 参数配置文件，包含所有训练配置
- `compatible_training_runner.py` - 训练核心逻辑，数据处理和模型训练

### 数据文件  
- `1964-2017dailyRunoff.csv` - 训练数据(24,836天径流数据)
- `timemixer_evaluation_results.csv` - 训练结果记录

### 配置文件
- `requirements.txt` - Python依赖包
- `README.md` - 项目说明文档

## 🚀 快速开始

1. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

2. **修改参数** (可选)
   编辑 `my_parameters.py` 中的配置参数

3. **开始训练**
   ```bash
   python run_my_training.py
   ```

4. **查看结果**
   训练完成后查看 `timemixer_evaluation_results.csv`

## 📊 数据预处理流程

1. **数据加载**: 读取CSV文件，解析日期索引
2. **缺失值处理**: 使用均值填充NaN值
3. **滑动窗口**: 创建输入-输出序列对
4. **数据划分**: 70%训练，15%验证，15%测试
5. **格式转换**: 转换为PyTorch张量格式

## 🔧 参数配置

当前支持7种预设配置，包括：
- 快速验证配置
- 季节性预测配置  
- 年度长期预测配置
- 极端事件预测配置
- 学习率实验配置
- 架构实验配置
- 全数据训练配置(500轮)

## 📈 训练监控

- 训练过程会显示实时进度
- 支持早停机制防止过拟合
- 自动保存最佳模型权重
- 生成详细的评估指标

## 🎯 评估指标

- MAE (平均绝对误差)
- RMSE (均方根误差)  
- NSE (Nash-Sutcliffe效率系数)
- R² (决定系数)

## 📦 归档文件

其他测试文件、文档和示例已移动到 `archive/` 目录中。
