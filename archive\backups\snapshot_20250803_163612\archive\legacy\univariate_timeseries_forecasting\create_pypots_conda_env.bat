@echo off
echo ========================================
echo Creating PyPOTS Conda Environment
echo ========================================
echo Based on PyPOTS official requirements
echo.

echo Step 1: Removing existing pypots environment (if exists)...
conda env remove -n pypots -y
echo.

echo Step 2: Creating new conda environment with Python 3.9...
conda create -n pypots python=3.9 -y
echo.

echo Step 3: Activating pypots environment...
call conda activate pypots
echo.

echo Step 4: Installing core scientific packages via conda...
conda install -c conda-forge numpy pandas matplotlib scikit-learn scipy -y
echo.

echo Step 5: Installing PyTorch (CPU version for stability)...
conda install pytorch torchvision torchaudio cpuonly -c pytorch -y
echo.

echo Step 6: Installing additional dependencies...
pip install packaging>=20.0
pip install setuptools>=50.0
pip install wheel
echo.

echo Step 7: Installing PyPOTS...
pip install pypots
echo.

echo Step 8: Installing additional useful packages...
pip install jupyter notebook ipykernel
pip install seaborn plotly
pip install openpyxl xlrd
echo.

echo Step 9: Testing PyPOTS installation...
python -c "import pypots; print('PyPOTS version:', pypots.__version__)"
python -c "from pypots.forecasting import TimeMixer; print('TimeMixer imported successfully')"
python -c "from pypots.optim import Adam; print('Adam optimizer imported successfully')"
echo.

echo ========================================
echo PyPOTS Environment Setup Complete!
echo ========================================
echo.
echo To use this environment:
echo 1. conda activate pypots
echo 2. python timemixer_based_on_test.py
echo.
echo Environment details:
echo - Name: pypots
echo - Python: 3.9
echo - PyTorch: CPU version
echo - PyPOTS: Latest version
echo.
pause
