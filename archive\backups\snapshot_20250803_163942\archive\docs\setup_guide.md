# TimeMixer++ PyPOTS Integration Setup Guide

本指南将帮助您设置和使用PyPOTS库中的TimeMixer++模型进行时间序列预测和插补任务。

## 目录
1. [环境要求](#环境要求)
2. [安装步骤](#安装步骤)
3. [快速开始](#快速开始)
4. [配置说明](#配置说明)
5. [使用示例](#使用示例)
6. [常见问题](#常见问题)

## 环境要求

### 系统要求
- Python 3.8 或更高版本
- 支持CUDA的GPU（可选，用于加速训练）

### 依赖库
- PyTorch >= 1.10.0
- NumPy >= 1.20.0
- Pandas >= 1.3.0
- PyPOTS >= 0.6.0

## 安装步骤

### 1. 创建虚拟环境（推荐）
```bash
# 使用conda
conda create -n timemixer python=3.9
conda activate timemixer

# 或使用venv
python -m venv timemixer_env
source timemixer_env/bin/activate  # Linux/Mac
# 或
timemixer_env\Scripts\activate  # Windows
```

### 2. 安装PyTorch
```bash
# CPU版本
pip install torch torchvision torchaudio

# GPU版本（CUDA 11.8）
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# GPU版本（CUDA 12.1）
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
```

### 3. 安装PyPOTS
```bash
# 从PyPI安装（推荐）
pip install pypots

# 或从源码安装最新版本
pip install git+https://github.com/WenjieDu/PyPOTS.git
```

### 4. 安装其他依赖
```bash
pip install numpy pandas matplotlib scikit-learn
```

### 5. 验证安装
```python
import pypots
import torch
print(f"PyPOTS version: {pypots.__version__}")
print(f"PyTorch version: {torch.__version__}")
print(f"CUDA available: {torch.cuda.is_available()}")
```

## 快速开始

### 1. 基本使用
```python
from timemixer_plus_plus_config import TimeMixerPPConfig, TimeMixerPPModelFactory
import numpy as np

# 创建配置
config = TimeMixerPPConfig(
    n_steps=96,      # 输入序列长度
    n_features=7,    # 特征数量
    d_model=256,     # 模型维度
    n_layers=2,      # 层数
    epochs=50        # 训练轮数
)

# 创建模型
model = TimeMixerPPModelFactory.create_imputation_model(config)

# 准备数据（示例）
train_data = {'X': np.random.randn(1000, 96, 7)}  # [样本数, 时间步, 特征数]
val_data = {'X': np.random.randn(200, 96, 7)}

# 训练模型
model.fit(train_data, val_data)

# 进行预测
test_data = {'X': np.random.randn(100, 96, 7)}
results = model.predict(test_data)
```

### 2. 运行示例
```bash
# 运行配置示例
python timemixer_plus_plus_config.py

# 运行完整示例
python timemixer_plus_plus_example.py
```

## 配置说明

### 主要参数

#### 数据维度参数
- `n_steps`: 输入时间序列的长度
- `n_features`: 时间序列的特征数量
- `n_pred_steps`: 预测步数（仅用于预测任务）
- `n_pred_features`: 预测特征数量（仅用于预测任务）

#### 模型架构参数
- `n_layers`: 模型层数（通常1-4层）
- `d_model`: 模型维度（128, 256, 512等）
- `d_ffn`: 前馈网络维度（通常是d_model的2倍）
- `top_k`: TimeMixer的top-k参数
- `dropout`: Dropout率（0.0-0.3）

#### 训练参数
- `batch_size`: 批次大小
- `epochs`: 训练轮数
- `patience`: 早停耐心值
- `learning_rate`: 学习率
- `device`: 设备选择（'cuda', 'cpu', None）

### 预设配置

```python
from timemixer_plus_plus_config import get_default_configs

configs = get_default_configs()

# 小型配置（快速测试）
small_config = configs['small']

# 中型配置（平衡性能）
medium_config = configs['medium']

# 大型配置（高性能）
large_config = configs['large']

# 预测配置
forecasting_config = configs['forecasting']
```

## 使用示例

### 时间序列插补
```python
# 1. 准备带有缺失值的数据
data_with_missing = {'X': your_data_with_nan}

# 2. 创建插补模型
config = TimeMixerPPConfig(n_steps=96, n_features=7)
model = TimeMixerPPModelFactory.create_imputation_model(config)

# 3. 训练和预测
model.fit(train_data, val_data)
results = model.predict(test_data)
imputed_data = results['imputation']
```

### 时间序列预测
```python
# 1. 创建预测配置
config = TimeMixerPPConfig(
    n_steps=96,        # 输入96个时间步
    n_features=7,      # 7个特征
    n_pred_steps=24,   # 预测未来24步
    n_pred_features=7  # 预测7个特征
)

# 2. 创建预测模型
model = TimeMixerPPModelFactory.create_forecasting_model(config)

# 3. 训练和预测
model.fit(train_data, val_data)
results = model.predict(test_data)
forecasts = results['forecasting']
```

## 常见问题

### Q1: 如何选择合适的模型参数？
**A:** 
- 对于小数据集：使用`small`配置
- 对于中等数据集：使用`medium`配置
- 对于大数据集：使用`large`配置
- 根据GPU内存调整`batch_size`

### Q2: 训练时出现内存不足错误？
**A:** 
- 减小`batch_size`
- 减小`d_model`和`d_ffn`
- 减少`n_layers`
- 使用梯度累积

### Q3: 模型训练很慢？
**A:** 
- 确保使用GPU：设置`device='cuda'`
- 增加`num_workers`进行并行数据加载
- 使用混合精度训练
- 减少`epochs`进行快速测试

### Q4: 如何处理不同长度的时间序列？
**A:** 
- 使用填充（padding）统一长度
- 使用滑动窗口切分长序列
- 调整`n_steps`参数匹配数据

### Q5: 如何保存和加载训练好的模型？
**A:** 
```python
# 保存模型
model.save('path/to/model.pypots')

# 加载模型
model.load('path/to/model.pypots')
```

### Q6: TimeMixer++与TimeMixer的区别？
**A:** 
- TimeMixer++是TimeMixer的改进版本
- 具有更好的性能和稳定性
- 支持更多的配置选项
- 如果TimeMixer++不可用，系统会自动回退到TimeMixer

## 进阶使用

### 自定义损失函数
```python
from pypots.nn.modules.loss import MSE, MAE

config.training_loss = MAE()
config.validation_metric = MSE()
```

### 使用多GPU训练
```python
config.device = ['cuda:0', 'cuda:1']  # 使用多个GPU
```

### 模型调优建议
1. 从小模型开始，逐步增加复杂度
2. 使用验证集进行超参数调优
3. 监控训练和验证损失，避免过拟合
4. 使用早停机制节省训练时间

## 参考资源

- [PyPOTS官方文档](https://docs.pypots.com/)
- [PyPOTS GitHub仓库](https://github.com/WenjieDu/PyPOTS)
- [TimeMixer++论文](https://arxiv.org/abs/2405.14616)
- [时间序列分析最佳实践](https://github.com/WenjieDu/Awesome_Imputation)

## 支持与反馈

如果您在使用过程中遇到问题，可以：
1. 查看PyPOTS官方文档
2. 在GitHub上提交Issue
3. 参与社区讨论

---

**注意**: 本指南基于PyPOTS库的最新版本编写，某些功能可能需要特定版本的支持。建议定期更新PyPOTS库以获得最新功能和修复。
