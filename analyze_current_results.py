"""
分析当前训练结果
================

分析所有训练结果，总结维度探索和R²优化的发现
"""

import pandas as pd
import numpy as np

def analyze_all_results():
    """分析所有训练结果"""
    print("📊 TimeMixer训练结果全面分析")
    print("="*50)
    
    # 读取结果
    df = pd.read_csv('timemixer_evaluation_results.csv')
    
    print(f"📈 总训练次数: {len(df)}")
    print(f"📅 训练时间跨度: {df['Timestamp'].min()} 到 {df['Timestamp'].max()}")
    
    # 基本统计
    print(f"\n📊 R²统计:")
    print(f"  最高R²: {df['R2'].max():.4f}")
    print(f"  最低R²: {df['R2'].min():.4f}")
    print(f"  平均R²: {df['R2'].mean():.4f}")
    print(f"  中位数R²: {df['R2'].median():.4f}")
    
    return df

def analyze_by_configuration_type(df):
    """按配置类型分析"""
    print(f"\n🔧 按配置类型分析")
    print("="*30)
    
    # 提取配置名称
    df['config_name'] = df['Parameters'].str.extract(r'name=([^|]+)')
    
    # 分类配置
    categories = {
        'LightGBM优化': df[df['config_name'].str.contains('LightGBM', na=False)],
        '稳定防过拟合': df[df['config_name'].str.contains('稳定防过拟合', na=False)],
        'PSO优化': df[df['config_name'].str.contains('PSO', na=False)],
        '水文学特征增强': df[df['config_name'].str.contains('水文学特征增强', na=False)],
        'R²冲刺尝试': df[df['config_name'].str.contains('R2冲刺|快速R2|精细微调|保守微调|精确调优', na=False)],
        '维度探索': df[df['config_name'].str.contains('维度探索', na=False)]
    }
    
    for category, data in categories.items():
        if not data.empty:
            best_r2 = data['R2'].max()
            avg_r2 = data['R2'].mean()
            count = len(data)
            print(f"\n{category} ({count}次训练):")
            print(f"  最佳R²: {best_r2:.4f}")
            print(f"  平均R²: {avg_r2:.4f}")
            
            # 显示最佳配置
            best_config = data.loc[data['R2'].idxmax()]
            print(f"  最佳配置: {best_config['config_name']}")

def analyze_dimension_impact(df):
    """分析维度对性能的影响"""
    print(f"\n🔍 维度影响分析")
    print("="*30)
    
    # 提取d_model参数
    df['d_model'] = df['Parameters'].str.extract(r'd_model=(\d+)').astype(float)
    df['d_ffn'] = df['Parameters'].str.extract(r'd_ffn=(\d+)').astype(float)
    
    # 过滤有维度信息的数据
    dimension_data = df.dropna(subset=['d_model'])
    
    if dimension_data.empty:
        print("❌ 没有找到维度信息")
        return
    
    # 按维度分组分析
    dimension_groups = {
        '极低维度 (≤128)': dimension_data[dimension_data['d_model'] <= 128],
        '低维度 (129-192)': dimension_data[(dimension_data['d_model'] > 128) & (dimension_data['d_model'] <= 192)],
        '中维度 (193-256)': dimension_data[(dimension_data['d_model'] > 192) & (dimension_data['d_model'] <= 256)],
        '高维度 (257-384)': dimension_data[(dimension_data['d_model'] > 256) & (dimension_data['d_model'] <= 384)],
        '超高维度 (>384)': dimension_data[dimension_data['d_model'] > 384]
    }
    
    print(f"📊 维度性能分析:")
    for group_name, group_data in dimension_groups.items():
        if not group_data.empty:
            best_r2 = group_data['R2'].max()
            avg_r2 = group_data['R2'].mean()
            count = len(group_data)
            print(f"\n{group_name} ({count}次训练):")
            print(f"  最佳R²: {best_r2:.4f}")
            print(f"  平均R²: {avg_r2:.4f}")
            
            # 显示最佳配置的维度
            if not group_data.empty:
                best_config = group_data.loc[group_data['R2'].idxmax()]
                print(f"  最佳配置: d_model={best_config['d_model']:.0f}, d_ffn={best_config['d_ffn']:.0f}")

def find_top_configurations(df):
    """找出最佳配置"""
    print(f"\n🏆 最佳配置排行榜")
    print("="*30)
    
    # 按R²排序
    top_configs = df.nlargest(10, 'R2')
    
    print(f"🥇 前10名配置:")
    for i, (_, config) in enumerate(top_configs.iterrows(), 1):
        config_name = config['Parameters'].split('|')[0].split('=')[1] if '=' in config['Parameters'] else 'Unknown'
        r2_value = config['R2']
        timestamp = config['Timestamp']
        
        medal = "🥇" if i == 1 else "🥈" if i == 2 else "🥉" if i == 3 else f"#{i:2d}"
        print(f"  {medal} R²={r2_value:.4f} - {config_name}")
        if i <= 3:
            print(f"      时间: {timestamp}")

def analyze_r2_08_attempts(df):
    """分析R²>0.8冲刺尝试"""
    print(f"\n🎯 R²>0.8冲刺分析")
    print("="*30)
    
    # 基线配置17
    baseline_r2 = 0.7301
    
    # R²冲刺相关配置
    r2_attempts = df[df['Parameters'].str.contains('R2冲刺|快速R2|精细微调|保守微调|精确调优|深度模型', na=False)]
    
    if r2_attempts.empty:
        print("❌ 没有找到R²冲刺尝试")
        return
    
    print(f"📊 R²冲刺统计:")
    print(f"  尝试次数: {len(r2_attempts)}")
    print(f"  最佳R²: {r2_attempts['R2'].max():.4f}")
    print(f"  平均R²: {r2_attempts['R2'].mean():.4f}")
    
    # 成功突破0.8的配置
    successful = r2_attempts[r2_attempts['R2'] > 0.8]
    if not successful.empty:
        print(f"  🎉 成功突破0.8: {len(successful)} 次")
        for _, config in successful.iterrows():
            config_name = config['Parameters'].split('|')[0].split('=')[1]
            print(f"    ✅ {config_name}: R²={config['R2']:.4f}")
    else:
        print(f"  ❌ 未成功突破0.8")
    
    # 超越基线的配置
    improved = r2_attempts[r2_attempts['R2'] > baseline_r2]
    if not improved.empty:
        print(f"  📈 超越基线 ({baseline_r2:.4f}): {len(improved)} 次")
        best_improvement = improved.loc[improved['R2'].idxmax()]
        config_name = best_improvement['Parameters'].split('|')[0].split('=')[1]
        improvement = best_improvement['R2'] - baseline_r2
        print(f"    💪 最佳改进: {config_name}")
        print(f"       R²={best_improvement['R2']:.4f} (+{improvement:.4f})")
    else:
        print(f"  📊 无配置超越基线")

def generate_insights(df):
    """生成洞察和建议"""
    print(f"\n💡 关键洞察和建议")
    print("="*30)
    
    # 最佳配置
    best_config = df.loc[df['R2'].idxmax()]
    best_r2 = best_config['R2']
    
    print(f"🎯 核心发现:")
    print(f"  1. 最佳R²: {best_r2:.4f}")
    
    if best_r2 > 0.8:
        print(f"     ✅ 已成功突破R²=0.8目标!")
    elif best_r2 > 0.75:
        print(f"     🔥 非常接近R²=0.8目标")
    elif best_r2 > 0.7:
        print(f"     👍 达到了良好的预测精度")
    else:
        print(f"     📊 仍有改进空间")
    
    # 水文学特征的重要性
    hydro_configs = df[df['Parameters'].str.contains('水文学特征增强', na=False)]
    if not hydro_configs.empty:
        hydro_best = hydro_configs['R2'].max()
        print(f"  2. 水文学特征工程效果: R²={hydro_best:.4f}")
        print(f"     💧 证明了领域知识的重要性")
    
    # 维度探索结果
    dimension_configs = df[df['Parameters'].str.contains('维度探索', na=False)]
    if not dimension_configs.empty:
        dim_best = dimension_configs['R2'].max()
        print(f"  3. 维度探索结果: 最佳R²={dim_best:.4f}")
        if dim_best < best_r2:
            print(f"     📉 维度调整未带来显著改善")
        else:
            print(f"     📈 维度优化有效果")
    
    print(f"\n🚀 下一步建议:")
    if best_r2 < 0.8:
        gap = 0.8 - best_r2
        print(f"  • 距离R²=0.8还差: {gap:.4f}")
        if gap < 0.05:
            print(f"  • 建议尝试集成学习")
            print(f"  • 考虑更多水文学特征")
        else:
            print(f"  • 可能需要不同的模型架构")
            print(f"  • 考虑数据增强技术")
    
    print(f"  • 当前最佳配置已经很优秀")
    print(f"  • 可以用于实际水文预测应用")

def main():
    """主函数"""
    print("🎯 TimeMixer项目训练结果分析")
    print("="*50)
    
    # 分析所有结果
    df = analyze_all_results()
    
    # 按配置类型分析
    analyze_by_configuration_type(df)
    
    # 维度影响分析
    analyze_dimension_impact(df)
    
    # 最佳配置排行
    find_top_configurations(df)
    
    # R²冲刺分析
    analyze_r2_08_attempts(df)
    
    # 生成洞察
    generate_insights(df)
    
    print(f"\n🎉 分析完成!")
    print(f"📊 详细结果请查看: timemixer_evaluation_results.csv")

if __name__ == "__main__":
    main()
