"""
精细微调R²>0.8
===============

基于配置17的成功，进行精细微调，争取突破R²=0.8
"""

import os
import sys
from datetime import datetime

def create_fine_tuned_configs():
    """创建精细微调的配置"""
    print("🔧 创建精细微调配置")
    print("="*40)
    
    # 基于配置17的成功参数
    base_config = {
        'n_steps': 90,
        'n_pred_steps': 5,
        'n_layers': 3,
        'd_model': 256,
        'd_ffn': 512,
        'top_k': 10,
        'moving_avg': 7,
        'downsampling_window': 3,
        'downsampling_layers': 2,
        'dropout': 0.2,
        'use_norm': True,
        'epochs': 200,
        'batch_size': 32,
        'learning_rate': 3e-4,
        'patience': 30,
        'device': 'cuda',
        'num_workers': 0
    }
    
    # 微调方案1: 学习率优化
    config_v1 = base_config.copy()
    config_v1.update({
        'name': '精细微调v1_学习率优化',
        'learning_rate': 2.5e-4,  # 降低学习率
        'epochs': 250,            # 增加训练时间
        'patience': 40            # 增加耐心值
    })
    
    # 微调方案2: 模型容量优化 (推荐)
    config_v2 = base_config.copy()
    config_v2.update({
        'name': '精细微调v2_模型容量优化',
        'd_model': 288,           # 适度增加模型维度
        'd_ffn': 576,             # 相应增加FFN
        'dropout': 0.18,          # 降低正则化
        'learning_rate': 2.5e-4,  # 稍微降低学习率
        'epochs': 250,            # 增加训练时间
        'patience': 40            # 增加耐心值
    })
    
    # 微调方案3: 序列优化
    config_v3 = base_config.copy()
    config_v3.update({
        'name': '精细微调v3_序列优化',
        'n_steps': 105,           # 增加输入序列
        'top_k': 12,              # 增加top_k
        'batch_size': 28,         # 减小batch_size
        'learning_rate': 2.5e-4,  # 降低学习率
        'epochs': 250,            # 增加训练时间
        'patience': 40            # 增加耐心值
    })
    
    # 微调方案4: 综合优化
    config_v4 = base_config.copy()
    config_v4.update({
        'name': '精细微调v4_综合优化',
        'n_steps': 96,            # 小幅增加序列长度
        'd_model': 272,           # 小幅增加模型维度
        'd_ffn': 544,             # 相应增加FFN
        'top_k': 11,              # 小幅增加top_k
        'dropout': 0.19,          # 小幅降低dropout
        'learning_rate': 2.7e-4,  # 小幅降低学习率
        'epochs': 275,            # 增加训练时间
        'batch_size': 30,         # 小幅减小batch_size
        'patience': 45            # 增加耐心值
    })
    
    # 微调方案5: 激进优化
    config_v5 = base_config.copy()
    config_v5.update({
        'name': '精细微调v5_激进优化',
        'n_steps': 120,           # 大幅增加序列长度
        'd_model': 320,           # 大幅增加模型维度
        'd_ffn': 640,             # 大幅增加FFN
        'top_k': 15,              # 大幅增加top_k
        'dropout': 0.15,          # 大幅降低dropout
        'learning_rate': 2e-4,    # 大幅降低学习率
        'epochs': 300,            # 大幅增加训练时间
        'batch_size': 24,         # 减小batch_size
        'patience': 50            # 大幅增加耐心值
    })
    
    configs = [config_v1, config_v2, config_v3, config_v4, config_v5]
    
    print(f"✅ 创建了 {len(configs)} 个精细微调配置:")
    for i, config in enumerate(configs, 1):
        print(f"\n{i}. {config['name']}")
        changes = []
        for key, value in config.items():
            if key in base_config and base_config[key] != value and key != 'name':
                if isinstance(value, float) and value < 1:
                    changes.append(f"{key}: {base_config[key]:.2e} → {value:.2e}")
                else:
                    changes.append(f"{key}: {base_config[key]} → {value}")
        print(f"   变更: {', '.join(changes[:3])}")  # 只显示前3个变更
    
    return configs

def run_single_fine_tune(config):
    """运行单个精细微调配置"""
    print(f"\n🚀 运行精细微调: {config['name']}")
    print("="*50)
    
    # 显示配置详情
    print(f"🔧 配置详情:")
    print(f"  序列: {config['n_steps']} → {config['n_pred_steps']}")
    print(f"  模型: {config['n_layers']}层, d_model={config['d_model']}, FFN={config['d_ffn']}")
    print(f"  训练: lr={config['learning_rate']:.2e}, epochs={config['epochs']}, batch={config['batch_size']}")
    print(f"  正则: dropout={config['dropout']:.3f}, top_k={config['top_k']}")
    
    try:
        import enhanced_compatible_training_runner
        
        print(f"\n🔥 开始训练...")
        start_time = datetime.now()
        
        result = enhanced_compatible_training_runner.run_compatible_training(config)
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        if result:
            print(f"\n✅ 训练完成! 用时: {duration:.1f}秒")
            
            # 读取最新结果
            try:
                import pandas as pd
                df = pd.read_csv('timemixer_evaluation_results.csv')
                latest_result = df.iloc[-1]
                r2_value = latest_result['R2']
                
                print(f"\n🎯 训练结果:")
                print(f"  R² Score: {r2_value:.4f}")
                print(f"  MAE: {latest_result['MAE']:.2f}")
                print(f"  RMSE: {latest_result['RMSE']:.2f}")
                
                # 与基线对比
                baseline_r2 = 0.7301
                improvement = r2_value - baseline_r2
                
                print(f"\n📈 与配置17对比:")
                print(f"  基线R²: {baseline_r2:.4f}")
                print(f"  当前R²: {r2_value:.4f}")
                print(f"  提升: {improvement:+.4f} ({improvement/baseline_r2*100:+.1f}%)")
                
                # 成功评估
                if r2_value > 0.8:
                    print(f"\n🎉 恭喜！成功突破R²=0.8！")
                    print(f"🏆 这是一个重大突破！")
                    return True, r2_value
                elif r2_value > 0.78:
                    print(f"\n🔥 非常接近！R²>0.78，继续努力！")
                    return False, r2_value
                elif r2_value > 0.75:
                    print(f"\n👍 很好！R²>0.75，有希望达到目标")
                    return False, r2_value
                elif r2_value > baseline_r2:
                    print(f"\n📈 有进步！超过了基线配置")
                    return False, r2_value
                else:
                    print(f"\n📊 结果记录，需要调整策略")
                    return False, r2_value
                
            except Exception as e:
                print(f"📊 结果读取失败: {e}")
                return False, None
        else:
            print(f"❌ 训练失败")
            return False, None
            
    except Exception as e:
        print(f"❌ 训练过程出错: {e}")
        return False, None

def run_sequential_fine_tuning():
    """顺序运行精细微调"""
    print("🎯 顺序精细微调策略")
    print("="*50)
    
    configs = create_fine_tuned_configs()
    
    print(f"\n💡 策略: 按成功概率顺序尝试")
    print(f"1. 模型容量优化 (推荐)")
    print(f"2. 综合优化 (平衡)")
    print(f"3. 学习率优化 (保守)")
    print(f"4. 序列优化 (创新)")
    print(f"5. 激进优化 (最后尝试)")
    
    # 按推荐顺序排列
    ordered_configs = [
        configs[1],  # v2: 模型容量优化
        configs[3],  # v4: 综合优化
        configs[0],  # v1: 学习率优化
        configs[2],  # v3: 序列优化
        configs[4]   # v5: 激进优化
    ]
    
    results = []
    
    for i, config in enumerate(ordered_configs, 1):
        print(f"\n{'='*20} 尝试 {i}/5 {'='*20}")
        
        success, r2_value = run_single_fine_tune(config)
        
        results.append({
            'config': config['name'],
            'success': success,
            'r2': r2_value
        })
        
        if success:
            print(f"\n🎉 成功！停止后续尝试")
            break
        elif r2_value and r2_value > 0.78:
            print(f"\n🔥 非常接近目标！继续下一个尝试")
        elif i < len(ordered_configs):
            print(f"\n⏭️ 继续下一个配置...")
        
        print(f"{'='*50}")
    
    # 总结结果
    print(f"\n📊 精细微调总结:")
    print("="*30)
    
    successful = [r for r in results if r['success']]
    best_result = max([r for r in results if r['r2']], key=lambda x: x['r2']) if any(r['r2'] for r in results) else None
    
    print(f"总尝试次数: {len(results)}")
    print(f"成功突破0.8: {len(successful)} 次")
    
    if successful:
        print(f"🎉 成功配置: {successful[0]['config']}")
        print(f"🏆 达到R²: {successful[0]['r2']:.4f}")
    elif best_result:
        print(f"💪 最佳尝试: {best_result['config']}")
        print(f"📊 达到R²: {best_result['r2']:.4f}")
        gap = 0.8 - best_result['r2']
        print(f"🎯 距离目标: {gap:.4f}")
    
    return results

def main():
    """主函数"""
    print("🎯 精细微调R²>0.8程序")
    print("="*50)
    
    print("📊 当前状况:")
    print("  • 基线配置17: R²=0.7301")
    print("  • 目标: R²>0.8")
    print("  • 策略: 精细微调而非大幅改动")
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] == 'single':
            # 运行单个推荐配置
            configs = create_fine_tuned_configs()
            recommended_config = configs[1]  # v2: 模型容量优化
            run_single_fine_tune(recommended_config)
        elif sys.argv[1] == 'all':
            # 运行所有配置
            run_sequential_fine_tuning()
        else:
            print("❌ 无效参数，使用 'single' 或 'all'")
    else:
        # 默认运行推荐的单个配置
        print("\n🚀 运行推荐配置 (模型容量优化)")
        configs = create_fine_tuned_configs()
        recommended_config = configs[1]  # v2: 模型容量优化
        
        success, r2_value = run_single_fine_tune(recommended_config)
        
        if not success and r2_value and r2_value < 0.78:
            print(f"\n🤔 推荐配置未达到预期")
            choice = input("是否尝试所有精细微调配置？(y/n): ").strip().lower()
            if choice == 'y':
                print(f"\n🔄 开始完整精细微调流程...")
                run_sequential_fine_tuning()

if __name__ == "__main__":
    main()
